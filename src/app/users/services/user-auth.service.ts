import assert from 'assert'
import { Injectable } from '@nestjs/common'
import { User } from '../../../app/users/entities/user.entity.js'
import { TokenContent } from '../../../modules/auth/middleware/auth.middleware.js'
import { RedisClient } from '../../../modules/redis/redis.client.js'
import { GetOrCreateUserUseCase } from '../use-cases/get-or-create-user/get-or-create-user.use-case.js'
import { AuthContent } from '../../../modules/auth/auth.context.js'
import { GetOrCreateUserCommandBuilder } from '../use-cases/get-or-create-user/get-or-create-user.command.builder.js'

@Injectable()
export class UserAuthService {
  constructor (
    private readonly redisClient: RedisClient,
    private readonly getOrCreateUserUseCase: GetOrCreateUserUseCase
  ) { }

  async findOneByAuthPayload (token: TokenContent): Promise<AuthContent> {
    const cacheKey = `auth:${token.sub}`

    const cachedUser = await this.redisClient.getCachedValue(cacheKey)

    if (cachedUser != null) {
      return JSON.parse(cachedUser) as AuthContent
    }

    const user = await this.fetchOrCreateUser(token)

    const response: AuthContent = {
      uuid: user.uuid,
      zitadelSub: user.zitadelSub,
      impersonateUserUuid: null,
      azureEntraId: user.azureEntraId,
      impersonateAzureEntraId: null,
      azureEntraUpn: user.azureEntraUpn,
      impersonateAzureEntraUpn: null,
      selectedCustomerId: null
    }

    await this.redisClient.putCachedValue(cacheKey, JSON.stringify(response))

    return response
  }

  private async fetchOrCreateUser (token: TokenContent): Promise<User> {
    assert(token.externalId != null)
    assert(token.externalUpn != null)
    assert(token.roles != null)

    const command = new GetOrCreateUserCommandBuilder()
      .withId(token.sub)
      .withEmail(token.email)
      .withFirstName(token.firstName)
      .withLastName(token.lastName)
      .withAzureEntraId(token.externalId)
      .withAzureEntraUpn(token.externalUpn)
      .withRoles(token.roles)
      .build()

    return await this.getOrCreateUserUseCase.getOrCreateUser(command)
  }
}
