import { Injectable } from '@nestjs/common'
import { DataSource } from 'typeorm'
import { User } from '../entities/user.entity.js'
import { NotFoundError } from '../../../modules/exceptions/generic/not-found.error.js'

@Injectable()
export class UserImpersonateService {
  constructor (
    private readonly dataSource: DataSource
    // Replacing the datasource with the following throws maximum call stack size exceeded error
    // @InjectRepository(User)
    // private readonly userRepository: Repository<User>,
  ) {}

  public async checkImpersonateUserAccess (
    userUuid: string,
    azureEntraId?: string,
    azureEntraUpn?: string
  ): Promise<User> {
    if (userUuid === '') throw new NotFoundError(`error.auth.impersonated_user_not_found`)

    const impersonateUser = await this.dataSource.getRepository(User).findOneBy({
      uuid: userUuid,
      azureEntraId,
      azureEntraUpn
    })

    if (impersonateUser == null) throw new NotFoundError(`error.auth.impersonated_user_not_found`)

    if (impersonateUser.isInternalUser()) {
      throw new NotFoundError(`error.auth.impersonated_user_not_found`)
    }

    return impersonateUser
  }
}
