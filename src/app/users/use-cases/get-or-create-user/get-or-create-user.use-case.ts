import assert from 'assert'
import { Injectable } from '@nestjs/common'
import { Any, DataSource, Repository, TypeORMError } from 'typeorm'
import { InjectRepository, transaction } from '@wisemen/nestjs-typeorm'
import { User } from '../../entities/user.entity.js'
import { UserEntityBuilder } from '../../tests/user-entity.builder.js'
import { DomainEventEmitter } from '../../../../modules/domain-events/domain-event-emitter.js'
import { UnauthorizedError } from '../../../../modules/exceptions/generic/unauthorized.error.js'
import { Role } from '../../../roles/entities/role.entity.js'
import { UserRole } from '../../../roles/entities/user-role.entity.js'
import { UserCache } from '../../cache/user-cache.js'
import { GetOrCreateUserCommand } from './get-or-create-user.command.js'
import { UserCreatedEvent } from './user-created.event.js'

@Injectable()
export class GetOrCreateUserUseCase {
  constructor (
    private dataSource: DataSource,
    private eventEmitter: DomainEventEmitter,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Role)
    private readonly roleRepository: Repository<Role>,
    @InjectRepository(UserRole)
    private readonly userRoleRepository: Repository<UserRole>,
    private readonly userCache: UserCache
  ) {}

  async getOrCreateUser (command: GetOrCreateUserCommand): Promise<User> {
    const user = await this.userRepository.findOne({
      where: [
        {
          zitadelSub: command.id
        },
        {
          azureEntraId: command.azureEntraId
        }
      ],
      relations: {
        userRoles: {
          role: true
        }
      }
    })

    if (user != null) {
      return await this.updateUser(user, command)
    }

    return await this.createUser(command)
  }

  private async createUser (command: GetOrCreateUserCommand): Promise<User> {
    const user = new UserEntityBuilder()
      .withEmail(command.email)
      .withFirstName(command.firstName)
      .withLastName(command.lastName)
      .withZitadelSub(command.id)
      .withAzureEntraId(command.azureEntraId)
      .withAzureEntraUpn(command.azureEntraUpn)
      .build()

    try {
      await transaction(this.dataSource, async () => {
        await this.userRepository.insert(user)
        await this.setUserRoles(user, command.roles)
        await this.eventEmitter.emitOne(new UserCreatedEvent(user.uuid))
      })
    } catch (e) {
      if (this.userHasBeenCreatedSimultaneously(e)) {
        return await this.refetchUser(command)
      } else {
        throw e
      }
    }

    return user
  }

  private async updateUser (user: User, command: GetOrCreateUserCommand): Promise<User> {
    assert(user.userRoles !== undefined)

    let isUpdate = false

    const existingRoleIdentifiers = user.userRoles
      .map(userRole => userRole.role!.identifier)
      .filter(identifier => identifier != null)

    if (
      existingRoleIdentifiers.length !== command.roles.length
      || !existingRoleIdentifiers.every(role => command.roles.includes(role))
    ) {
      isUpdate = true
    }

    if (isUpdate) {
      await transaction(this.dataSource, async () => {
        await this.userRepository.update(user.uuid, {
          azureEntraId: user.azureEntraId,
          azureEntraUpn: user.azureEntraUpn
        })
        await this.setUserRoles(user, command.roles)
      })
    }

    return user
  }

  private async setUserRoles (user: User, roleIdentifiers: string[]): Promise<void> {
    assert(user.userRoles !== undefined)

    const roles = await this.roleRepository.find({
      select: {
        uuid: true
      },
      where: {
        identifier: Any(roleIdentifiers)
      }
    })
    const roleUuids = roles.map(role => role.uuid)
    const existingRoleUuids = user.userRoles.map(userRole => userRole.roleUuid)

    const rolesToAdd = roleUuids.filter(roleUuid => !existingRoleUuids.includes(roleUuid))
      .map(roleUuid => this.userRoleRepository.create({
        userUuid: user.uuid,
        roleUuid
      }))

    const rolesToRemove = existingRoleUuids.filter(roleUuid => !roleUuids.includes(roleUuid))

    if (rolesToAdd.length > 0) {
      await this.userRoleRepository.insert(rolesToAdd)
    }
    if (rolesToRemove.length > 0) {
      await this.userRoleRepository.delete({
        userUuid: user.uuid,
        roleUuid: Any(rolesToRemove)
      })
    }

    await this.userCache.setUserRoles(user.uuid, roleUuids)
  }

  private async refetchUser (command: GetOrCreateUserCommand): Promise<User> {
    const user = await this.userRepository.findOne({
      where: {
        zitadelSub: command.id
      }
    })

    if (user != null) {
      return user
    } else {
      throw new UnauthorizedError()
    }
  }

  private userHasBeenCreatedSimultaneously (e: unknown): e is TypeORMError {
    return e instanceof TypeORMError
      && e.name === 'QueryFailedError'
      && e.message.startsWith('duplicate key')
  }
}
