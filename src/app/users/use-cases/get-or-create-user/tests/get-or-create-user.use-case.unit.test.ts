import { afterEach, before, describe, it } from 'node:test'
import Sinon, { createStubInstance, SinonStubbedInstance } from 'sinon'
import { expect } from 'expect'
import { Repository } from 'typeorm'
import { randEmail } from '@ngneat/falso'
import { GetOrCreateUserUseCase } from '../get-or-create-user.use-case.js'
import { stubDataSource } from '../../../../../../test/utils/stub-datasource.js'
import { UserEntityBuilder } from '../../../tests/user-entity.builder.js'
import { GetOrCreateUserCommandBuilder } from '../get-or-create-user.command.builder.js'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { DomainEventEmitter } from '../../../../../modules/domain-events/domain-event-emitter.js'
import { User } from '../../../entities/user.entity.js'
import { Role } from '../../../../roles/entities/role.entity.js'
import { UserRole } from '../../../../roles/entities/user-role.entity.js'
import { UserCache } from '../../../cache/user-cache.js'
import { UserCreatedEvent } from '../user-created.event.js'
import { RoleEntityBuilder } from '../../../../roles/entities/role.entity-builder.js'

describe('Get or create user use case unit tests', () => {
  let useCase: GetOrCreateUserUseCase

  let eventEmitter: SinonStubbedInstance<DomainEventEmitter>
  let userRepository: SinonStubbedInstance<Repository<User>>
  let roleRepository: SinonStubbedInstance<Repository<Role>>
  let userRoleRepository: SinonStubbedInstance<Repository<UserRole>>

  before(() => {
    TestBench.setupUnitTest()

    eventEmitter = createStubInstance(DomainEventEmitter)
    userRepository = createStubInstance<Repository<User>>(
      Repository<User>
    )
    roleRepository = createStubInstance<Repository<Role>>(
      Repository<Role>
    )
    userRoleRepository = createStubInstance<Repository<UserRole>>(
      Repository<UserRole>
    )
    const userCache = createStubInstance(UserCache)

    useCase = new GetOrCreateUserUseCase(
      stubDataSource(),
      eventEmitter,
      userRepository,
      roleRepository,
      userRoleRepository,
      userCache
    )

    mockMethods()
  })

  afterEach(() => {
    Sinon.resetHistory()
    mockMethods()
  })

  function mockMethods () {
    userRepository.findOne.resolves(null)
    roleRepository.find.resolves([])
    userRoleRepository.findOne.resolves(null)
  }

  describe('create user', () => {
    it('creates a new user when not existing', async () => {
      const command = new GetOrCreateUserCommandBuilder().build()

      await useCase.getOrCreateUser(command)

      expect(userRepository.insert.called).toBe(true)
      expect(userRoleRepository.insert.called).toBe(false)
      expect(userRoleRepository.delete.called).toBe(false)
    })

    it('creates a new user with roles', async () => {
      const command = new GetOrCreateUserCommandBuilder()
        .withRoles(['role1'])
        .build()
      roleRepository.find.resolves([
        new RoleEntityBuilder().withIdentifier('role1').build()
      ])

      await useCase.getOrCreateUser(command)

      expect(userRepository.insert.called).toBe(true)
      expect(userRoleRepository.insert.called).toBe(true)
      expect(userRoleRepository.delete.called).toBe(false)
    })

    it('emits an event when a user is created', async () => {
      const command = new GetOrCreateUserCommandBuilder().build()

      const user = await useCase.getOrCreateUser(command)

      expect(eventEmitter).toHaveEmitted(new UserCreatedEvent(user.uuid))
    })
  })

  describe('update user', () => {
    it('does not create or update a user when one already exists and nothing changed', async () => {
      const user = new UserEntityBuilder().build()
      userRepository.findOne.resolves(user)

      const command = new GetOrCreateUserCommandBuilder()
        .withAzureEntraUpn(user.azureEntraUpn)
        .withEmail(user.email)
        .withFirstName(user.firstName)
        .withLastName(user.lastName)
        .build()

      await useCase.getOrCreateUser(command)

      expect(userRepository.insert.called).toBe(false)
      expect(userRepository.update.called).toBe(false)
    })

    it('updates the user when email differ', async () => {
      const user = new UserEntityBuilder().build()
      userRepository.findOne.resolves(user)
      roleRepository.find.resolves([
        new RoleEntityBuilder().withIdentifier('role2').build()
      ])

      const command = new GetOrCreateUserCommandBuilder()
        .withAzureEntraUpn(user.azureEntraUpn)
        .withEmail(randEmail())
        .withFirstName(user.firstName)
        .withLastName(user.lastName)
        .build()

      await useCase.getOrCreateUser(command)

      expect(userRepository.insert.called).toBe(false)
      expect(userRepository.update.called).toBe(true)
    })
  })
})
