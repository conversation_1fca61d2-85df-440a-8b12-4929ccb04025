import type { User } from '../entities/user.entity.js'

export class TypesenseUser {
  id: string
  firstName: string
  lastName: string
  email: string
  isInternal: boolean
  upn: string

  constructor (user: User) {
    return {
      id: user.uuid,
      upn: user.azureEntraUpn,
      firstName: user.firstName ?? '',
      lastName: user.lastName ?? '',
      email: user.email,
      isInternal: user.isInternalUser()
    }
  }
}
