import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { Repository } from 'typeorm'
import { PgBossScheduler } from '@wisemen/pgboss-nestjs-job'
import { RedisClient } from '../../../modules/redis/redis.client.js'
import { User } from '../entities/user.entity.js'
import { SyncUserRolesJob } from '../use-cases/sync-user-roles/sync-user-roles.job.js'

const USER_ROLE_CACHE = 'user-role-cache'

@Injectable()
export class UserCache {
  constructor (
    private readonly client: RedisClient,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private readonly jobScheduler: PgBossScheduler
  ) { }

  private cacheKey (userUuid: string): string {
    return `${USER_ROLE_CACHE}.${userUuid}`
  }

  async setUserRoles (userUuid: string, roleUuids: string[]): Promise<void> {
    await this.client.putCachedValue(this.cacheKey(userUuid), JSON.stringify(roleUuids), 900)
  }

  async getUserRoles (userUuid: string): Promise<string[]> {
    const cachedRoleUuids = await this.getCachedRoles(userUuid)

    if (cachedRoleUuids != null) {
      return cachedRoleUuids
    }

    await this.jobScheduler.scheduleJob(new SyncUserRolesJob(userUuid))

    const user = await this.userRepository.findOne({
      where: { uuid: userUuid },
      relations: { userRoles: true }
    })

    const roleUuids = user?.userRoles?.map(userRole => userRole.roleUuid) ?? []

    return roleUuids
  }

  private async getCachedRoles (userUuid: string): Promise<string[] | null> {
    const result = await this.client.getCachedValue(this.cacheKey(userUuid))

    if (result != null) {
      return JSON.parse(String(result)) as string[]
    } else {
      return null
    }
  }
}
