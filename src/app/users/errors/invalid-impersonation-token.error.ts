import { ApiErrorCode } from '../../../modules/exceptions/api-errors/api-error-code.decorator.js'
import { BadRequestApiError } from '../../../modules/exceptions/api-errors/bad-request.api-error.js'
import { translateCurrent } from '../../../modules/localization/helpers/translate.helper.js'

export class InvalidImpersonationTokenError extends BadRequestApiError {
  @ApiErrorCode('invalid_impersonation_token')
  readonly code = 'invalid_impersonation_token'

  readonly meta: never

  constructor () {
    super(translateCurrent('error.user.invalid_impersonation_token'))
  }
}
