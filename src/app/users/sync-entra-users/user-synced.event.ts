import { OneOfMeta } from '@wisemen/one-of'
import { ApiProperty } from '@nestjs/swagger'
import { RegisterDomainEvent } from '../../../modules/domain-events/register-domain-event.decorator.js'
import { DomainEventType } from '../../../modules/domain-events/domain-event-type.js'
import { DomainEventLog } from '../../../modules/domain-event-log/domain-event-log.entity.js'
import { UserEvent } from '../events/user.event.js'

@OneOfMeta(DomainEventLog, DomainEventType.USER_UPDATED)
export class UserUpdatedEventContent {
  @ApiProperty({ type: 'string', format: 'uuid' })
  readonly userUuid: string

  constructor (userUuid: string) {
    this.userUuid = userUuid
  }
}

@RegisterDomainEvent(DomainEventType.USER_UPDATED, 1)
export class UserUpdatedEvent extends UserEvent<UserUpdatedEventContent> {
  constructor (userUuid: string) {
    super({
      userUuid,
      content: new UserUpdatedEventContent(userUuid)
    })
  }
}
