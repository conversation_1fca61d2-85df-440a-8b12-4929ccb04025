import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { GetEntraUsersModule } from '../../../modules/microsoft-graph/use-cases/fetch-entra-users/fetch-entra-users.module.js'
import { User } from '../entities/user.entity.js'
import { DomainEventEmitterModule } from '../../../modules/domain-events/domain-event-emitter.module.js'
import { SyncEntraUsersUseCase } from './sync-entra-users.use-case.js'
import { SyncEntraUsersController } from './sync-entra-users.controller.js'

@Module({
  imports: [
    GetEntraUsersModule,
    TypeOrmModule.forFeature([User]),
    DomainEventEmitterModule
  ],
  controllers: [SyncEntraUsersController],
  providers: [SyncEntraUsersUseCase],
  exports: [SyncEntraUsersUseCase]
})
export class SyncEntraUsersCronjobModule { }
