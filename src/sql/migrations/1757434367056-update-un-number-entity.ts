import { MigrationInterface, QueryRunner } from 'typeorm'

export class UpdateUnNumberEntity1757434367056 implements MigrationInterface {
  name = 'UpdateUnNumberEntity1757434367056'

  public async up (queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "un_number" ALTER COLUMN "key" SET NOT NULL`)
    await queryRunner.query(`ALTER TABLE "un_number" DROP CONSTRAINT "PK_ce43c3a3f722ca98233c165b9af"`)
    await queryRunner.query(`ALTER TABLE "un_number" ADD CONSTRAINT "PK_359d3991f5cde077a4fe9a50066" PRIMARY KEY ("key")`)
  }

  public async down (queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "un_number" DROP CONSTRAINT "PK_359d3991f5cde077a4fe9a50066"`)
    await queryRunner.query(`ALTER TABLE "un_number" ADD CONSTRAINT "PK_ce43c3a3f722ca98233c165b9af" PRIMARY KEY ("number", "packing_group")`)
    await queryRunner.query(`ALTER TABLE "un_number" ALTER COLUMN "key" DROP NOT NULL`)
  }
}
