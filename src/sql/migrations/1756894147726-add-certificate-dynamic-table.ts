import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddCertificateDynamicTable1756894147726 implements MigrationInterface {
  name = 'AddCertificateDynamicTable1756894147726'

  public async up (queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TYPE "public"."dynamic_table_name_enum" RENAME TO "dynamic_table_name_enum_old"`)
    await queryRunner.query(`CREATE TYPE "public"."dynamic_table_name_enum" AS ENUM('certificate', 'contract-line', 'contract-overview', 'draft-invoice', 'form-pick-up-request-contract-line', 'invoice', 'pick-up-request', 'waste-inquiry', 'guidance-letter')`)
    await queryRunner.query(`ALTER TABLE "dynamic_table" ALTER COLUMN "name" TYPE "public"."dynamic_table_name_enum" USING "name"::"text"::"public"."dynamic_table_name_enum"`)
    await queryRunner.query(`DROP TYPE "public"."dynamic_table_name_enum_old"`)
  }

  public async down (queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE TYPE "public"."dynamic_table_name_enum_old" AS ENUM('contract-line', 'contract-overview', 'draft-invoice', 'form-pick-up-request-contract-line', 'guidance-letter', 'invoice', 'pick-up-request', 'waste-inquiry')`)
    await queryRunner.query(`ALTER TABLE "dynamic_table" ALTER COLUMN "name" TYPE "public"."dynamic_table_name_enum_old" USING "name"::"text"::"public"."dynamic_table_name_enum_old"`)
    await queryRunner.query(`DROP TYPE "public"."dynamic_table_name_enum"`)
    await queryRunner.query(`ALTER TYPE "public"."dynamic_table_name_enum_old" RENAME TO "dynamic_table_name_enum"`)
  }
}
