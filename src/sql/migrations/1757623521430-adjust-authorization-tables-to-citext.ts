import { MigrationInterface, QueryRunner } from 'typeorm'

export class AdjustAuthorizationTablesToCitext1757623521430 implements MigrationInterface {
  name = 'AdjustAuthorizationTablesToCitext1757623521430'

  public async up (queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE EXTENSION IF NOT EXISTS citext`)

    await queryRunner.query(`
      ALTER TABLE "authorization"."user_customer"
      ALTER COLUMN "user_id" TYPE citext USING "user_id"::citext
    `)

    await queryRunner.query(`
      ALTER TABLE "authorization"."user_waste_producer"
      ALTER COLUMN "user_id" TYPE citext USING "user_id"::citext
    `)
  }

  public async down (queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE "authorization"."user_customer"
      ALTER COLUMN "user_id" TYPE varchar USING "user_id"::varchar
    `)

    await queryRunner.query(`
      ALTER TABLE "authorization"."user_waste_producer"
      ALTER COLUMN "user_id" TYPE varchar USING "user_id"::varchar
    `)
  }
}
