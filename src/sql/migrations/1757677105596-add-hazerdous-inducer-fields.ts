import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddHazerdousInducerFields1757677105596 implements MigrationInterface {
  name = 'AddHazerdousInducerFields1757677105596'

  public async up (queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "waste_inquiry" ADD "hazard_inducer1" character varying`)
    await queryRunner.query(`ALTER TABLE "waste_inquiry" ADD "hazard_inducer2" character varying`)
    await queryRunner.query(`ALTER TABLE "waste_inquiry" ADD "hazard_inducer3" character varying`)
  }

  public async down (queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "waste_inquiry" DROP COLUMN "hazard_inducer3"`)
    await queryRunner.query(`ALTER TABLE "waste_inquiry" DROP COLUMN "hazard_inducer2"`)
    await queryRunner.query(`ALTER TABLE "waste_inquiry" DROP COLUMN "hazard_inducer1"`)
  }
}
