import { MigrationInterface, QueryRunner } from 'typeorm'

export class UnNumberDescriptionNullable1757933001886 implements MigrationInterface {
  name = 'UnNumberDescriptionNullable1757933001886'

  public async up (queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "un_number" ALTER COLUMN "description" DROP NOT NULL`)
  }

  public async down (queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "un_number" ALTER COLUMN "description" SET NOT NULL`)
  }
}
