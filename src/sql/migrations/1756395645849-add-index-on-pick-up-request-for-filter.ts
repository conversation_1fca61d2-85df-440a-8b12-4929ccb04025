import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddIndexOnPickUpRequestForFilter1756395645849 implements MigrationInterface {
  name = 'AddIndexOnPickUpRequestForFilter1756395645849'

  public async up (queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."domain_event_log_created_at_idx"`)
    await queryRunner.query(`CREATE INDEX "IDX_be34beef4817d78c87927a0e8e" ON "pick_up_request" ("transport_mode") `)
    await queryRunner.query(`CREATE INDEX "IDX_21344124a7f85ce5f1c43e0de7" ON "pick_up_request" ("is_transport_by_indaver") `)
    await queryRunner.query(`CREATE INDEX "IDX_6466efdc7f2ae687ead18e08c4" ON "pick_up_request" ("start_date") `)
    await queryRunner.query(`CREATE INDEX "IDX_836bc7ef471fb32a3cd32985e0" ON "pick_up_request" ("end_date") `)
    await queryRunner.query(`CREATE INDEX "IDX_83c1ea544d3af1e9ef2926f53b" ON "pick_up_request" ("submitted_on") `)
    await queryRunner.query(`CREATE INDEX "IDX_1333cb0f72bd058989c0449d33" ON "pick_up_request" ("request_number") `)
  }

  public async down (queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."IDX_1333cb0f72bd058989c0449d33"`)
    await queryRunner.query(`DROP INDEX "public"."IDX_83c1ea544d3af1e9ef2926f53b"`)
    await queryRunner.query(`DROP INDEX "public"."IDX_836bc7ef471fb32a3cd32985e0"`)
    await queryRunner.query(`DROP INDEX "public"."IDX_6466efdc7f2ae687ead18e08c4"`)
    await queryRunner.query(`DROP INDEX "public"."IDX_21344124a7f85ce5f1c43e0de7"`)
    await queryRunner.query(`DROP INDEX "public"."IDX_be34beef4817d78c87927a0e8e"`)
    await queryRunner.query(`CREATE INDEX "domain_event_log_created_at_idx" ON "domain_event_log" ("created_at") `)
  }
}
