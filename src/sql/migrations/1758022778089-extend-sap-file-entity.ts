import { MigrationInterface, QueryRunner } from 'typeorm'

export class ExtendSapFileEntity1758022778089 implements MigrationInterface {
  name = 'ExtendSapFileEntity1758022778089'

  public async up (queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "sap_file" ADD "entity_part" character varying NOT NULL DEFAULT 'additional'`)
    await queryRunner.query(`ALTER TABLE "sap_file" ADD "mime_type" character varying NOT NULL DEFAULT 'application/pdf'`)
    await queryRunner.query(`ALTER TABLE "sap_file" ADD "order" smallint`)
    await queryRunner.query(`ALTER TABLE "file" ALTER COLUMN "mime_type" SET NOT NULL`)

    await queryRunner.query(`ALTER TABLE "sap_file" ALTER COLUMN "entity_part" DROP DEFAULT`)
    await queryRunner.query(`ALTER TABLE "sap_file" ALTER COLUMN "mime_type" DROP DEFAULT`)
  }

  public async down (queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "file" ALTER COLUMN "mime_type" DROP NOT NULL`)
    await queryRunner.query(`ALTER TABLE "sap_file" DROP COLUMN "order"`)
    await queryRunner.query(`ALTER TABLE "sap_file" DROP COLUMN "mime_type"`)
    await queryRunner.query(`ALTER TABLE "sap_file" DROP COLUMN "entity_part"`)
  }
}
