import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddSapFileEntity1756994984594 implements MigrationInterface {
  name = 'AddSapFileEntity1756994984594'

  public async up (queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE TABLE "sap_file" ("uuid" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP(3) NOT NULL DEFAULT now(), "updated_at" TIMESTAMP(3) NOT NULL DEFAULT now(), "sap_object_id" character varying NOT NULL, "sap_file_id" character varying NOT NULL, "entity_type" character varying NOT NULL, "name" character varying NOT NULL, CONSTRAINT "UQ_49aefa514c84d133a323e2862ac" UNIQUE ("sap_object_id", "sap_file_id"), CONSTRAINT "PK_38e3e08af6c70506463c969e0b6" PRIMARY KEY ("uuid"))`)
    await queryRunner.query(`CREATE INDEX "IDX_39b142e24159335f2c61b277b6" ON "sap_file" ("sap_object_id") `)
    await queryRunner.query(`CREATE INDEX "IDX_d1891304abe334d5fb3a3710ee" ON "sap_file" ("sap_file_id") `)
  }

  public async down (queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."IDX_d1891304abe334d5fb3a3710ee"`)
    await queryRunner.query(`DROP INDEX "public"."IDX_39b142e24159335f2c61b277b6"`)
    await queryRunner.query(`DROP TABLE "sap_file"`)
  }
}
