import { MigrationInterface, QueryRunner } from 'typeorm'

export class AzureEntraFieldsNotNullableUsersTable1756376550354 implements MigrationInterface {
  name = 'AzureEntraFieldsNotNullableUsersTable1756376550354'

  public async up (queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`UPDATE "dynamic_table_view" SET "created_by_user_uuid" = NULL WHERE "created_by_user_uuid" IN (SELECT "uuid" FROM "user" WHERE "azure_entra_id" IS NULL OR "azure_entra_upn" IS NULL)`)
    await queryRunner.query(`DELETE FROM "waste_inquiry" WHERE "created_by_user_uuid" IN (SELECT "uuid" FROM "user" WHERE "azure_entra_id" IS NULL OR "azure_entra_upn" IS NULL)`)
    await queryRunner.query(`DELETE FROM "pick_up_request" WHERE "created_by_user_uuid" IN (SELECT "uuid" FROM "user" WHERE "azure_entra_id" IS NULL OR "azure_entra_upn" IS NULL)`)
    await queryRunner.query(`DELETE FROM "ui_preferences" WHERE "user_uuid" IN (SELECT "uuid" FROM "user" WHERE "azure_entra_id" IS NULL OR "azure_entra_upn" IS NULL)`)
    await queryRunner.query(`DELETE FROM "notification_preferences" WHERE "user_uuid" IN (SELECT "uuid" FROM "user" WHERE "azure_entra_id" IS NULL OR "azure_entra_upn" IS NULL)`)
    await queryRunner.query(`DELETE FROM "notification_preferences_preset" WHERE "user_uuid" IN (SELECT "uuid" FROM "user" WHERE "azure_entra_id" IS NULL OR "azure_entra_upn" IS NULL)`)
    await queryRunner.query(`DELETE FROM "user_role" WHERE "user_uuid" IN (SELECT "uuid" FROM "user" WHERE "azure_entra_id" IS NULL OR "azure_entra_upn" IS NULL)`)
    await queryRunner.query(`DELETE FROM "user" WHERE "azure_entra_id" IS NULL OR "azure_entra_upn" IS NULL`)
    await queryRunner.query(`ALTER TABLE "user" ALTER COLUMN "azure_entra_id" SET NOT NULL`)
    await queryRunner.query(`ALTER TABLE "user" ALTER COLUMN "azure_entra_upn" SET NOT NULL`)
  }

  public async down (queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user" ALTER COLUMN "azure_entra_upn" DROP NOT NULL`)
    await queryRunner.query(`ALTER TABLE "user" ALTER COLUMN "azure_entra_id" DROP NOT NULL`)
  }
}
