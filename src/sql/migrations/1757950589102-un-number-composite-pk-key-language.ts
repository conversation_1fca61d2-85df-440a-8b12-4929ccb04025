import { MigrationInterface, QueryRunner } from 'typeorm'

export class UnNumberCompositePkKeyLanguage1757950589102 implements MigrationInterface {
  name = 'UnNumberCompositePkKeyLanguage1757950589102'

  public async up (queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "un_number" DROP CONSTRAINT "UQ_dcbd069b7163e4345022c1bc97a"`)
    await queryRunner.query(`ALTER TABLE "un_number" DROP CONSTRAINT "PK_359d3991f5cde077a4fe9a50066"`)
    await queryRunner.query(`ALTER TABLE "un_number" ADD CONSTRAINT "PK_dcbd069b7163e4345022c1bc97a" PRIMARY KEY ("key", "language")`)
  }

  public async down (queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "un_number" DROP CONSTRAINT "PK_dcbd069b7163e4345022c1bc97a"`)
    await queryRunner.query(`ALTER TABLE "un_number" ADD CONSTRAINT "PK_359d3991f5cde077a4fe9a50066" PRIMARY KEY ("key")`)
    await queryRunner.query(`ALTER TABLE "un_number" ADD CONSTRAINT "UQ_dcbd069b7163e4345022c1bc97a" UNIQUE ("key", "language")`)
  }
}
