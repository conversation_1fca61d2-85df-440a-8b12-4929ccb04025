import { MigrationInterface, QueryRunner } from 'typeorm'

export class CreatedByUserNullableDynamicTableViewTable1756375984253 implements MigrationInterface {
  name = 'CreatedByUserNullableDynamicTableViewTable1756375984253'

  public async up (queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "dynamic_table_view" DROP CONSTRAINT "FK_e112b13947527bc83261b36d5bb"`)
    await queryRunner.query(`ALTER TABLE "dynamic_table_view" DROP CONSTRAINT "UQ_f0e3d89edc88c8af3b5e8f24583"`)
    await queryRunner.query(`ALTER TABLE "dynamic_table_view" ALTER COLUMN "created_by_user_uuid" DROP NOT NULL`)
    await queryRunner.query(`ALTER TABLE "dynamic_table_view" ADD CONSTRAINT "UQ_f0e3d89edc88c8af3b5e8f24583" UNIQUE ("created_by_user_uuid", "name", "dynamic_table_uuid")`)
    await queryRunner.query(`ALTER TABLE "dynamic_table_view" ADD CONSTRAINT "FK_e112b13947527bc83261b36d5bb" FOREIGN KEY ("created_by_user_uuid") REFERENCES "user"("uuid") ON DELETE NO ACTION ON UPDATE NO ACTION`)
  }

  public async down (queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "dynamic_table_view" DROP CONSTRAINT "FK_e112b13947527bc83261b36d5bb"`)
    await queryRunner.query(`ALTER TABLE "dynamic_table_view" DROP CONSTRAINT "UQ_f0e3d89edc88c8af3b5e8f24583"`)
    await queryRunner.query(`ALTER TABLE "dynamic_table_view" ALTER COLUMN "created_by_user_uuid" SET NOT NULL`)
    await queryRunner.query(`ALTER TABLE "dynamic_table_view" ADD CONSTRAINT "UQ_f0e3d89edc88c8af3b5e8f24583" UNIQUE ("name", "dynamic_table_uuid", "created_by_user_uuid")`)
    await queryRunner.query(`ALTER TABLE "dynamic_table_view" ADD CONSTRAINT "FK_e112b13947527bc83261b36d5bb" FOREIGN KEY ("created_by_user_uuid") REFERENCES "user"("uuid") ON DELETE NO ACTION ON UPDATE NO ACTION`)
  }
}
