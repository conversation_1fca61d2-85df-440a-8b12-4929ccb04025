import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddIsWicConfirmedOnPickUpRequest1756807428520 implements MigrationInterface {
  name = 'AddIsWicConfirmedOnPickUpRequest1756807428520'

  public async up (queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "pick_up_request" ADD "is_wic_confirmed" boolean`)
  }

  public async down (queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "pick_up_request" DROP COLUMN "is_wic_confirmed"`)
  }
}
