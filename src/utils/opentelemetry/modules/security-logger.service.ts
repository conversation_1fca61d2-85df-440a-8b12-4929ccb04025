import { Injectable } from '@nestjs/common'
import { OpenTelemetryLoggerService, LogContext } from './logger.service.js'

export enum SecurityEventType {
  AUTHENTICATION_SUCCESS = 'authentication_success',
  AUTHENTICATION_FAILED = 'authentication_failed',
  AUTHORIZATION_DENIED = 'authorization_denied',
  IMPERSONATION_STARTED = 'impersonation_started',
  IMPERSONATION_ENDED = 'impersonation_ended',
  IMPERSONATION_FAILED = 'impersonation_failed',
  FILE_ACCESS = 'file_access',
  FILE_UPLOAD = 'file_upload',
  FILE_DOWNLOAD = 'file_download',
  SENSITIVE_DATA_ACCESS = 'sensitive_data_access',
  INVALID_TOKEN = 'invalid_token',
  MALFORMED_REQUEST = 'malformed_request',
  WEBSOCKET_AUTH_FAILED = 'websocket_auth_failed',
  SUSPICIOUS_ACTIVITY = 'suspicious_activity'
}

export interface SecurityLogEvent {
  eventType: SecurityEventType
  userUuid?: string
  azureEntraUpn?: string
  impersonatedUserUuid?: string
  ipAddress?: string
  userAgent?: string
  endpoint?: string
  resourceType?: string
  resourceId?: string
  action?: string
  success: boolean
  errorMessage?: string
  additionalData?: Record<string, unknown>
}

@Injectable()
export class SecurityLoggerService {
  constructor(
    private readonly logger: OpenTelemetryLoggerService
  ) { }

  logSecurityEvent(event: SecurityLogEvent): void {
    const logLevel = event.success ? 'info' : 'warn'

    const logData = {
      context: LogContext.SECURITY,
      body: {
        event: event.eventType,
        success: event.success,
        userUuid: event.userUuid,
        azureEntraUpn: event.azureEntraUpn,
        impersonatedUserUuid: event.impersonatedUserUuid,
        endpoint: event.endpoint,
        resourceType: event.resourceType,
        resourceId: event.resourceId,
        action: event.action,
        errorMessage: event.errorMessage,
        timestamp: new Date().toISOString()
      },
      attributes: {
        ipAddress: event.ipAddress,
        userAgent: event.userAgent,
        ...event.additionalData
      }
    }

    if (logLevel === 'warn') {
      this.logger.warn(logData)
    } else {
      this.logger.info(logData)
    }
  }

  logAuthenticationSuccess(userUuid: string, azureEntraUpn: string, ipAddress?: string, userAgent?: string): void {
    this.logSecurityEvent({
      eventType: SecurityEventType.AUTHENTICATION_SUCCESS,
      userUuid,
      azureEntraUpn,
      ipAddress,
      userAgent,
      success: true
    })
  }

  logAuthenticationFailure(errorMessage: string, ipAddress?: string, userAgent?: string): void {
    this.logSecurityEvent({
      eventType: SecurityEventType.AUTHENTICATION_FAILED,
      errorMessage,
      ipAddress,
      userAgent,
      success: false
    })
  }

  logAuthorizationDenied(userUuid: string, endpoint: string, requiredPermissions: string[], ipAddress?: string): void {
    this.logSecurityEvent({
      eventType: SecurityEventType.AUTHORIZATION_DENIED,
      userUuid,
      endpoint,
      ipAddress,
      success: false,
      additionalData: {
        requiredPermissions
      }
    })
  }

  logImpersonationStarted(userUuid: string, impersonatedUserUuid: string, ipAddress?: string): void {
    this.logSecurityEvent({
      eventType: SecurityEventType.IMPERSONATION_STARTED,
      userUuid,
      impersonatedUserUuid,
      ipAddress,
      success: true
    })
  }

  logImpersonationFailed(userUuid: string, targetUserUuid: string, errorMessage: string, ipAddress?: string): void {
    this.logSecurityEvent({
      eventType: SecurityEventType.IMPERSONATION_FAILED,
      userUuid,
      errorMessage,
      ipAddress,
      success: false,
      additionalData: {
        targetUserUuid
      }
    })
  }

  logFileAccess(userUuid: string, fileId: string, action: 'upload' | 'download' | 'access', ipAddress?: string): void {
    this.logSecurityEvent({
      eventType: action === 'upload' ? SecurityEventType.FILE_UPLOAD :
        action === 'download' ? SecurityEventType.FILE_DOWNLOAD :
          SecurityEventType.FILE_ACCESS,
      userUuid,
      resourceType: 'file',
      resourceId: fileId,
      action,
      ipAddress,
      success: true
    })
  }

  logSensitiveDataAccess(userUuid: string, resourceType: string, resourceId: string, action: string, ipAddress?: string): void {
    this.logSecurityEvent({
      eventType: SecurityEventType.SENSITIVE_DATA_ACCESS,
      userUuid,
      resourceType,
      resourceId,
      action,
      ipAddress,
      success: true
    })
  }

  logInvalidToken(errorMessage: string, ipAddress?: string, userAgent?: string): void {
    this.logSecurityEvent({
      eventType: SecurityEventType.INVALID_TOKEN,
      errorMessage,
      ipAddress,
      userAgent,
      success: false
    })
  }

  logWebSocketAuthFailure(ipAddress?: string, userAgent?: string): void {
    this.logSecurityEvent({
      eventType: SecurityEventType.WEBSOCKET_AUTH_FAILED,
      ipAddress,
      userAgent,
      success: false
    })
  }
}
