import { Global, Module } from '@nestjs/common'
import { ConfigModule } from '@nestjs/config'
import { OpenTelemetryLoggerService } from './logger.service.js'
import { SecurityLoggerService } from './security-logger.service.js'

@Global()
@Module({
  imports: [ConfigModule],
  providers: [OpenTelemetryLoggerService, SecurityLoggerService],
  exports: [OpenTelemetryLoggerService, SecurityLoggerService]
})
export class LoggerModule { }
