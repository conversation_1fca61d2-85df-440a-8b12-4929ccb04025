import { ApiProperty } from '@nestjs/swagger'
import { FilterQuery } from '@wisemen/pagination'
import { IsSameOrAfterDateString, IsSameOrBeforeDateString } from '@wisemen/validators'
import { IsDateString } from 'class-validator'

export class <PERSON><PERSON><PERSON><PERSON> extends FilterQuery {
  @ApiProperty({ type: String, format: 'date' })
  @IsDateString()
  @IsSameOrBeforeDateString((range: DateRange) => range.to)
  from: string

  @ApiProperty({ type: String, format: 'date' })
  @IsDateString()
  @IsSameOrAfterDateString((range: DateRange) => range.from)
  to: string
}
