import { ReadStream } from 'fs'
import axios from 'axios'
import { Response } from 'express'
import { InternalServerApiError } from '../../modules/exceptions/api-errors/internal-server.api-error.js'
import { MimeType } from '../../modules/files/enums/mime-type.enum.js'

export class FileStreamUtil {
  static async pipeFileResponse (
    response: Response,
    url: string
  ): Promise<void> {
    if (!this.isWhitelisted(url)) {
      throw new InternalServerApiError('URL not allowed')
    }

    try {
      const fileResponse = await axios.get<ReadStream>(url, {
        responseType: 'stream'
      })
      response.setHeader('Content-Type', fileResponse.headers['content-type'] as string || 'application/octet-stream')
      response.setHeader('Content-Disposition', fileResponse.headers['content-disposition'] as string || `attachment; filename="unknown"`)

      fileResponse.data.pipe(response)
    } catch (_e) {
      throw new InternalServerApiError('File currently not available')
    }
  }

  static pipeBufferResponse (
    response: Response,
    buffer: Buffer,
    filename: string,
    contentType: MimeType
  ): void {
    try {
      response.setHeader('Content-Type', contentType)
      response.setHeader(
        'Content-Disposition',
        `attachment; filename="${filename}"`
      )
      response.send(buffer)
    } catch (_e) {
      throw new InternalServerApiError('File could not be sent')
    }
  }

  private static isWhitelisted (url: string): boolean {
    try {
      const parsed = new URL(url)
      const domain = 'sap.indaver.com'
      return parsed.hostname === domain || parsed.hostname.endsWith(`.${domain}`)
    } catch {
      return false
    }
  }
}
