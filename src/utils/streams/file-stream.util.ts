import { ReadStream } from 'fs'
import axios from 'axios'
import { Response } from 'express'
import { InternalServerApiError } from '../../modules/exceptions/api-errors/internal-server.api-error.js'

export class FileStreamUtil {
  private static readonly ALLOWED_DOMAINS = [
    'vhiinotdotc01.sap.indaver.com'
  ]

  static async pipeFileResponse (
    response: Response,
    url: string
  ): Promise<void> {
    if (!this.isWhitelisted(url)) {
      throw new InternalServerApiError('URL not allowed')
    }

    try {
      const fileResponse = await axios.get<ReadStream>(url, {
        responseType: 'stream'
      })
      response.setHeader('Content-Type', fileResponse.headers['content-type'] as string || 'application/octet-stream')
      response.setHeader('Content-Disposition', fileResponse.headers['content-disposition'] as string || `attachment; filename="unknown"`)

      fileResponse.data.pipe(response)
    } catch (_e) {
      throw new InternalServerApiError('File currently not available')
    }
  }

  private static isWhitelisted (url: string): boolean {
    try {
      const parsed = new URL(url)
      return this.ALLOWED_DOMAINS.includes(parsed.hostname)
    } catch {
      return false
    }
  }
}
