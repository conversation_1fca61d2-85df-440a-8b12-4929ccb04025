import { ApiErrorCode } from '../../exceptions/api-errors/api-error-code.decorator.js'
import { ConflictApiError } from '../../exceptions/api-errors/conflict.api-error.js'
import { translateCurrent } from '../../localization/helpers/translate.helper.js'

export class DuplicateFileUuidError extends ConflictApiError {
  @ApiErrorCode('duplicate_file_uuid')
  readonly code = 'duplicate_file_uuid'

  readonly meta: never

  constructor () {
    const detail = translateCurrent('error.files.duplicate_file_uuid')
    super(detail)
  }
}
