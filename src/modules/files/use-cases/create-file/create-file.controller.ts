import { <PERSON>, Post, Body, Req } from '@nestjs/common'
import { ApiTags, ApiOAuth2, ApiCreatedResponse } from '@nestjs/swagger'
import { Request } from 'express'
import { Permissions } from '../../../../modules/permission/permission.decorator.js'
import { Permission } from '../../../../modules/permission/permission.enum.js'
import { CreateFileCommand } from './create-file.command.js'
import { CreateFileUseCase } from './create-file.use-case.js'
import { CreateFileResponse } from './create-file.response.js'
import { SecurityLoggerService } from '../../../../../utils/opentelemetry/modules/security-logger.service.js'
import { AuthContext } from '../../../../auth/auth.context.js'

@ApiTags('File')
@Controller('files')
@ApiOAuth2([])
export class CreateFileController {
  constructor(
    private readonly useCase: CreateFileUseCase,
    private readonly securityLogger: SecurityLoggerService,
    private readonly authContext: AuthContext
  ) { }

  @Post()
  @Permissions(
    Permission.WASTE_INQUIRY_MANAGE,
    Permission.WASTE_INQUIRY_READ,
    Permission.PICK_UP_REQUEST_MANAGE,
    Permission.PICK_UP_REQUEST_READ,
    Permission.WEEKLY_PLANNING_REQUEST_MANAGE,
    Permission.WEEKLY_PLANNING_REQUEST_READ
  )
  @ApiCreatedResponse({
    type: CreateFileResponse
  })
  async createFile(
    @Body() command: CreateFileCommand,
    @Req() req: Request
  ): Promise<CreateFileResponse> {
    const result = await this.useCase.execute(command)

    // Log file creation
    const userUuid = this.authContext.getUserUuidOrFail()
    this.securityLogger.logFileAccess(
      userUuid,
      result.uuid,
      'upload',
      req.ip
    )

    return result
  }
}
