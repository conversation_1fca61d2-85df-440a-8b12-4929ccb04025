import { ApiProperty } from '@nestjs/swagger'
import { MimeType } from '../enums/mime-type.enum.js'
import { File } from '../entities/file.entity.js'

export class FileResponse {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({ type: String })
  name: string

  @ApiProperty({ type: String, enum: MimeType, enumName: 'MimeType' })
  mimeType: string

  @ApiProperty({ type: String, format: 'url' })
  url: string

  constructor (file: File) {
    this.uuid = file.uuid
    this.name = file.name
    this.mimeType = file.mimeType
    this.url = file.url
  }
}
