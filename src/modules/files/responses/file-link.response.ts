import assert from 'assert'
import { ApiProperty } from '@nestjs/swagger'
import { MimeType } from '../enums/mime-type.enum.js'
import { FileLink } from '../entities/file-link.entity.js'
import { ExternalFile } from '../types/external-file.type.js'

export class FileLinkResponse {
  @ApiProperty({ type: String, format: 'uuid', nullable: true, description: 'Null when file is from Sharepoint' })
  uuid: string | null

  @ApiProperty({ type: String })
  name: string

  @ApiProperty({ type: String, enum: MimeType, enumName: 'MimeType', nullable: true })
  mimeType: string | null

  @ApiProperty({ type: String, format: 'url', nullable: true })
  url: string | null

  @ApiProperty({ type: Number, nullable: true })
  order: number | null

  constructor (fileLink: FileLink | ExternalFile) {
    if (fileLink instanceof FileLink) {
      assert(fileLink.file !== undefined)

      this.uuid = fileLink.file.uuid
      this.name = fileLink.file.name
      this.mimeType = fileLink.file.mimeType
      this.url = fileLink.file.url ?? null
      this.order = fileLink.order
    } else {
      this.uuid = fileLink.uuid ?? null
      this.name = fileLink.name
      this.mimeType = fileLink.mimeType
      this.url = fileLink.url
      this.order = fileLink.order ?? null
    }
  }
}
