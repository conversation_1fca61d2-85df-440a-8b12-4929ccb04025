import { Entity, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, Column, Unique, Index } from 'typeorm'
import { EntityType } from '../enums/entity-type.enum.js'
import { MimeType } from '../../files/enums/mime-type.enum.js'

@Entity()
@Unique(['sapObjectId', 'sapFileId'])
export class SapFile {
  @PrimaryGeneratedColumn('uuid')
  uuid: string

  @CreateDateColumn({ precision: 3 })
  createdAt: Date

  @UpdateDateColumn({ precision: 3 })
  updatedAt: Date

  @Index()
  @Column({ type: 'varchar' })
  sapObjectId: string

  @Index()
  @Column({ type: 'varchar' })
  sapFileId: string

  @Column({ type: 'varchar' })
  entityType: EntityType

  @Column({ type: 'varchar' })
  entityPart: string

  @Column({ type: 'varchar' })
  mimeType: MimeType

  @Column({ type: 'varchar' })
  name: string

  @Column({ type: 'smallint', nullable: true })
  order: number | null
}
