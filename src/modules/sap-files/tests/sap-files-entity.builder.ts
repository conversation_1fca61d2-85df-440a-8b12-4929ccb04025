import { randomUUID } from 'crypto'
import { SapFile } from '../entities/sap-file.entity.js'
import { EntityType } from '../enums/entity-type.enum.js'
import { MimeType } from '../../files/enums/mime-type.enum.js'
import { EntityPart } from '../../files/enums/entity-part.enum.js'

export class SapFileEntityBuilder {
  private sapFile: SapFile

  constructor () {
    this.reset()
  }

  reset (): this {
    this.sapFile = new SapFile()

    this.sapFile.uuid = randomUUID()
    this.sapFile.createdAt = new Date()
    this.sapFile.updatedAt = new Date()
    this.sapFile.sapFileId = randomUUID()
    this.sapFile.sapObjectId = randomUUID()
    this.sapFile.entityType = EntityType.PICK_UP_REQUEST
    this.sapFile.name = 'test.pdf'
    this.sapFile.entityPart = EntityPart.ADDITIONAL
    this.sapFile.mimeType = MimeType.PDF
    this.sapFile.order = 1

    return this
  }

  withUuid (uuid: string): this {
    this.sapFile.uuid = uuid

    return this
  }

  withSapFileId (sapFileId: string): this {
    this.sapFile.sapFileId = sapFileId

    return this
  }

  withSapObjectId (sapObjectId: string): this {
    this.sapFile.sapObjectId = sapObjectId

    return this
  }

  withEntityType (entityType: EntityType): this {
    this.sapFile.entityType = entityType

    return this
  }

  withName (name: string): this {
    this.sapFile.name = name

    return this
  }

  withEntityPart (part: string): this {
    this.sapFile.entityPart = part

    return this
  }

  withMimeType (mimeType: MimeType): this {
    this.sapFile.mimeType = mimeType
    return this
  }

  withOrder (order: number | null): this {
    this.sapFile.order = order
    return this
  }

  build (): SapFile {
    const sapFile = this.sapFile

    this.reset()

    return sapFile
  }
}
