import { ApiProperty } from '@nestjs/swagger'
import { NotFoundApiError } from '../../exceptions/api-errors/not-found.api-error.js'
import { ApiErrorMeta } from '../../exceptions/api-errors/api-error-meta.decorator.js'
import { ApiErrorCode } from '../../exceptions/api-errors/api-error-code.decorator.js'
import { translateCurrent } from '../../localization/helpers/translate.helper.js'

export class SapFileNotFoundErrorMeta {
  @ApiProperty({ type: String, format: 'uuid' })
  sapFileUuid: string

  constructor (uuid: string) {
    this.sapFileUuid = uuid
  }
}

export class SapFileNotFoundError extends NotFoundApiError {
  @ApiErrorCode('sap_file_not_found')
  code = 'sap_file_not_found'

  @ApiErrorMeta()
  meta: SapFileNotFoundErrorMeta

  constructor (meta: SapFileNotFoundErrorMeta) {
    super(translateCurrent('error.sap-file.not-found'))
    this.meta = meta
  }
}
