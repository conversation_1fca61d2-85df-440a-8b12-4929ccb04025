import { Equals, IsNotEmpty, <PERSON><PERSON><PERSON>al, IsString, ValidateNested } from 'class-validator'
import { ApiProperty } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { FilterQuery, PaginatedOffsetSearchQuery } from '@wisemen/pagination'
import { IsUndefinable } from '@wisemen/validators'

export class ViewPickUpAddressIndexFilterQuery extends FilterQuery {
  @ApiProperty({ type: String, required: false, description: 'Optional for internal users' })
  @IsUndefinable()
  @IsString()
  @IsNotEmpty()
  customerId?: string
}

export class ViewPickUpAddressIndexQuery extends PaginatedOffsetSearchQuery {
  @Equals(undefined)
  sort?: never

  @ApiProperty({ type: ViewPickUpAddressIndexFilterQuery, required: false })
  @IsUndefinable()
  @Type(() => ViewPickUpAddressIndexFilterQuery)
  @ValidateNested()
  @IsNotEmpty()
  filter?: ViewPickUpAddressIndexFilterQuery

  @ApiProperty({ type: String, required: false })
  @IsOptional()
  search?: string
}
