import { Injectable } from '@nestjs/common'
import { AuthContext } from '../../../auth/auth.context.js'
import { SelectedCustomerFilterMismatchError } from '../../../customer/errors/selected-customer-filter-mismatch.error.js'
import { ViewPickUpAddressIndexQuery } from './query/view-pick-up-address-index.query.js'

@Injectable()
export class ViewPickUpAddressIndexValidator {
  constructor (
    private readonly authContext: AuthContext
  ) {}

  execute (query: ViewPickUpAddressIndexQuery): void {
    const selectedCustomerId = this.authContext.getSelectedCustomerId()
    if (
      selectedCustomerId != null
      && query.filter?.customerId != null
      && selectedCustomerId !== query.filter?.customerId
    ) {
      throw new SelectedCustomerFilterMismatchError({ pointer: '$.customerId' })
    }
  }
}
