import { ApiProperty } from '@nestjs/swagger'
import { PaginatedOffsetResponse } from '@wisemen/pagination'
import { PickUpAddressResponse } from '../../responses/pick-up-address.response.js'
import { calculatePageSize } from '../../../../utils/helpers/unknown-page-size.helper.js'
import { PickUpAddress } from '../../types/pick-up-address.type.js'

export class ViewPickUpAddressIndexResponse extends PaginatedOffsetResponse<PickUpAddressResponse> {
  @ApiProperty({ type: PickUpAddressResponse, isArray: true })
  declare items: PickUpAddressResponse[]

  constructor (
    items: PickUpAddress[],
    total: number | null,
    limit: number,
    offset: number
  ) {
    const result = items.map(address => new PickUpAddressResponse(address))

    if (total === null) {
      total = calculatePageSize(items, offset, limit)
    }

    super(result, total, limit, offset)
  }
}
