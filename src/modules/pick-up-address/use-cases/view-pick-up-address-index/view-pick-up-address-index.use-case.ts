import { Injectable } from '@nestjs/common'
import { typeormPagination } from '@wisemen/pagination'
import { SapGetPickUpAddressIndexResponse } from '../../../sap/use-cases/get-pick-up-address-index/get-pick-up-address-index.response.js'
import { SapGetPickUpAddressIndexUseCase } from '../../../sap/use-cases/get-pick-up-address-index/get-pick-up-address-index.use-case.js'
import { GetCustomerDefaultSalesOrganisationIdUseCase } from '../../../customer/use-cases/get-customer-default-sales-organisation-id/get-customer-default-sales-organisation-id.use-case.js'
import { SapQuery } from '../../../sap/query/sap-query.js'
import { MapPickUpAddressSapService } from '../../services/map-pick-up-address.service.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { ViewPickUpAddressIndexQuery } from './query/view-pick-up-address-index.query.js'
import { ViewPickUpAddressIndexResponse } from './view-pick-up-address-index.response.js'
import { ViewPickUpAddressIndexValidator } from './view-pick-up-address-index.validator.js'

@Injectable()
export class ViewPickUpAddressIndexUseCase {
  constructor (
    private readonly validator: ViewPickUpAddressIndexValidator,
    private readonly authContext: AuthContext,
    private readonly getCustomerSalesOrganisationId: GetCustomerDefaultSalesOrganisationIdUseCase,
    private readonly sapGetPickUpAddressIndex: SapGetPickUpAddressIndexUseCase
  ) {}

  public async execute (
    query: ViewPickUpAddressIndexQuery
  ): Promise<ViewPickUpAddressIndexResponse> {
    this.validator.execute(query)

    const sapQuery = await this.getSapQuery(query)
    const sapResult = await this.sapGetPickUpAddressIndex.execute(sapQuery)

    const pickUpAddresses = MapPickUpAddressSapService.mapResultsToPickUpAddresses(sapResult.items)

    const pagination = typeormPagination(query.pagination)
    return new ViewPickUpAddressIndexResponse(
      pickUpAddresses,
      null,
      pagination.take,
      pagination.skip
    )
  }

  private async getSapQuery (
    query: ViewPickUpAddressIndexQuery
  ): Promise<SapQuery<SapGetPickUpAddressIndexResponse>> {
    const filterCustomerId = query.filter?.customerId ?? this.authContext.getSelectedCustomerId()

    const sapQuery = new SapQuery<SapGetPickUpAddressIndexResponse>(query)

    if (filterCustomerId != null) {
      const customerDefaultSalesOrganisationId = await this.getCustomerSalesOrganisationId
        .getOrganisationIdOrFail(filterCustomerId)

      sapQuery.where('Customer', filterCustomerId)
        .andWhere('SalesOrganization', customerDefaultSalesOrganisationId)
    }

    return sapQuery
  }
}
