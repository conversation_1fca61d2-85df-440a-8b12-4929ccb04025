import { afterEach, before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import { createStubInstance, SinonStubbedInstance } from 'sinon'
import { expect } from 'expect'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { ViewPackagingTypeIndexValidator } from '../view-packaging-type-index.validator.js'
import { AuthContext } from '../../../../auth/auth.context.js'
import { UserCustomerAuthService } from '../../../../auth/services/user-customer-auth.service.js'
import { ViewPackagingTypeIndexQuery } from '../query/view-packaging-type-index.query.js'
import { CustomerNotAccessibleError } from '../../../../customer/errors/customer-not-accessible.error.js'
import { ViewPackagingTypeIndexQueryBuilder } from './view-packaging-type-index.query.builder.js'

describe('View packaging type index validator unit test', () => {
  let validator: ViewPackagingTypeIndexValidator

  let query: ViewPackagingTypeIndexQuery

  let authContext: SinonStubbedInstance<AuthContext>
  let userCustomerAuthService: SinonStubbedInstance<UserCustomerAuthService>

  before(() => {
    TestBench.setupUnitTest()

    query = new ViewPackagingTypeIndexQueryBuilder().build()

    authContext = createStubInstance(AuthContext)
    userCustomerAuthService = createStubInstance(UserCustomerAuthService)

    validator = new ViewPackagingTypeIndexValidator(
      authContext,
      userCustomerAuthService
    )

    mockMethods()
  })

  afterEach(() => {
    mockMethods()
  })

  function mockMethods () {
    authContext.getAzureEntraUpn.resolves(randomUUID())
    userCustomerAuthService.canUserAccessCustomer.resolves(true)
  }

  it('doesn\'t throw an error when validation passes', async () => {
    await expect(validator.execute(query)).resolves.not.toThrow()
  })

  it('throws an error when customer is not accessible by auth user', async () => {
    userCustomerAuthService.canUserAccessCustomer.resolves(false)

    await expect(validator.execute(query)).rejects.toThrow(CustomerNotAccessibleError)
  })

  it ('throws no error if user is an internal user', async () => {
    authContext.isInternalUser.resolves(true)
    await expect(validator.execute(query)).resolves.not.toThrow()
  })
})
