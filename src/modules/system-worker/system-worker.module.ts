import { DynamicModule, Module } from '@nestjs/common'
import { AppModule } from '../../app.module.js'
import { PublishNatsEventJobModule } from '../nats/outbox/publish-nats-event/publish-nats-event.module.js'
import { SyncTypesenseJobModule } from '../typesense/use-cases/sync-collection/sync-typesense-collection-job.module.js'
import { AssignDefaultNotificationPreferencesToUserJobModule } from '../notification/use-cases/assign-default-notification-preferences-to-user/assign-default-notification-preferences-to-user.job.module.js'
import { CreateNotificationJobModule } from '../notification/use-cases/create-notification/create-notification.job.module.js'
import { CreateUserNotificationsJobModule } from '../notification/use-cases/create-user-notifications/create-user-notifications.job-module.js'
import { AddNewNotificationTypeToPreferencesJobModule } from '../notification/use-cases/add-new-notification-type-to-preferences/add-new-notification-type-to-preferences.job.module.js'
import { CreateWasteInquirySapJobModule } from '../waste-inquiry/use-cases/create-waste-inquiry-sap/create-waste-inquiry-sap.job.module.js'
import { CreateContactsFromFormSubmissionJobModule } from '../contact/use-cases/create-contacts-from-form-submission/create-contacts-from-form-submission.job.module.js'
import { SyncUserRolesJobModule } from '../../app/users/use-cases/sync-user-roles/sync-user-roles.job-module.js'
import { RemoveFromTypesenseJobModule } from '../typesense/jobs/remove-from-typesense/remove-from-typesense.module.js'

@Module({})
export class SystemWorkerModule {
  static forRoot (modules: DynamicModule[]): DynamicModule {
    return {
      module: SystemWorkerModule,
      imports: [
        AppModule.forRoot(modules),
        PublishNatsEventJobModule,
        SyncTypesenseJobModule,
        RemoveFromTypesenseJobModule,
        CreateNotificationJobModule,
        CreateUserNotificationsJobModule,
        AddNewNotificationTypeToPreferencesJobModule,
        AssignDefaultNotificationPreferencesToUserJobModule,

        CreateContactsFromFormSubmissionJobModule,
        CreateWasteInquirySapJobModule,
        SyncUserRolesJobModule
      ]
    }
  }
}
