export interface EntraUserData {
  id: string
  upn: string
  firstName: string | null
  lastName: string | null
  email: string | null
}

export class EntraUserResponse {
  id: string
  upn: string
  firstName: string | null
  lastName: string | null
  email: string | null

  constructor (data: EntraUserData) {
    this.id = data.id
    this.upn = data.upn
    this.firstName = data.firstName
    this.lastName = data.lastName
    this.email = data.email
  }
}
