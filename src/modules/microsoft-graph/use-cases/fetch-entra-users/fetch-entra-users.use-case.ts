import { Injectable } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { AppRoleAssignment } from 'microsoft-graph'
import { EntraUserResponse } from './entra-user.response.js'
import { AppRoleAssignmentsService } from './app-role-assignments.service.js'
import { GroupMembersService } from './group-members.service.js'
import { UsersService } from './users.service.js'

@Injectable()
export class GetEntraUsersUseCase {
  constructor (
    private readonly configService: ConfigService,
    private readonly appRoleAssignmentsService: AppRoleAssignmentsService,
    private readonly groupMembersService: GroupMembersService,
    private readonly usersService: UsersService
  ) { }

  async execute (): Promise<EntraUserResponse[]> {
    const servicePrincipalId = this.configService.getOrThrow<string>('MICROSOFT_ENTRA_SERVICE_PRINCIPAL_ID')
    const allAssignments = await this.appRoleAssignmentsService.fetchAll(servicePrincipalId)

    const userAssignments = allAssignments.filter(
      (a: AppRoleAssignment) => a.principalType === 'User' && typeof a.principalId === 'string'
    )

    const groupAssignments = allAssignments.filter(
      (a: AppRoleAssignment) => a.principalType === 'Group' && typeof a.principalId === 'string'
    )

    const userIds: string[] = userAssignments.map(a => a.principalId!)
    const groupIds: string[] = groupAssignments.map(a => a.principalId!)

    const groupUserIds = await this.groupMembersService.fetchAllUserIdsFromGroups(groupIds)

    const allUserPrincipalIds: string[] = [...userIds, ...groupUserIds]
    const uniqueUserPrincipalIds = [...new Set(allUserPrincipalIds)]

    const allGraphUsers = await this.usersService.fetchByIds(uniqueUserPrincipalIds)

    const externalUsers = allGraphUsers.filter(
      u => typeof u.userPrincipalName === 'string' && !u.userPrincipalName.toLowerCase().endsWith('@indaver.com')
    )

    return externalUsers.map(u => new EntraUserResponse({
      id: u.id,
      upn: u.userPrincipalName,
      firstName: u.givenName,
      lastName: u.surname,
      email: u.mail
    }))
  }
}
