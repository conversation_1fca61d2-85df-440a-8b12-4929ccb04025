import { Client } from '@microsoft/microsoft-graph-client'
import { Inject, Injectable } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { AppRoleAssignment } from 'microsoft-graph'

@Injectable()
export class GetUserAppRolesUseCase {
  constructor (
    @Inject('MICROSOFT_GRAPH_CLIENT') private client: Client,
    private readonly configService: ConfigService
  ) {}

  async execute (
    userId: string
  ): Promise<AppRoleAssignment[]> {
    const servicePrincipalId = this.configService.getOrThrow<string>('MICROSOFT_ENTRA_SERVICE_PRINCIPAL_ID')

    const data = await this.client.api(`/users/${userId}/appRoleAssignments`)
      .filter(`resourceId eq ${servicePrincipalId}`)
      .get() as { value: AppRoleAssignment[] }

    return data.value ?? []
  }
}
