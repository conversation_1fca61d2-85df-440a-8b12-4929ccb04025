import { Modu<PERSON> } from '@nestjs/common'
import { ConfigModule } from '@nestjs/config'
import Jo<PERSON> from 'joi'
import { SyncEntraUsersCronjobModule } from '../../app/users/sync-entra-users/sync-entra-users.cron-job.module.js'
import { GetEntraUsersModule } from './use-cases/fetch-entra-users/fetch-entra-users.module.js'
import { GetUserAppRolesModule } from './use-cases/get-user-app-roles/get-user-app-roles.module.js'

@Module({
  imports: [
    ConfigModule.forRoot({
      envFilePath: process.env.ENV_FILE,
      validationSchema: Joi.object({
        MICROSOFT_GRAPH_TENANT_ID: Joi.string().required(),
        MICROSOFT_GRAPH_CLIENT_ID: Joi.string().required(),
        MICROSOFT_GRAPH_CLIENT_SECRET: Joi.string().required(),
        MICROSOFT_ENTRA_SERVICE_PRINCIPAL_ID: Joi.string().required()
      })
    }),
    GetUserAppRolesModule,
    GetEntraUsersModule,
    SyncEntraUsersCronjobModule
  ],

  exports: [
    GetUserAppRolesModule
  ]
})
export class MicrosoftGraphModule { }
