import { Module } from '@nestjs/common'
import { ConfigModule } from '@nestjs/config'
import Jo<PERSON> from 'joi'
import { SapCreatePickUpRequestModule } from './use-cases/create-pick-up-request/create-pick-up-request.module.js'
import { SapCreateWasteInquiryModule } from './use-cases/create-waste-inquiry/create-waste-inquiry.module.js'
import { SapGetPickUpRequestIndexModule } from './use-cases/get-pick-up-request-index/get-pick-up-request-index.module.js'
import { SapGetWasteInquiryIndexModule } from './use-cases/get-waste-inquiry-index/get-waste-inquiry-index.module.js'
import { SapGetContainerTypeIndexModule } from './use-cases/get-container-type-index/get-container-type-index.module.js'
import { SapGetContractLineIndexModule } from './use-cases/get-contract-line-index/get-contract-line-index.module.js'
import { SapGetCustomerIndexModule } from './use-cases/get-customer-index/get-customer-index.module.js'
import { SapGetEwcCodeIndexModule } from './use-cases/get-ewc-code-index/get-ewc-code-index.module.js'
import { SapGetPackagingTypeIndexModule } from './use-cases/get-packaging-type-index/get-packaging-type-index.module.js'
import { SapGetPickUpAddressIndexModule } from './use-cases/get-pick-up-address-index/get-pick-up-address-index.module.js'
import { SapGetUnNumberIndexModule } from './use-cases/get-un-number-index/get-un-number-index.module.js'
import { SapGetWasteProducerIndexModule } from './use-cases/get-waste-producer-index/get-waste-producer-index.module.js'
import { SapGetCustomerSalesOrganizationIndexModule } from './use-cases/get-customer-sales-organization-index/get-customer-sales-organization-index.module.js'
import { SapCreateWeeklyPlanningRequestModule } from './use-cases/create-weekly-planning-request/create-weekly-planning-request.module.js'
import { SapGetIsPoReferenceRequiredModule } from './use-cases/get-is-po-reference-required/get-is-po-reference-required.module.js'
import { SapGetIsCostCenterRequiredModule } from './use-cases/get-is-cost-center-required/get-is-cost-center-required.module.js'
import { SapGetPickUpRequestDetailModule } from './use-cases/get-pick-up-request-detail/get-pick-up-request-detail.module.js'
import { SapGetInvoiceIndexModule } from './use-cases/get-invoice-index/get-invoice-index.module.js'
import { SapGetPickUpRequestCommentModule } from './use-cases/get-pick-up-request-comment/get-pick-up-request-comment.module.js'
import { SapGetPickUpRequestContactsModule } from './use-cases/get-pick-up-request-contacts/get-pick-up-request-contacts.module.js'
import { SapGetPickUpRequestAttachmentsModule } from './use-cases/get-pick-up-request-attachments/get-pick-up-request-attachments.module.js'
import { SapUpdatePickUpRequestModule } from './use-cases/update-pick-up-request/update-pick-up-request.module.js'
import { SapGetTankerTypeIndexModule } from './use-cases/get-tanker-type-index/get-tanker-type-index.module.js'
import { SapGetUnNumberIndexForPickUpRequestModule } from './use-cases/get-un-number-index-for-pick-up-request/get-un-number-index-for-pick-up-request.module.js'
import { SapGetDraftInvoiceIndexModule } from './use-cases/get-draft-invoice-index/get-draft-invoice-index.module.js'
import { SapGetTransportTypeIndexModule } from './use-cases/get-transport-type-index/get-transport-type-index.module.js'
import { SapGetSalesOrganisationIndexModule } from './use-cases/get-sales-organisation-index/get-sales-organisation-index.module.js'
import { SapApproveDraftInvoiceCustomerModule } from './use-cases/approve-draft-invoice-customer/approve-draft-invoice-customer.module.js'
import { SapApproveDraftInvoiceSalesRepModule } from './use-cases/approve-draft-invoice-sales-rep/approve-draft-invoice-sales-rep.module.js'
import { SapRejectDraftInvoiceCustomerModule } from './use-cases/reject-draft-invoice-customer/reject-draft-invoice-customer.module.js'
import { SapRejectDraftInvoiceSalesRepModule } from './use-cases/reject-draft-invoice-sales-rep/reject-draft-invoice-sales-rep.module.js'
import { SapGetIndascanDetailModule } from './use-cases/get-indascan-detail/get-indascan-detail.module.js'
import { SapGenerateContractLinesPdfModule } from './use-cases/generate-contract-lines-pdf/generate-contract-lines-pdf.module.js'
import { SapUploadDocumentModule } from './use-cases/upload-document/upload-document.module.js'
import { SapCreateDocumentModule } from './use-cases/create-document/create-document.module.js'
import { SapGetGuidanceLetterIndexModule } from './use-cases/get-guidance-letter-index/get-guidance-letter-index.module.js'
import { SapUploadAndCreateDocumentModule } from './use-cases/upload-and-create-document/upload-and-create-document.module.js'
import { SapGetDocumentsIndexModule } from './use-cases/get-documents-index/get-documents-index.module.js'
import { SapGetCertificateIndexModule } from './use-cases/get-certificate-index/get-certificate-index.module.js'
import { SapGetPayerIndexModule } from './use-cases/get-payer-index/get-payer-index.module.js'

@Module({
  imports: [
    ConfigModule.forRoot({
      envFilePath: process.env.ENV_FILE,
      validationSchema: Joi.object({
        SAP_BASE_URL: Joi.string().uri().required(),
        SAP_USERNAME: Joi.string().required(),
        SAP_PASSWORD: Joi.string().required(),
        SAP_CLIENT: Joi.string().required()
      })
    }),

    SapApproveDraftInvoiceCustomerModule,
    SapApproveDraftInvoiceSalesRepModule,
    SapCreateDocumentModule,
    SapCreatePickUpRequestModule,
    SapCreateWasteInquiryModule,
    SapCreateWeeklyPlanningRequestModule,
    SapGenerateContractLinesPdfModule,
    SapGetCertificateIndexModule,
    SapGetContainerTypeIndexModule,
    SapGetContractLineIndexModule,
    SapGetCustomerIndexModule,
    SapGetCustomerSalesOrganizationIndexModule,
    SapGetDocumentsIndexModule,
    SapGetDraftInvoiceIndexModule,
    SapGetEwcCodeIndexModule,
    SapGetGuidanceLetterIndexModule,
    SapGetIndascanDetailModule,
    SapGetInvoiceIndexModule,
    SapGetIsCostCenterRequiredModule,
    SapGetIsPoReferenceRequiredModule,
    SapGetPackagingTypeIndexModule,
    SapGetPayerIndexModule,
    SapGetPickUpAddressIndexModule,
    SapGetPickUpRequestAttachmentsModule,
    SapGetPickUpRequestCommentModule,
    SapGetPickUpRequestContactsModule,
    SapGetPickUpRequestDetailModule,
    SapGetPickUpRequestIndexModule,
    SapGetSalesOrganisationIndexModule,
    SapGetTankerTypeIndexModule,
    SapGetTransportTypeIndexModule,
    SapGetUnNumberIndexForPickUpRequestModule,
    SapGetUnNumberIndexModule,
    SapGetWasteInquiryIndexModule,
    SapGetWasteProducerIndexModule,
    SapRejectDraftInvoiceCustomerModule,
    SapRejectDraftInvoiceSalesRepModule,
    SapUpdatePickUpRequestModule,
    SapUploadAndCreateDocumentModule,
    SapUploadDocumentModule
  ],
  exports: [
    SapApproveDraftInvoiceCustomerModule,
    SapApproveDraftInvoiceSalesRepModule,
    SapCreateDocumentModule,
    SapCreatePickUpRequestModule,
    SapCreateWasteInquiryModule,
    SapCreateWeeklyPlanningRequestModule,
    SapGenerateContractLinesPdfModule,
    SapGetCertificateIndexModule,
    SapGetContainerTypeIndexModule,
    SapGetContractLineIndexModule,
    SapGetCustomerIndexModule,
    SapGetCustomerSalesOrganizationIndexModule,
    SapGetDocumentsIndexModule,
    SapGetDraftInvoiceIndexModule,
    SapGetEwcCodeIndexModule,
    SapGetGuidanceLetterIndexModule,
    SapGetIndascanDetailModule,
    SapGetInvoiceIndexModule,
    SapGetIsCostCenterRequiredModule,
    SapGetIsPoReferenceRequiredModule,
    SapGetPackagingTypeIndexModule,
    SapGetPayerIndexModule,
    SapGetPickUpAddressIndexModule,
    SapGetPickUpRequestAttachmentsModule,
    SapGetPickUpRequestCommentModule,
    SapGetPickUpRequestContactsModule,
    SapGetPickUpRequestDetailModule,
    SapGetPickUpRequestIndexModule,
    SapGetSalesOrganisationIndexModule,
    SapGetTankerTypeIndexModule,
    SapGetTransportTypeIndexModule,
    SapGetUnNumberIndexForPickUpRequestModule,
    SapGetUnNumberIndexModule,
    SapGetWasteInquiryIndexModule,
    SapGetWasteProducerIndexModule,
    SapRejectDraftInvoiceCustomerModule,
    SapRejectDraftInvoiceSalesRepModule,
    SapUpdatePickUpRequestModule,
    SapUploadAndCreateDocumentModule,
    SapUploadDocumentModule
  ]
})
export class SapModule {}
