import { SapNumberBooleanType } from '../../types/number-boolean.type.js'
import { SapUomType } from '../../types/uom.type.js'
import { SapWasteType } from '../../types/waste.type.js'
import { SapXBooleanType } from '../../types/x-boolean.type.js'

export interface SapCreatePickUpRequestWasteItemCommand {
  Reqno: ''
  NameApplicant?: string
  EmailApplicant?: string
  Kunnr?: string
  Vkorg?: string
  KunnrY2?: string
  KunnrWe?: string
  WasteType?: SapWasteType
  Vbeln?: string
  Posnr?: string
  Matnr?: string
  Dangerous?: string
  Arktx?: string
  Eural?: string
  Yyasn?: string
  Yyklantmat?: string
  ActionNr?: string
  QuantityPallets?: string
  NrOPallets?: string
  WeightVolume?: string
  WeightUom?: SapUomType
  CostCenter?: string
  OrderNumber?: string
  Yyun?: string
  TypeRecipient?: string
  QuantityBarrels?: string
  QuantityLabels?: string
  QuanContainers?: string
  ContainerType?: string
  Cont1Weight?: string
  Cont1Volume?: string
  Cont1Uom?: string
  Cont1Nr?: string
  Cont1Transport?: string
  BulkType?: string
  Cont1Cover?: SapNumberBooleanType
  RequestedDate?: string
  ReqDateFrom?: string
  ReqDateTo?: string
  TransportBy?: SapXBooleanType
  ReturnPackaging?: boolean
  ReturnPackagingRemark?: string
  IdWeekPlanning?: string
  Yypackaginggrp?: string
  ReconciliationNr?: string
  HazardInducers?: string
  Tfsnumber?: string
  SerialNumber?: string
  CreateLangu?: string
  DateApplication?: string
  ConfirmWic?: boolean
}

export interface SapCreatePickUpRequestCommand {
  Reqno: ''
  ToDisposalRequest: SapCreatePickUpRequestWasteItemCommand[]
  ToWMRComment?: {
    Reqno: ''
    Comment: string
  }[]
  ToWMRContacts?: {
    Reqno: ''
    Email: string
    Name1: string
    Name2: string
  }[]
  ToWMRAttachments?: {
    Reqno: ''
    Posnr: string
    Filename: string
  }[]
}
