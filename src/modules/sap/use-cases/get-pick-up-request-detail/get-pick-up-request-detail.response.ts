import { SapBulkType } from '../../types/bulk.type.js'
import { SapContainerTransportType } from '../../types/container-transport.type.js'
import { SapNumberBooleanType } from '../../types/number-boolean.type.js'
import { SapUomType } from '../../types/uom.type.js'
import { SapWasteType } from '../../types/waste.type.js'
import { SapXBooleanType } from '../../types/x-boolean.type.js'

export interface SapGetPickUpRequestDetailResponse {
  __metadata?: {
    id: string
    uri: string
    type: string
  }
  Aedate?: string | null
  ConfirmWic?: boolean
  ExtId?: string
  ReconciliationNr?: string
  ReqDateFrom?: string | null
  ReqDateTo?: string | null
  Aename?: string
  Reqno?: string
  Cont1Volume?: string
  Reqpos?: string
  EmailApplicant?: string
  Vbeln?: string
  IdWeekPlanning?: string
  Posnr?: string
  NrOPallets?: string
  Status?: string
  Kunnr?: string
  Kunnrname?: string
  Kunnrstreet?: string
  Kunnrcity?: string
  Kunnrpostcode?: string
  Kunnrland?: string
  ReturnPackaging?: boolean
  KunnrY2?: string
  Kunnry2name?: string
  Kunnry2street?: string
  Kunnry2city?: string
  Kunnry2postcode?: string
  Kunnry2land?: string
  ReturnPackagingRemark?: string
  HazardInducers?: string
  KunnrWe?: string
  Kunnrwename?: string
  Kunnrwestreet?: string
  Kunnrwecity?: string
  Kunnrwepostcode?: string
  Kunnrweland?: string
  Yyklantmat?: string
  Matnr?: string
  Arktx?: string
  RequestedDate?: string
  ConfirmedDate?: string
  TransportBy?: SapXBooleanType
  TransportByTxt?: string
  NameApplicant?: string
  DateApplication?: string
  WasteType?: SapWasteType | ''
  WasteTypeTxt?: string
  OrderNumber?: string
  CostCenter?: string
  WeightVolume?: string
  WeightUom?: SapUomType | ''
  QuantityPallets?: string
  QuantityLabels?: string
  QuantityBarrels?: string
  TypeRecipient?: string
  QuanContainers?: string
  Cont1Nr?: string
  Cont1Cover?: SapNumberBooleanType | ''
  Cont1Transport?: SapContainerTransportType | ''
  Cont1TranspTxt?: string
  Cont1Weight?: string
  Cont1Uom?: SapUomType | ''
  Cont2Yypack?: string
  BulkType?: SapBulkType | ''
  BulkTypeTxt?: string
  Services?: string
  ServicesTxt?: string
  Explanation?: string
  SalesDoc?: string
  SalesDocitem?: string
  DeliveryDoc?: string
  Yyasn?: string
  Yyun?: string
  Yypackaginggrp?: string
  Tfsnumber?: string
  SerialNumber?: string
  ReasonCancel?: string
  CreateLangu?: string
  Dangerous?: string
  LifnrY0?: string
  Dgcl?: string
  Eural?: string
  ActionNr?: string
  YeLifnr?: string
  ConfDelDate?: string | null
  Hpn1?: string
  Hpn2?: string
  Hpn3?: string
  ContainerType?: string
  Vkorg?: string
}
