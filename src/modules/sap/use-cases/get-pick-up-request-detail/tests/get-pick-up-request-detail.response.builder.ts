import { randBoolean, rand<PERSON>ity, randCompanyName, randCountryCode, randFullName, randNumber, randProductMaterial, randStreetName, randZipCode } from '@ngneat/falso'
import { SAP_ENDPOINTS } from '../../../constants/endpoint.constants.js'
import { SapGetPickUpRequestDetailResponse } from '../get-pick-up-request-detail.response.js'
import { SapXBooleanType } from '../../../types/x-boolean.type.js'
import { SapWasteType } from '../../../types/waste.type.js'
import { SapNumberBooleanType } from '../../../types/number-boolean.type.js'
import { SapUomType } from '../../../types/uom.type.js'
import { SapDateFormatterService } from '../../../services/sap-date-formatter.service.js'

export class SapGetPickUpRequestDetailResponseBuilder {
  private response: SapGetPickUpRequestDetailResponse

  constructor () {
    this.reset()
  }

  reset (): this {
    this.response = {
      __metadata: {
        id: `https://services.odata.org/OData${SAP_ENDPOINTS.PICK_UP_REQUEST.INDEX}`,
        uri: `https://services.odata.org/OData${SAP_ENDPOINTS.PICK_UP_REQUEST.INDEX}`,
        type: 'ZCZ_SRV.wmrItems'
      },
      Aedate: null,
      ConfirmWic: randBoolean(),
      ExtId: '',
      ReconciliationNr: randNumber({ min: 1000000, max: 9999999 }).toString(),
      ReqDateFrom: null,
      ReqDateTo: null,
      Aename: '',
      Reqno: randNumber({ min: 1000000, max: 9999999 }).toString(),
      Cont1Volume: `${randNumber({ min: 0, max: 100 }).toString()}.000`,
      Reqpos: randNumber({ min: 1000000, max: 9999999 }).toString(),
      EmailApplicant: '',
      Vbeln: randNumber({ min: 1000000, max: 9999999 }).toString(),
      IdWeekPlanning: randNumber({ min: 1000000, max: 9999999 }).toString(),
      Posnr: randNumber({ min: 1000000, max: 9999999 }).toString(),
      NrOPallets: '0',
      Status: '002',
      Kunnr: randNumber({ min: 10000000, max: 99999999 }).toString(),
      Kunnrname: randCompanyName(),
      Kunnrstreet: randStreetName(),
      Kunnrcity: randCity(),
      Kunnrpostcode: randZipCode(),
      Kunnrland: randCountryCode(),
      ReturnPackaging: false,
      KunnrY2: randNumber({ min: 10000000, max: 99999999 }).toString(),
      Kunnry2name: randCompanyName(),
      Kunnry2street: randStreetName(),
      Kunnry2city: randCity(),
      Kunnry2postcode: randZipCode(),
      Kunnry2land: randCountryCode(),
      ReturnPackagingRemark: '',
      HazardInducers: '',
      KunnrWe: randNumber({ min: 10000000, max: 99999999 }).toString(),
      Kunnrwename: randCompanyName(),
      Kunnrwestreet: randStreetName(),
      Kunnrwecity: randCity(),
      Kunnrwepostcode: randZipCode(),
      Kunnrweland: randCountryCode(),
      Yyklantmat: '',
      Matnr: '',
      Arktx: randProductMaterial(),
      RequestedDate: '/Date(1741824000000)/',
      ConfirmedDate: '/Date(1741824000000)/',
      TransportBy: ['', 'X'][randNumber({ min: 0, max: 1 })] as SapXBooleanType,
      TransportByTxt: '',
      NameApplicant: randFullName(),
      DateApplication: '/Date(1741824000000)/',
      WasteType: ['01', '02', '03', '04', '05', '06'][randNumber({ min: 0, max: 5 })] as SapWasteType,
      WasteTypeTxt: '',
      OrderNumber: '',
      CostCenter: '',
      WeightVolume: `${randNumber({ min: 0, max: 100 })}.00`,
      WeightUom: ['M3', 'TO', 'ST', 'KG'][randNumber({ min: 0, max: 2 })] as SapUomType,
      QuantityPallets: randNumber({ min: 0, max: 100 }).toString(),
      QuantityLabels: '',
      QuantityBarrels: randNumber({ min: 0, max: 100 }).toString(),
      TypeRecipient: '',
      QuanContainers: '',
      Cont1Nr: randNumber({ min: 1000000, max: 9999999 }).toString(),
      Cont1Cover: ['01', '02'][randNumber({ min: 0, max: 1 })] as SapNumberBooleanType,
      Cont1Transport: '',
      Cont1TranspTxt: '',
      Cont1Weight: `${randNumber({ min: 0, max: 100 })}.00`,
      Cont1Uom: '',
      Cont2Yypack: '',
      BulkType: '',
      BulkTypeTxt: '',
      Services: '',
      ServicesTxt: '',
      Explanation: '',
      SalesDoc: randNumber({ min: 100, max: 999 }).toString(),
      SalesDocitem: randNumber({ min: 100000, max: 999999 }).toString(),
      DeliveryDoc: '',
      Yyasn: '',
      Yyun: '',
      Yypackaginggrp: '',
      Tfsnumber: '',
      SerialNumber: randNumber({ min: 1000, max: 9999 }).toString(),
      ReasonCancel: '',
      CreateLangu: '',
      Dangerous: '',
      LifnrY0: '',
      Dgcl: '',
      Eural: '',
      ActionNr: randNumber({ min: 100, max: 999 }).toString(),
      YeLifnr: '',
      ConfDelDate: null,
      Hpn1: '',
      Hpn2: '',
      Hpn3: '',
      ContainerType: '',
      Vkorg: ''
    }

    return this
  }

  withRequestNumber (requestNumber: string): this {
    this.response.Reqno = requestNumber

    return this
  }

  withStatus (sapStatus: string): this {
    this.response.Status = sapStatus

    return this
  }

  withPosition (position: string): this {
    this.response.Reqpos = position

    return this
  }

  withWasteType (wasteType: SapWasteType): this {
    this.response.WasteType = wasteType

    return this
  }

  withKunnr (kunnr: string): this {
    this.response.Kunnr = kunnr

    return this
  }

  withKunnrY2 (kunnrY2: string): this {
    this.response.KunnrY2 = kunnrY2

    return this
  }

  withYyun (yyun: string): this {
    this.response.Yyun = yyun

    return this
  }

  withReqDateTo (end: Date | null): this {
    this.response.ReqDateTo = SapDateFormatterService.toSapDate(end)
    return this
  }

  withRequestedDate (start: Date): this {
    this.response.RequestedDate = SapDateFormatterService.toSapDate(start)!
    return this
  }

  build (): SapGetPickUpRequestDetailResponse {
    const result = this.response

    this.reset()

    return result
  }
}
