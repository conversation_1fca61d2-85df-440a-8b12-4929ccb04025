import { SAP_ENDPOINTS } from '../../../../constants/endpoint.constants.js'
import { SapDateFormatterService } from '../../../../services/sap-date-formatter.service.js'
import { SapWasteType } from '../../../../types/waste.type.js'
import { SapGetIndascanDetailResponse } from '../../get-indascan-detail.response.js'

export class SapGetIndascanDetailResponseBuilder {
  private response: SapGetIndascanDetailResponse

  constructor () {
    this.response = {
      __metadata: {
        id: `https://services.odata.org/OData${SAP_ENDPOINTS.PICK_UP_REQUEST.INDASCAN_DETAIL}`,
        uri: `https://services.odata.org/OData${SAP_ENDPOINTS.PICK_UP_REQUEST.INDASCAN_DETAIL}`,
        type: 'ZCZ_SRV.disposalRequest'
      },
      Yyesn: '',
      Aedate: '/Date(1753142400000)/',
      RequestedDate: '/Date(1753228800000)/',
      ConfirmWic: false,
      Stext: '',
      Cont2Nr: '',
      ExtId: '',
      Cont2Cover: '',
      ReconciliationNr: '000000',
      Cont2Transport: '',
      ReqDateFrom: '/Date(1753228800000)/',
      Cont2TranspTxt: '',
      ReqDateTo: '/Date(1753833600000)/',
      Aename: 'CZ_SERVICE',
      Cont2Weight: '0.00',
      Cont2Uom: '',
      Reqno: '2000200',
      Cont1Volume: '0.000',
      Cont2OrderNumb: '',
      Cont2CostCente: '',
      Reqpos: '000020',
      Cont2Yyun: '',
      EmailApplicant: '<EMAIL>',
      Cont2Yypack: '',
      Vbeln: '30000030',
      Cont2Tfsnumber: '',
      IdWeekPlanning: '000000',
      Cont2Serial: '0000',
      Posnr: '000010',
      BulkLoad: '',
      NrOPallets: '0',
      BulkLoadTxt: '',
      Status: '000',
      CombineReqno: '',
      Kunnr: '50000075',
      CombineAnswer: '',
      ReturnPackaging: false,
      IdApplicant: '',
      KunnrY2: '50000125',
      DgChanged: false,
      ReturnPackagingRemark: '',
      Cont2DgChanged: false,
      HazardInducers: '',
      KunnrWe: '50000075',
      ReqDateLabel: '',
      EdiFlag: false,
      Yyklantmat: '590010',
      Matnr: '590010',
      Yymatnrdg: '',
      Aetime: 'PT13H55M30S',
      Arktx: '160506* LAB SMALLS CMIR',
      ConfirmedDate: null,
      TransportBy: 'X',
      TransportByTxt: '',
      NameApplicant: 'Joren Vandeweyer',
      DateApplication: null,
      WasteType: '02',
      WasteTypeTxt: '',
      OrderNumber: '',
      CostCenter: '100',
      WeightVolume: '4.00',
      WeightUom: 'TO',
      QuantityPallets: '0',
      QuantityLabels: '',
      QuantityBarrels: '0',
      TypeRecipient: '',
      QuanContainers: '',
      Cont1Nr: '',
      Cont1Cover: '',
      Cont1Transport: '02',
      Cont1TranspTxt: '',
      Cont1Weight: '0.00',
      Cont1Uom: '',
      BulkType: '',
      BulkTypeTxt: '',
      Services: '',
      ServicesTxt: '',
      Explanation: '',
      SalesDoc: '',
      SalesDocitem: '000000',
      DeliveryDoc: '',
      Yyasn: '',
      Yyun: '1013',
      Yypackaginggrp: '0',
      Tfsnumber: '',
      SerialNumber: '0000',
      ReasonCancel: '',
      CreateLangu: 'EN',
      Dangerous: '',
      LifnrY0: '',
      Dgcl: '',
      Eural: '',
      ActionNr: '000',
      YeLifnr: '',
      ConfDelDate: null,
      Hpn1: '',
      Hpn2: '',
      Hpn3: '',
      ContainerType: '',
      Vkorg: '1600'
    }
  }

  withRequestNumber (requestNumber: string): this {
    this.response.Reqno = requestNumber

    return this
  }

  withStatus (sapStatus: string): this {
    this.response.Status = sapStatus

    return this
  }

  withPosition (position: string): this {
    this.response.Reqpos = position

    return this
  }

  withWasteType (wasteType: SapWasteType): this {
    this.response.WasteType = wasteType

    return this
  }

  withReqDateTo (end: Date | null): this {
    this.response.ReqDateTo = SapDateFormatterService.toSapDate(end)
    return this
  }

  withRequestedDate (start: Date | null): this {
    this.response.RequestedDate = SapDateFormatterService.toSapDate(start)!
    return this
  }

  build (): SapGetIndascanDetailResponse {
    return this.response
  }
}
