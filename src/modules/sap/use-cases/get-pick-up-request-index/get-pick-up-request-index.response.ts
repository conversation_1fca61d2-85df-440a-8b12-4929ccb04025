import { SapBulkType } from '../../types/bulk.type.js'
import { SapContainerTransportType } from '../../types/container-transport.type.js'
import { SapUomType } from '../../types/uom.type.js'

export interface SapGetPickUpRequestIndexResponse {
  __metadata?: {
    id: string
    uri: string
    type: string
  }
  Aedate?: string
  Reqno?: string
  Reqpos?: string
  Arktx?: string
  Yyklantmat?: string
  Vbeln?: string
  Posnr?: string
  ActionNr?: string
  Wastetype?: string
  Status?: string
  Dateapplication?: string
  Costcenter?: string
  Transportby?: string
  Requesteddate?: string | null
  Confirmeddate?: string
  Salesdoc?: string
  Dangerous?: string
  Nameapplicant?: string
  Kunnr?: string
  Kunnrname?: string
  Kunnry2?: string
  Kunnry2name?: string
  Kunnrwe?: string
  Kunnrwename?: string
  Ordernumber?: string
  Cont1nr?: string
  Extid?: string
  Eural?: string
  Tfsnumber?: string
  Aetime?: string
  Lifnry0name?: string
  Lifnry3name?: string
  Accountmanager?: string
  Materialanalysis?: string
  Deliveryinfo?: string
  QuantityPallets?: string
  QuantityBarrels?: string
  ReturnPackaging?: string
  ReturnPackagingRemark?: string
  WeightVolume?: string
  WeightUom?: SapUomType | ''
  Yyun?: string
  TypeRecipient?: string
  ReqDateFrom?: string
  ReqDateTo?: string
  QuantityLabels?: string
  ContainerType?: string
  Cont1Weight?: string
  Cont1Transport?: SapContainerTransportType | ''
  NrOPallets?: string
  Cont1Cover?: string
  BulkType?: SapBulkType | ''
  Yyesn?: string
  Kunnrstreet?: string
  Kunnrwepostcode?: string
  Kunnrpostcode?: string
  Kunnrwecity?: string
  Kunnrwestreet?: string
}
