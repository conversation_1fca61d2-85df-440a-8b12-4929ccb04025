import { Inject, Injectable } from '@nestjs/common'
import { AxiosInstance } from 'axios'
import { SapQuery } from '../../query/sap-query.js'
import { SAP_ENDPOINTS } from '../../constants/endpoint.constants.js'
import { SapDResultResponse } from '../../shared/sap-d-result.response.js'
import { SAP_CLIENT } from '../../sap-client.provider.js'
import { SapGetInvoiceIndexResponse } from './get-invoice-index.response.js'

@Injectable()
export class SapGetInvoiceIndexUseCase {
  constructor (
  @Inject(SAP_CLIENT) private readonly client: AxiosInstance
  ) {}

  async execute (
    query: SapQuery<SapGetInvoiceIndexResponse>
  ): Promise<SapGetInvoiceIndexResponse[]> {
    const url: string = query.buildUrl(SAP_ENDPOINTS.INVOICE.INDEX)

    const response = await this.client.get<SapDResultResponse<SapGetInvoiceIndexResponse[]>>(url)

    return response.data.d.results
  }
}
