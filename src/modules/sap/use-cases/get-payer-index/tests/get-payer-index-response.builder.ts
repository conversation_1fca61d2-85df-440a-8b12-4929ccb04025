import { randomUUID } from 'crypto'
import { randCity, randCompanyName, randCountry, randStreetName } from '@ngneat/falso'
import { SapGetPayerIndexResponse } from '../get-payer-index.response.js'

export class SapGetPayerIndexResponseBuilder {
  private response: SapGetPayerIndexResponse

  constructor () {
    this.reset()
  }

  private reset (): this {
    this.response = {
      __metadata: {
        type: 'ZCZ_SRV.payer'
      },
      Number: randomUUID(),
      Name: randCompanyName(),
      City: randCity(),
      PostCode: '2040',
      Street: randStreetName(),
      Country: randCountry()
    }

    return this
  }

  withNumber (number: string): this {
    this.response.Number = number
    return this
  }

  withName (name: string): this {
    this.response.Name = name
    return this
  }

  withCity (city: string): this {
    this.response.City = city
    return this
  }

  withPostCode (postCode: string): this {
    this.response.PostCode = postCode
    return this
  }

  withStreet (street: string): this {
    this.response.Street = street
    return this
  }

  withCountry (country: string): this {
    this.response.Country = country
    return this
  }

  build (): SapGetPayerIndexResponse {
    const result = this.response
    this.reset()
    return result
  }
}
