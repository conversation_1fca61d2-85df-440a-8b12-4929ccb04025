import { Inject, Injectable } from '@nestjs/common'
import { AxiosInstance } from 'axios'
import { SAP_ENDPOINTS } from '../../constants/endpoint.constants.js'
import { SapDResultResponse } from '../../shared/sap-d-result.response.js'
import { SAP_CLIENT } from '../../sap-client.provider.js'
import { SapGetPayerIndexResponse } from './get-payer-index.response.js'

@Injectable()
export class SapGetPayerIndexUseCase {
  constructor (
  @Inject(SAP_CLIENT) private readonly client: AxiosInstance
  ) {}

  async execute (
    query: string
  ): Promise<SapGetPayerIndexResponse[]> {
    const url = `${SAP_ENDPOINTS.PAYER.INDEX}${query}`

    const response = await this.client.get<SapDResultResponse<SapGetPayerIndexResponse[]>>(url)

    return response.data.d.results
  }
}
