import { Inject, Injectable } from '@nestjs/common'
import { AxiosInstance } from 'axios'
import { SAP_CLIENT } from '../../sap-client.provider.js'
import { SapQuery } from '../../query/sap-query.js'
import { SAP_ENDPOINTS } from '../../constants/endpoint.constants.js'
import { SapDResultResponse } from '../../shared/sap-d-result.response.js'
import { SapGetCertificateIndexResponse } from './get-certificate-index.response.js'

@Injectable()
export class SapGetCertificateIndexUseCase {
  constructor (
    @Inject(SAP_CLIENT) private readonly client: AxiosInstance
  ) {}

  async execute (
    query: SapQuery<SapGetCertificateIndexResponse>
  ): Promise<SapGetCertificateIndexResponse[]> {
    const url = query.buildUrl(SAP_ENDPOINTS.CERTIFICATES.INDEX)

    const response = await this.client
      .get<SapDResultResponse<SapGetCertificateIndexResponse[]>>(url)

    return response.data.d.results
  }
}
