import { SAP_ENDPOINTS } from '../../constants/endpoint.constants.js'
import { SapGetCertificateIndexResponse } from './get-certificate-index.response.js'

export class SapGetCertificateIndexResponseBuilder {
  private response: SapGetCertificateIndexResponse

  constructor () {
    this.response = {
      __metadata: {
        id: `https://services.odata.org/OData${SAP_ENDPOINTS.CERTIFICATES.INDEX}`,
        uri: `https://services.odata.org/OData${SAP_ENDPOINTS.CERTIFICATES.INDEX}`,
        type: 'ZCZ_SRV.listCertificates'
      },
      contract: '123123',
      DocType: 'Treatment certificates',
      ContractPos: '000000',
      SalesOrder: '18377',
      SalesOrderPos: '000010',
      Arktx: 'METHANOL',
      Delivery: '123123',
      Invoice: '90000354',
      Ketdat: '/Date(1753747200000)/',
      Kunnr: '50000125',
      Name1: 'Aspen Oss bv',
      KunnrY2: '50000125',
      AddressY2: 'Aspen Oss bv Kloosterstraat 6 5349 AB OSS',
      KunnrWe: '50000125',
      AddressWe: 'Aspen Oss bv Kloosterstraat 6 5349 AB OSS',
      SelectedDoc: '123123',
      Erdat: '/Date(1754092800000)/',
      InvoiceUrl: '0090000354_CE.PDF',
      TypeDoc: '',
      Document: '',
      DocumentItem: '000000',
      Yycolldate: null,
      TfsKey: '123123',
      Yc1Form: '123123',
      NameY0: 'Indaver Antwerpen nv',
      NameYe: '12123',
      Yvdatdes: '/Date(1754092800000)/',
      Yeural: '',
      ScannedErdat: '/Date(1754092800000)/',
      Dareg: null,
      Dalbg: null,
      EndTreatCntr: '',
      Vdatu: null,
      Vkorg: '1600',
      Pdf: '',
      LifnrY0: 'IC1601'
    }
  }

  build (): SapGetCertificateIndexResponse {
    return this.response
  }
}
