import { randomUUID } from 'crypto'
import { randAbbreviation, randBoolean, randCompanyName, randEmail, randFirstName, randFullAddress, randFullName, randFutureDate, randLastName, randNumber, randPastDate, randProductMaterial, randSentence } from '@ngneat/falso'
import { SapGetWasteInquiryIndexResponse } from '../get-waste-inquiry-index.response.js'
import { mapSapValueToWasteInquiryStatus } from '../../../../waste-inquiry/enums/waste-inquiry-status.enum.js'

export class SapGetWasteInquiryIndexResponseBuilder {
  private response: SapGetWasteInquiryIndexResponse

  constructor () {
    this.reset()
  }

  reset (): this {
    const status = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L'][randNumber({ min: 0, max: 11 })]
    this.response = {
      PstRequestId: randomUUID(),
      InquiryNumber: randNumber({ min: ********, max: ******** }).toString(),
      QuotationNumber: randNumber({ min: ********, max: ******** }).toString(),
      RequestDate: randPastDate().toLocaleDateString().toString(),
      RequestCategory: '4',
      RequestCategoryText: 'New waste request',
      RequestType: '',
      RequestTypeText: '',
      OutletManager: '0',
      OutletManagerName: '',
      AssignedTo: '',
      Customer: randNumber({ min: ********, max: ******** }).toString(),
      CustomerName: randCompanyName(),
      CustomerAddress: randFullAddress(),
      SalesOrganization: randNumber({ min: 1000, max: 9999 }).toString(),
      SalesOrganizationName: randCompanyName(),
      SalesAccount: randNumber({ min: 1, max: 9 }).toString(),
      SalesAccountName: randFullName(),
      PickUpAddress: randNumber({ min: ********, max: ******** }).toString(),
      PickUpAddressName: randCompanyName(),
      PickUpAddressAddress: randFullAddress(),
      WasteProducer: randNumber({ min: ********, max: ******** }).toString(),
      WasteProducerName: randCompanyName(),
      WasteProducerAddress: randFullAddress(),
      Notifier: randNumber({ min: ********, max: ******** }).toString(),
      NotifierName: randCompanyName(),
      HazardCommod: 'C',
      HazardCommodText: 'Commodity',
      Requestor: randAbbreviation(),
      RequestorName: randFullName(),
      RequestTitle: randProductMaterial(),
      RequestDescription: randSentence(),
      ContractExists: false,
      Contract: '',
      ContractItem: '0',
      Priority: '2',
      PriorityText: 'Medium',
      Sample: false,
      SampleQuantity: '0',
      ShortRequestStatus: '',
      EsnNumber: '',
      AsnNumber: '',
      NewRequestStatus: status,
      NewRequestStatusText: mapSapValueToWasteInquiryStatus(status).toString(),
      NewRequestType: '2',
      NewRequestTypeText: 'New waste stream',
      SalesStatus: '',
      SalesStatusText: '',
      TransportArranged: randBoolean(),
      TransportRegulated: '',
      TransportRegulatedText: '',
      TransportStatus: '',
      TransportStatusText: '',
      TransportType: '',
      TransportTypeText: '',
      ServiceStatus: '',
      ServiceStatusText: '',
      ServiceMaterial: '',
      ServiceMaterialDescription: '',
      WasteFlowManager: '0',
      WasteMaterial: randNumber({ min: 100000, max: 999999 }).toString(),
      WasteMaterialCustomer: '',
      WasteName: randProductMaterial(),
      WasteMaterialDescription: randSentence(),
      WasteType: '1',
      WasteTypeText: 'Bulk',
      CriticalWaste: false,
      WasteStatus: '1',
      WasteStatusText: 'In Progress',
      InternalScreening: false,
      FrequencyDischarge: '1',
      FrequencyDischargeText: 'Once-off stream',
      QuantityYear: randNumber({ min: 1, max: 1000 }),
      QuantityYearUom: 'TO',
      QuantityYearRemarks: '',
      PackagingMaterial: '',
      PackagingMaterialDescription: '',
      Sds: false,
      AnalysisReport: false,
      Flashpoint: '2',
      FlashpointText: '23° - 60°',
      Ph: '3',
      PhText: '4 - 10',
      Gravity: 0.0,
      Temperature: '1',
      TemperatureText: 'Ambient',
      MinTemperature: 0,
      MaxTemperature: 0,
      CollectionDate: randFutureDate().toLocaleDateString().toString(),
      CollectionQuantity: randNumber({ min: 1, max: 1000 }),
      CollectionUom: 'TO',
      LoadingType: '',
      LoadingTypeText: '',
      LoadingMethod: '',
      LoadingMethodText: '',
      StoredIn: '',
      StoredInText: '',
      Volume: 0.00,
      VolumeUom: '',
      UnCode: '',
      PackingGroup: '',
      HazardInducer1: '',
      HazardInducer2: '',
      HazardInducer3: '',
      ClassificationFinished: false,
      SalesEwc: '',
      CurrentWasteContractor: '',
      ProjectOnSite: randBoolean(),
      ProjectStart: null,
      ProjectEnd: null,
      LocationOnSite: '',
      Duration: 0,
      ProposedTreatmentCenter: '',
      SccwRelevant: false,
      MaterialDedicated: '',
      MaterialDedicatedText: '',
      NumberOfContainers: '',
      NumberOfContainersText: '',
      ContainerLeakproof: false,
      CraneRequired: false,
      FullLoad: false,
      PriceReceptables: false,
      EwcWt: '',
      ExpectedEnd: null,
      StateOfMatter: '01',
      StateOfMatterRemarks: '',
      InformationDelivery: '',
      SalesOffice: 'GEN',
      PriceAgreementResponsible: '',
      SapSetupRequired: false,
      MeansOfTransportation: '',
      SpecialProcessIndicator: '',
      ValidFrom: null,
      ValidUntil: null,
      TreatmentCenter: randNumber({ min: ********, max: ******** }).toString(),
      TreatmentCenterName: randCompanyName(),
      Installation: '',
      InstallationName: '',
      Transporter: '',
      FreightForwarder: '',
      ServiceProvider: '',
      ProfitCenter: '',
      MaterialGroup: '',
      ConditionType: '',
      ValuationClass: '',
      CurrentSupplier: '',
      CurrentPackaging: '',
      BuyRent: '',
      EstimatedQuantity: 0.00,
      EstimatedQuantityUom: '',
      Frequency: '',
      KeyCombination: '',
      TankOwnedCustomer: false,
      RequiredForCollection: '',
      LoadingByIndaver: false,
      FirstNameCustomerZone: randFirstName(),
      LastNameCustomerZone: randLastName(),
      EmailCustomerZone: randEmail(),
      TypeOfSvhc: '',
      PersistentSubstance: '',
      CommentSubjectHazard: '',
      CommentSubjectLegislation: '',
      SalesResponsible: randNumber({ min: 1, max: 4 }).toString(),
      Adr: false,
      InPrice: 0.00,
      Currency: '',
      Unit: '',
      ProjectPriceAgreement: '',
      Niedersachsen: false,
      RejectionReason: '',
      LoadingUnloadingIncluded: 0.00,
      LoadingUnloadingIncludedUom: '',
      TypeOfPriority: '',
      WhereEmptyContainer: '',
      RemarksTransport: '',
      CreatedByName: randFullName(),
      CreatedByEmail: randEmail(),
      WicId: '',
      WicUrl: '',
      EwcCustomerLevel1: '01',
      EwcCustomerLevel2: '01',
      EwcCustomerLevel3: '01',
      SalesEwcLevel1: '',
      SalesEwcLevel2: '',
      SalesEwcLevel3: '',
      EwcWtLevel1: '',
      EwcWtLevel2: '',
      EwcWtLevel3: '',
      LastChangeAt: randPastDate().toISOString(),
      ChangedBy: randAbbreviation(),
      CreatedBy: randAbbreviation(),
      CreatedAt: randPastDate().toISOString(),
      SAP__Messages: []
    }

    return this
  }

  withInquiryNumber (inquiryNumber: string): this {
    this.response.InquiryNumber = inquiryNumber
    return this
  }

  build (): SapGetWasteInquiryIndexResponse {
    const result = this.response

    this.reset()

    return result
  }
}
