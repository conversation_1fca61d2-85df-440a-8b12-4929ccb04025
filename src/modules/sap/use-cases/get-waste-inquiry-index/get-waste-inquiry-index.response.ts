import { SapTypeOfWasteRequestType } from '../../types/type-of-waste-request.type.js'
import { SapUomType } from '../../types/uom.type.js'
import { SapNumberPackGroup, SapPackagingType, SapWasteSubjectHazardCode, SapWasteSubjectLegislationCode } from '../create-waste-inquiry/create-waste-inquiry-sap.command.js'

export interface SapGetWasteInquiryIndexResponse {
  PstRequestId?: string
  InquiryNumber?: string
  QuotationNumber?: string
  RequestDate?: string
  RequestCategory?: string
  RequestCategoryText?: string
  RequestType?: string
  RequestTypeText?: string
  OutletManager?: string
  OutletManagerName?: string
  AssignedTo?: string
  Customer?: string
  CustomerName?: string
  CustomerAddress?: string
  SalesOrganization?: string
  SalesOrganizationName?: string
  SalesAccount?: string
  SalesAccountName?: string
  PickUpAddress?: string
  PickUpAddressName?: string
  PickUpAddressAddress?: string
  WasteProducer?: string
  WasteProducerName?: string
  WasteProducerAddress?: string
  Notifier?: string
  NotifierName?: string
  HazardCommod?: string
  HazardCommodText?: string
  Requestor?: string
  RequestorName?: string
  RequestTitle?: string
  RequestDescription?: string
  ContractExists?: boolean
  Contract?: string
  ContractItem?: string
  Priority?: string
  PriorityText?: string
  Sample?: boolean
  SampleQuantity?: string
  ShortRequestStatus?: string
  EsnNumber?: string
  AsnNumber?: string
  NewRequestStatus?: string
  NewRequestStatusText?: string
  NewRequestType?: SapTypeOfWasteRequestType | ''
  NewRequestTypeText?: string
  SalesStatus?: string
  SalesStatusText?: string
  TransportArranged?: boolean
  TransportRegulated?: string | null
  TransportRegulatedText?: string
  TransportStatus?: string
  TransportStatusText?: string
  TransportType?: string
  TransportTypeText?: string
  ServiceStatus?: string
  ServiceStatusText?: string
  ServiceMaterial?: string
  ServiceMaterialDescription?: string
  WasteFlowManager?: string
  WasteMaterial?: string
  WasteMaterialCustomer?: string
  WasteName?: string
  WasteMaterialDescription?: string
  WasteType?: string | null
  WasteTypeText?: string
  CriticalWaste?: boolean
  WasteStatus?: string
  WasteStatusText?: string
  InternalScreening?: boolean
  FrequencyDischarge?: string
  FrequencyDischargeText?: string
  QuantityYear?: number
  QuantityYearUom?: SapUomType | ''
  QuantityYearRemarks?: string
  PackagingMaterial?: string
  PackagingMaterialDescription?: string
  Sds?: boolean
  AnalysisReport?: boolean
  Flashpoint?: string
  FlashpointText?: string
  Ph?: string
  PhText?: string
  Gravity?: number
  Temperature?: string | null
  TemperatureText?: string
  MinTemperature?: number
  MaxTemperature?: number
  CollectionDate?: string | null
  CollectionQuantity?: number
  CollectionUom?: SapUomType | ''
  LoadingType?: string
  LoadingTypeText?: string
  LoadingMethod?: string
  LoadingMethodText?: string
  StoredIn?: string
  StoredInText?: string
  Volume?: number
  VolumeUom?: SapUomType | ''
  UnCode?: string
  PackingGroup?: string
  HazardInducer1?: string
  HazardInducer2?: string
  HazardInducer3?: string
  ClassificationFinished?: boolean
  SalesEwc?: string
  CurrentWasteContractor?: string
  ProjectOnSite?: boolean
  ProjectStart?: string | null
  ProjectEnd?: string | null
  LocationOnSite?: string
  Duration?: number
  ProposedTreatmentCenter?: string
  SccwRelevant?: boolean
  MaterialDedicated?: string
  MaterialDedicatedText?: string
  NumberOfContainers?: string
  NumberOfContainersText?: string
  ContainerLeakproof?: boolean
  CraneRequired?: boolean
  FullLoad?: boolean
  PriceReceptables?: boolean
  EwcWt?: string
  ExpectedEnd?: string | null
  StateOfMatter?: string
  StateOfMatterRemarks?: string
  InformationDelivery?: string
  SalesOffice?: string
  PriceAgreementResponsible?: string
  SapSetupRequired?: boolean
  MeansOfTransportation?: string
  SpecialProcessIndicator?: string
  ValidFrom?: string | null
  ValidUntil?: string | null
  TreatmentCenter?: string
  TreatmentCenterName?: string
  Installation?: string
  InstallationName?: string
  Transporter?: string
  FreightForwarder?: string
  ServiceProvider?: string
  ProfitCenter?: string
  MaterialGroup?: string
  ConditionType?: string
  ValuationClass?: string
  CurrentSupplier?: string
  CurrentPackaging?: string
  BuyRent?: string | null
  EstimatedQuantity?: number
  EstimatedQuantityUom?: string
  Frequency?: string
  KeyCombination?: string
  TankOwnedCustomer?: boolean
  RequiredForCollection?: string
  LoadingByIndaver?: boolean
  FirstNameCustomerZone?: string
  LastNameCustomerZone?: string
  EmailCustomerZone?: string
  TypeOfSvhc?: string
  PersistentSubstance?: string
  CommentSubjectHazard?: string
  CommentSubjectLegislation?: string
  SalesResponsible?: string
  Adr?: boolean
  InPrice?: number
  Currency?: string
  Unit?: string
  ProjectPriceAgreement?: string
  Niedersachsen?: boolean
  RejectionReason?: string
  LoadingUnloadingIncluded?: number
  LoadingUnloadingIncludedUom?: string
  OvertimeRate?: number
  TypeOfPriority?: string
  WhereEmptyContainer?: string
  RemarksTransport?: string
  CreatedByName?: string
  CreatedByEmail?: string
  WicId?: string
  WicUrl?: string
  EwcCustomer?: string
  EwcCustomerLevel1?: string
  EwcCustomerLevel2?: string
  EwcCustomerLevel3?: string
  SalesEwcLevel1?: string
  SalesEwcLevel2?: string
  SalesEwcLevel3?: string
  EwcWtLevel1?: string
  EwcWtLevel2?: string
  EwcWtLevel3?: string
  LastChangeAt?: string
  ChangedBy?: string
  CreatedBy?: string
  CreatedAt?: string
  SAP__Messages?: string[]
  _component?: Array<{
    ComponentId?: string
    PstRequestId?: string
    Component?: string
    MinWeight?: number
    MaxWeight?: number
    LastChangeAt?: string
    ChangedBy?: string
    CreatedBy?: string
    CreatedAt?: string
    SAP__Messages?: string[]
  }>
  _contact?: Array<{
    ContactId?: string
    PstRequestId?: string
    FirstName?: string
    LastName?: string
    Email?: string
    Source?: string
    LastChangeAt?: string
    ChangedBy?: string
    CreatedBy?: string
    CreatedAt?: string
    SAP__Messages?: string[]
  }>
  _packaging?: Array<{
    PackagingId?: string
    PstRequestId?: string
    TypePackaging?: SapPackagingType
    SizePackaging?: string
    CommentPackaging?: string
    InnerPackaging?: boolean
    WeightPiece?: number
    WeightPieceUom?: SapUomType
    LastChangeAt?: string
    ChangedBy?: string
    CreatedBy?: string
    CreatedAt?: string
    SAP__Messages?: string[]
  }>
  _unNumber?: Array<{
    UnNumberId?: string
    PstRequestId?: string
    UnNumber?: string
    UnNumberPackGroup?: SapNumberPackGroup
    UnNumberApprovalCode?: string
    LastChangeAt?: string
    ChangedBy?: string
    CreatedBy?: string
    CreatedAt?: string
    SAP__Messages?: string[]
  }>
  _wasteSubjectLegislation?: Array<{
    WasteSubjectId?: string
    PstRequestId?: string
    WasteSubjectCode?: SapWasteSubjectLegislationCode
    WasteSubjectCodeText?: string
    WasteSubjectType?: string
    WasteSubjectCodeValid?: boolean
    SortOrder?: string
    ImageUrl?: string
    LastChangeAt?: string
    ChangedBy?: string
    CreatedBy?: string
    CreatedAt?: string
    SAP__Messages?: string[]
  }>
  _wasteSubjectHazard?: Array<{
    WasteSubjectId?: string
    PstRequestId?: string
    WasteSubjectCode?: SapWasteSubjectHazardCode
    WasteSubjectCodeText?: string
    WasteSubjectType?: string
    WasteSubjectCodeValid?: boolean
    SortOrder?: string
    ImageUrl?: string
    LastChangeAt?: string
    ChangedBy?: string
    CreatedBy?: string
    CreatedAt?: string
    SAP__Messages?: string[]
  }>
}
