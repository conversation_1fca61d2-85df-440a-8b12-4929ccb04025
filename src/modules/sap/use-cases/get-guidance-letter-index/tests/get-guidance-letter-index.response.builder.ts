import { randCompanyName, randNumber, randPastDate } from '@ngneat/falso'
import { SAP_ENDPOINTS } from '../../../constants/endpoint.constants.js'
import { SapGetGuidanceLetterIndexResponse } from '../get-guidance-letter-index.response.js'

export class SapGetGuidanceLetterIndexResponseBuilder {
  private response: SapGetGuidanceLetterIndexResponse

  constructor () {
    this.reset()
  }

  reset (): this {
    this.response = {
      __metadata: {
        id: `https://services.odata.org/OData${SAP_ENDPOINTS.GUIDANCE_LETTER.INDEX}`,
        uri: `https://services.odata.org/OData${SAP_ENDPOINTS.GUIDANCE_LETTER.INDEX}`,
        type: 'ZCZ_SRV.guidanceLetter'
      },
      TorId: randNumber({ min: 10000000, max: 99999999 }).toString(),
      Odf: '',
      PdfYbeg: '',
      Attachment: '',
      PdfYdg: '',
      Reqno: randNumber({ min: 10000000, max: 99999999 }).toString(),
      Vbeln: randNumber({ min: 10000000, max: 99999999 }).toString(),
      Arktx: '',
      ZcsKwmeng: randNumber({ min: 100, max: 10000 }).toString(),
      Vrkme: 'TO',
      Vdatu: randPastDate().toISOString(),
      Kunnr: randNumber({ min: 10000000, max: 99999999 }).toString(),
      KunnrName: randCompanyName(),
      KunnrY2: randNumber({ min: 10000000, max: 99999999 }).toString(),
      KunnrY2Name: randCompanyName(),
      KunnrWe: randNumber({ min: 10000000, max: 99999999 }).toString(),
      KunnrWeName: randCompanyName(),
      KunnrDisplayName: randCompanyName(),
      KunnrWeDisplayName: randCompanyName(),
      KunnrY2DisplayName: randCompanyName(),
      PdfYdgContent: '',
      PdfYbegContent: ''
    }

    return this
  }

  withTorId (torId: string): this {
    this.response.TorId = torId
    return this
  }

  withPdfYdgContent (content: string): this {
    this.response.PdfYdgContent = content
    return this
  }

  withPdfYbegContent (content: string): this {
    this.response.PdfYbegContent = content
    return this
  }

  withKunnr (kunnr: string): this {
    this.response.Kunnr = kunnr
    return this
  }

  build (): SapGetGuidanceLetterIndexResponse {
    const result = this.response

    this.reset()

    return result
  }
}
