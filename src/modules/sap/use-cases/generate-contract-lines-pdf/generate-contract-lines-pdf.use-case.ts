import { Inject, Injectable } from '@nestjs/common'
import { AxiosInstance } from 'axios'
import { SAP_CLIENT } from '../../sap-client.provider.js'
import { SapDResponse } from '../../shared/sap-d.response.js'
import { SAP_ENDPOINTS } from '../../constants/endpoint.constants.js'
import { SapLanguageMapper } from '../../services/sap-language.mapper.js'
import { getCurrentLanguage } from '../../../localization/helpers/translate.helper.js'
import { SapGenerateContractLinesPdfCommand } from './generate-contract-lines-pdf.command.js'
import { SapGenerateContractLinesPdfResponse } from './generate-contract-lines-pdf.response.js'

@Injectable()
export class SapGenerateContractLinesPdfUseCase {
  constructor (
    @Inject(SAP_CLIENT) private client: AxiosInstance
  ) {}

  async execute (
    command: SapGenerateContractLinesPdfCommand
  ): Promise<SapGenerateContractLinesPdfResponse> {
    const url = SAP_ENDPOINTS.CONTRACT_LINE.GENERATE_PDF
    const params = {
      'sap-language': SapLanguageMapper.fromLocale(getCurrentLanguage()),
      'selection': `'${JSON.stringify(command.selection)}'`
    }

    const response = await this.client.get<SapDResponse<SapGenerateContractLinesPdfResponse>>(
      url.toString(),
      {
        params
      }
    )

    return response.data.d
  }
}
