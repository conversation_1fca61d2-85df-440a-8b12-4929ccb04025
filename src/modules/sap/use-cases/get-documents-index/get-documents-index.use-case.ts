import { Inject, Injectable } from '@nestjs/common'
import { AxiosInstance } from 'axios'
import { SAP_CLIENT } from '../../sap-client.provider.js'
import { SAP_ENDPOINTS } from '../../constants/endpoint.constants.js'
import { SapDResultResponse } from '../../shared/sap-d-result.response.js'
import { SapQuery } from '../../query/sap-query.js'
import { SapGetDocumentResponse } from './get-documents-index.response.js'
import { SapGetDocumentObject } from './get-document-sap-object.enum.js'

@Injectable()
export class SapGetDocumentsIndexUseCase {
  private readonly expandValue = 'DocToAttributeNav'

  constructor (
    @Inject(SAP_CLIENT) private client: AxiosInstance
  ) {}

  async execute (
    id: string,
    sabObject: SapGetDocumentObject
  ): Promise<SapGetDocumentResponse[]> {
    const url = new SapQuery<SapGetDocumentResponse>()
      .where('SapObject', sabObject)
      .andWhere('ObjectId', id)
      .loadRelation(this.expandValue)
      .buildUrl(SAP_ENDPOINTS.DOCUMENT.INDEX)

    const response = await this.client.get<SapDResultResponse<SapGetDocumentResponse[]>>(
      url
    )

    return response.data.d.results
  }
}
