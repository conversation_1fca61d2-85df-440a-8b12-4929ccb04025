import { randWord } from '@ngneat/falso'
import { SAP_ENDPOINTS } from '../../../constants/endpoint.constants.js'
import { ArchiveLinkAttribute } from '../get-documents-index.response.js'

export class SapGetDocumentsIndexAttributeResponseBuilder {
  private attribute: ArchiveLinkAttribute

  constructor () {
    this.attribute = {
      __metadata: {
        id: `https://services.odata.org/OData${SAP_ENDPOINTS.DOCUMENT.INDEX}`,
        uri: `https://services.odata.org/OData${SAP_ENDPOINTS.DOCUMENT.INDEX}`,
        type: '/OTX/ALDS_ODATA_SRV.ArchiveLinkAttribute'
      },
      ExtAttrType: 'ZSD_SW',
      FieldScale: 0,
      FieldType: 'CHAR',
      SapObject: 'BUS2000108',
      ExtAttribute: 'ZDOC_TYPE',
      FieldLength: 10,
      ObjectId: '123',
      ArchivId: '123',
      FieldLabel: 'Document Type',
      ArcDocId: '123',
      ArObject: 'DRAW',
      FieldName: 'DOC_TYPE',
      FieldValue: randWord(),
      CheckTable: false,
      CheckTableError: ''
    }
  }

  withFieldLabel (label: string): this {
    this.attribute.FieldLabel = label
    return this
  }

  withFieldValue (value: string): this {
    this.attribute.FieldValue = value
    return this
  }

  withObjectId (id: string): this {
    this.attribute.ObjectId = id
    return this
  }

  build (): ArchiveLinkAttribute {
    return this.attribute
  }
}
