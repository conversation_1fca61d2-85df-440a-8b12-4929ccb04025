import { SapUomType } from '../../types/uom.type.js'

export type SapStateOfMatter = '01' | '02' | '03' | '04' | '05' | '06' | '07' | '99'
export type SapWasteType = '1' | '2'
export type SapFlashpoint = '1' | '2' | '3'
export type SapPh = '1' | '2' | '3' | '4'
export type SapTemperature = '1' | '2'
export type SapWasteSubjectLegislationCode = '00' | '01' | '02' | '03' | '04' | '05' | '06' | '07' | '08' | '09'
export type SapTypeOfSvhc = '1' | '2'
export type SapPersistentSubstance = '1' | '2'
export type SapWasteSubjectHazardCode = '00' | '01' | '02' | '03' | '04' | '05' | '06' | '07' | '08' | '09' | '10'
export type SapFrequencyDischarge = '1' | '2' | '3' | '4'
export type SapTransportRegulated = 'Y' | 'N' | 'U'
export type SapNumberPackGroup = '0' | '1' | '2' | '3'
export type SapPackagingType = '01' | '02' | '03' | '04' | '05' | '06' | '07' | '08' | '09'
export type SapTransportType = '1' | '2' | '3' | '4' | '5' | '6' | '7' | '8' | '9'
export type SapLoadingType = '1' | '2' | '3' | '4'
export type SapLoadingMethod = '1' | '2' | '3'
export type SapStoredIn = '1' | '2' | '3' | '4' | '5'
export type SapRequiredForCollection = '1' | '2' | '3'
export type SapRequestStatus = '2'
export type SapRequestCategory = '4'
export interface SapCreateWasteInquiryCommand {
  Customer?: string
  SalesOrganization?: string
  WasteProducer?: string
  PickUpAddress?: string
  WasteName?: string
  WasteMaterialDescription?: string
  EwcCustomerLevel1?: string
  EwcCustomerLevel2?: string
  EwcCustomerLevel3?: string
  StateOfMatter?: SapStateOfMatter
  WasteType?: SapWasteType
  Flashpoint?: SapFlashpoint
  Ph?: SapPh
  Gravity?: number
  Temperature?: SapTemperature
  MinTemperature?: number
  MaxTemperature?: number
  Sds?: boolean
  AnalysisReport?: boolean
  _component?: {
    Component?: string
    MinWeight?: number
    MaxWeight?: number
  }[]
  Sample?: boolean
  _wasteSubjectLegislation?: {
    WasteSubjectCode?: SapWasteSubjectLegislationCode
    WasteSubjectCodeValid?: boolean
  }[]
  TypeOfSvhc?: SapTypeOfSvhc
  PersistentSubstance?: SapPersistentSubstance
  CommentSubjectLegislation?: string
  _wasteSubjectHazard?: {
    WasteSubjectCode?: SapWasteSubjectHazardCode
    WasteSubjectCodeValid?: boolean
  }[]
  CommentSubjectHazard?: string
  QuantityYear?: number
  QuantityYearUom?: SapUomType
  CollectionQuantity?: number
  CollectionUom?: SapUomType
  FrequencyDischarge?: SapFrequencyDischarge
  CollectionDate?: string
  ExpectedEnd?: string
  InformationDelivery?: string
  TransportArranged?: boolean
  LoadingByIndaver?: boolean
  TransportRegulated?: SapTransportRegulated
  _unNumber?: {
    UnNumber?: string
    UnNumberPackGroup?: SapNumberPackGroup
  }[]
  _packaging?: {
    TypePackaging?: SapPackagingType
    SizePackaging?: string
    WeightPiece?: number
    WeightPieceUom?: SapUomType
    InnerPackaging?: boolean
    CommentPackaging?: string
  }[]
  TransportType?: SapTransportType
  LoadingType?: SapLoadingType
  LoadingMethod?: SapLoadingMethod
  StoredIn?: SapStoredIn
  Volume?: number
  VolumeUom?: SapUomType
  TankOwnedCustomer?: boolean
  RequiredForCollection?: SapRequiredForCollection
  RequestDescription?: string
  _contact?: {
    FirstName?: string
    LastName?: string
    Email?: string
  }[]
  FirstNameCustomerZone?: string
  LastNameCustomerZone?: string
  EmailCustomerZone?: string
  NewRequestStatus?: SapRequestStatus
  RequestCategory?: SapRequestCategory
}
