import { SapBulkType } from '../../types/bulk.type.js'
import { SapContainerTransportType } from '../../types/container-transport.type.js'
import { SapNumberBooleanType } from '../../types/number-boolean.type.js'
import { SapUomType } from '../../types/uom.type.js'
import { SapWasteType } from '../../types/waste.type.js'
import { SapXBooleanType } from '../../types/x-boolean.type.js'

export interface SapUpdatePickUpRequestItemCommand {
  Aedate?: string | null
  Yyesn?: string
  RequestedDate?: string
  ConfirmWic?: boolean
  Stext?: string
  Cont2Nr?: string
  ExtId?: string
  Cont2Cover?: string
  ReconciliationNr?: string
  Cont2Transport?: string
  ReqDateFrom?: string | null
  Cont2TranspTxt?: string
  ReqDateTo?: string | null
  Aename?: string
  Cont2Weight?: string
  Cont2Uom?: string
  Reqno?: string
  Cont1Volume?: string
  Cont2OrderNumb?: string
  Cont2CostCente?: string
  Reqpos?: string
  Cont2Yyun?: string
  EmailApplicant?: string
  Cont2Yypack?: string
  Vbeln?: string
  Cont2Tfsnumber?: string
  IdWeekPlanning?: string
  Cont2Serial?: string
  Posnr?: string
  BulkLoad?: string
  NrOPallets?: string
  BulkLoadTxt?: string
  Status?: string
  CombineReqno?: string
  Kunnr?: string
  CombineAnswer?: string
  ReturnPackaging?: boolean
  IdApplicant?: string
  KunnrY2?: string
  DgChanged?: boolean
  ReturnPackagingRemark?: string
  Cont2DgChanged?: boolean
  HazardInducers?: string
  KunnrWe?: string
  ReqDateLabel?: string
  EdiFlag?: boolean
  Yyklantmat?: string
  Matnr?: string
  Yymatnrdg?: string
  Aetime?: string
  Arktx?: string
  ConfirmedDate?: string | null
  TransportBy?: SapXBooleanType | ''
  TransportByTxt?: string
  NameApplicant?: string
  DateApplication?: string | null
  WasteType?: SapWasteType | ''
  WasteTypeTxt?: string
  OrderNumber?: string
  CostCenter?: string
  WeightVolume?: string
  WeightUom?: SapUomType | ''
  QuantityPallets?: string
  QuantityLabels?: string
  QuantityBarrels?: string
  TypeRecipient?: string
  QuanContainers?: string
  Cont1Nr?: string
  Cont1Cover?: SapNumberBooleanType | ''
  Cont1Transport?: SapContainerTransportType | ''
  Cont1TranspTxt?: string
  Cont1Weight?: string
  Cont1Uom?: string
  BulkType?: SapBulkType | ''
  BulkTypeTxt?: string
  Services?: string
  ServicesTxt?: string
  Explanation?: string
  SalesDoc?: string
  SalesDocitem?: string
  DeliveryDoc?: string
  Yyasn?: string
  Yyun?: string
  Yypackaginggrp?: string
  Tfsnumber?: string
  SerialNumber?: string
  ReasonCancel?: string
  CreateLangu?: string
  Dangerous?: string
  LifnrY0?: string
  Dgcl?: string
  Eural?: string
  ActionNr?: string
  YeLifnr?: string
  ConfDelDate?: string | null
  Hpn1?: string
  Hpn2?: string
  Hpn3?: string
  ContainerType?: string
  Vkorg?: string
}

export interface SapUpdatePickUpRequestCommand {
  Reqno: string
  ToDisposalRequest?: SapUpdatePickUpRequestItemCommand[]
  ToWMRComment?: {
    Reqno: string
    Comment: string
  }[]
  ToWMRContacts?: {
    Reqno: string
    Email: string
    Name1: string
    Name2: string
  }[]
  ToWMRAttachments?: {
    Reqno: string
    Posnr: string
    Filename: string
  }[]
  DeleteAllComments?: '' | 'X'
  DeleteAllContacts?: '' | 'X'
  DeleteAllAttachments?: '' | 'X'
}
