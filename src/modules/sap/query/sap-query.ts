import assert from 'assert'
import { PaginatedKeysetSearchQuery, PaginatedOffsetSearchQuery, SearchQuery, SortDirection } from '@wisemen/pagination'
import { FilterOperator } from '../enums/odata-filter-operator.enum.js'
import { OdataQueryParameters } from '../enums/odata-query-parameter.enum.js'
import { SapOdataResponseService } from '../services/sap-odata-response.service.js'
import { SapLanguage } from '../enums/sap-language.enum.js'
import { SapFilterGroup } from './types/sap-filter-group.js'
import { SapOrderTypeQuery } from './types/sap-order.type.js'
import { SapEndpoint } from './types/sap-endpoint.type.js'

export class SapQuery<T> {
  private _select?: Array<keyof T>
  private _filters?: SapFilterGroup<T>
  private _orderBy?: Array<{ column: keyof T, direction: 'asc' | 'desc' }>
  private _search?: string
  private _top?: number
  private _skip?: number
  private _skipToken?: string
  private _sorts?: SapOrderTypeQuery<T>
  private _expand?: string[]
  private _language?: SapLanguage

  constructor (
    query?: SearchQuery | PaginatedOffsetSearchQuery | PaginatedKeysetSearchQuery,
    sorts?: SapOrderTypeQuery<T>
  ) {
    if (!query) return

    this._sorts = sorts

    if (query instanceof PaginatedKeysetSearchQuery) {
      this._top = query.pagination?.limit

      if (typeof query.pagination?.key === 'object') {
        if (!('skipToken' in query.pagination.key!)) {
          throw new Error('SAP keyset pagination object must include skipToken parameter.')
        }
        this._skipToken = query.pagination?.key.skipToken as string
      }
      if (typeof query.pagination?.key === 'string') {
        this._skipToken = query.pagination?.key
      }
    }

    if (query instanceof PaginatedOffsetSearchQuery) {
      this._top = query.pagination?.limit
      this._skip = query.pagination?.offset
    }

    if (query.search !== undefined) {
      this._search = query.search
    }

    if (
      query.sort !== undefined
      && query.sort.length > 0
    ) {
      this._orderBy = query.sort.map((sort) => {
        assert(this._sorts !== undefined)
        return {
          column: this._sorts.keyMapper(sort.key as string),
          direction: sort.order === SortDirection.ASC ? 'asc' : 'desc'
        }
      })
    } else if (
      (query.sort === undefined || query.sort.length === 0)
      && this._sorts
      && this._sorts.defaultOrderBy !== undefined
    ) {
      this._orderBy = [{
        column: this._sorts.defaultOrderBy.column,
        direction: this._sorts.defaultOrderBy.direction
      }]
    }
  }

  get skipToken (): string | undefined {
    return this._skipToken
  }

  addSelect (fields: keyof T | Array<keyof T>): this {
    this._select ??= []

    if (Array.isArray(fields)) {
      this._select.push(...fields)
    } else {
      this._select.push(fields)
    }

    return this
  }

  where (
    fieldOrBrackets: keyof T | ((qb: SapFilterGroup<T>) => SapFilterGroup<T>),
    value?: string | number | boolean | null,
    operator: Exclude<FilterOperator, FilterOperator.AND | FilterOperator.OR> = FilterOperator.EQUAL
  ): this {
    this._filters ??= new SapFilterGroup<T>()

    if (typeof fieldOrBrackets === 'function') {
      const subQb = new SapFilterGroup<T>()
      fieldOrBrackets(subQb)
      this._filters.where(subQb)
    } else {
      if (value === undefined) {
        throw new Error('Value must be provided for a simple filter condition.')
      }
      this._filters.where(fieldOrBrackets, value, operator)
    }
    return this
  }

  andWhere (
    fieldOrBrackets: keyof T | ((qb: SapFilterGroup<T>) => SapFilterGroup<T>),
    value?: string | number | boolean | null,
    operator: Exclude<FilterOperator, FilterOperator.AND | FilterOperator.OR> = FilterOperator.EQUAL
  ): this {
    if (!this._filters) {
      this.where(fieldOrBrackets, value, operator)

      return this
    }

    if (typeof fieldOrBrackets === 'function') {
      const subQb = new SapFilterGroup<T>()
      fieldOrBrackets(subQb)
      this._filters.andWhere(subQb)
    } else {
      if (value === undefined) {
        throw new Error('Value must be provided for a simple filter condition.')
      }
      this._filters.andWhere(fieldOrBrackets, value, operator)
    }
    return this
  }

  orWhere (
    fieldOrBrackets: keyof T | ((qb: SapFilterGroup<T>) => SapFilterGroup<T>),
    value?: string | number | boolean | null,
    operator: Exclude<FilterOperator, FilterOperator.AND | FilterOperator.OR> = FilterOperator.EQUAL
  ): this {
    if (!this._filters) {
      this.where(fieldOrBrackets, value, operator)

      return this
    }

    if (typeof fieldOrBrackets === 'function') {
      const subQb = new SapFilterGroup<T>()
      fieldOrBrackets(subQb)
      this._filters.orWhere(subQb)
    } else {
      if (value === undefined) {
        throw new Error('Value must be provided for a simple filter condition.')
      }
      this._filters.orWhere(fieldOrBrackets, value, operator)
    }
    return this
  }

  resetOrderBy (): this {
    this._orderBy = []
    return this
  }

  addOrderBy (column: keyof T, direction: 'asc' | 'desc' = 'asc'): this {
    this._orderBy ??= []
    this._orderBy.push({ column, direction })

    return this
  }

  setTop (top: number): this {
    if (top < 0) {
      throw new Error('Top value must be a non-negative integer.')
    }
    this._top = top

    return this
  }

  setSkipToken (skipToken: string): this {
    this._skipToken = skipToken

    return this
  }

  loadRelation (relation: keyof T | Array<keyof T>): this {
    if (this._expand === undefined) {
      this._expand = []
    }

    if (Array.isArray(relation)) {
      this._expand.push(...relation.map(r => r as string))
      return this
    }

    this._expand.push(relation as string)
    return this
  }

  setLanguage (language: SapLanguage): this {
    this._language = language
    return this
  }

  buildUrl (baseUrl: SapEndpoint): string {
    const queryParams: string[] = []

    if (this._select && this._select.length > 0) {
      queryParams.push(`${OdataQueryParameters.SELECT}=${this._select.join(',')}`)
    }

    if (this._filters) {
      const odataVersion = SapOdataResponseService.getVersionFromEndpoint(baseUrl)
      const filterString = this._filters.buildFilterString(odataVersion)
      if (filterString) {
        queryParams.push(`${OdataQueryParameters.FILTER}=${filterString}`)
      }
    }

    if (this._orderBy && this._orderBy.length > 0) {
      const orderByString = this._orderBy.map(o => `${o.column.toString()} ${o.direction}`).join(',')
      queryParams.push(`${OdataQueryParameters.ORDER_BY}=${orderByString}`)
    }

    if (this._search !== undefined && this._search !== '') {
      queryParams.push(`${OdataQueryParameters.SEARCH}=${encodeURIComponent(this._search)}`)
    }

    if (this._top !== undefined) {
      queryParams.push(`${OdataQueryParameters.TOP}=${this._top.toString()}`)
    }

    if (this._skip !== undefined) {
      queryParams.push(`${OdataQueryParameters.SKIP}=${this._skip.toString()}`)
    }

    if (this._skipToken !== undefined) {
      queryParams.push(`${OdataQueryParameters.SKIP_TOKEN}=${this._skipToken}`)
    }

    if (this._expand !== undefined) {
      queryParams.push(`${OdataQueryParameters.EXPAND}=${this._expand.join(',')}`)
    }

    if (this._language !== undefined) {
      queryParams.push(`${OdataQueryParameters.LANGUAGE}=${this._language}`)
    }

    if (queryParams.length === 0) {
      return baseUrl
    }

    return `${baseUrl}?${queryParams.join('&')}`
  }

  getUniqueKey (): string {
    const keyParts = {
      select: this._select,
      filters: this._filters,
      orderBy: this._orderBy,
      top: this._top,
      skip: this._skip,
      skipToken: this._skipToken,
      search: this._search
    }

    const jsonString = JSON.stringify(keyParts)
    return Buffer.from(jsonString).toString('base64')
  }
}
