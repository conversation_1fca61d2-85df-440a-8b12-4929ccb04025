import dayjs from 'dayjs'

export class SapDateFormatterService {
  static parseDate (dateString: string | null): Date | null {
    if (dateString === null) return null

    const dateMatch = dateString.match(/\d+/)
    if (!dateMatch) return null

    const timestamp = Number(dateMatch[0])
    if (isNaN(timestamp)) return null

    return dayjs(timestamp).toDate()
  }

  static parseDateToString (dateString: string | null): string | null {
    if (dateString === null) return null

    const dateMatch = dateString.match(/\d+/)
    if (!dateMatch) return null

    const timestamp = Number(dateMatch[0])
    if (isNaN(timestamp)) return null

    return dayjs(timestamp).format('YYYY-MM-DD')
  }

  static toSapFilterDate (date: Date | null = new Date()): string | null {
    if (date === null) {
      return null
    }

    const dateWithoutTimezone = date.toISOString().slice(0, -1)

    return `datetime'${dateWithoutTimezone}'`
  }

  static toSapDate (date: Date | null = new Date()): string | null {
    if (date === null) {
      return null
    }

    return `/Date(${date.getTime()})/`
  }

  static toMutationDate (date: Date | string | null | undefined): string | null {
    if (date == null) {
      return null
    }

    if (typeof date === 'string') {
      if (!dayjs(date).isValid()) {
        return null
      }
    }

    return dayjs(date).format('YYYY-MM-DDTHH:mm')
  }
}
