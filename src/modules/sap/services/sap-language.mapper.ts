import { Locale } from '../../localization/enums/locale.enum.js'
import { SapLanguage } from '../enums/sap-language.enum.js'

export class SapLanguageMapper {
  static fromLocale (locale: Locale): SapLanguage {
    switch (locale) {
      case Locale.DE_DE:
        return SapLanguage.DE
      case Locale.NL_BE:
        return SapLanguage.NL
      case Locale.ES_ES:
        return SapLanguage.ES
      case Locale.FR_FR:
        return SapLanguage.FR
      case Locale.EN_GB:
        return SapLanguage.EN
      default:
        return SapLanguage.EN
    }
  }
}
