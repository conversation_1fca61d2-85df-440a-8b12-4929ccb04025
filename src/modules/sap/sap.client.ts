import { ConfigService } from '@nestjs/config'
import { captureException } from '@sentry/nestjs'
import axios, { AxiosError, AxiosInstance, AxiosResponse } from 'axios'
import { trace } from '@opentelemetry/api'
import { LogContext, OpenTelemetryLoggerService } from '../../utils/opentelemetry/modules/logger.service.js'
import { SAP_ENDPOINTS } from './constants/endpoint.constants.js'

export function createSapClient (
  configService: ConfigService,
  logService?: OpenTelemetryLoggerService
): AxiosInstance {
  const instance = axios.create({
    baseURL: configService.getOrThrow<string>('SAP_BASE_URL'),
    headers: {
      'content-type': 'application/json',
      'accept': 'application/json'
    },
    auth: {
      username: configService.getOrThrow<string>('SAP_USERNAME'),
      password: configService.getOrThrow<string>('SAP_PASSWORD')
    },
    params: {
      'sap-client': configService.getOrThrow<string>('SAP_CLIENT')
    }
  })

  instance.interceptors.request.use(
    async (config) => {
      config.headers['x-cz-trace-id'] = trace.getActiveSpan()?.spanContext()?.traceId

      if (config.method === 'get') {
        config.params = {
          ...(config.params as Record<string, string>),
          $format: 'json'
        }
      }

      if (config.method !== 'get' && config.headers['x-csrf-token'] === undefined) {
        const csrfResponse = await instance.get(SAP_ENDPOINTS.X_CSRF_TOKEN, {
          headers: {
            'x-csrf-token': 'Fetch'
          }
        })

        const csrfToken = csrfResponse.headers['x-csrf-token'] as string | undefined

        if (csrfToken === undefined) {
          throw new Error('Failed to retrieve CSRF token from SAP')
        }

        // Set the CSRF token in the request headers
        config.headers['x-csrf-token'] = csrfToken

        // If there are new cookies set by the SAP server, add them to the request headers
        const setCookieHeader = csrfResponse.headers['set-cookie']
        if (setCookieHeader !== undefined) {
          const newCookiesArray = setCookieHeader.map(cookieString => cookieString.split(';')[0])

          if (newCookiesArray.length > 0) {
            const newCookiesString = newCookiesArray.join('; ')

            const existingCookies = config.headers['cookie'] as string | undefined
            config.headers['cookie'] = existingCookies !== undefined
              ? `${existingCookies}; ${newCookiesString}`
              : newCookiesString
          }
        }
      }
      return config
    }
  )

  instance.interceptors.response.use(
    (response: AxiosResponse) => {
      if (logService) {
        logService.info({
          context: LogContext.SAP,
          body: {
            endpoint: `${response.config?.method?.toUpperCase()} ${response.config?.url?.split('?')[0]}`,
            status: response.status
          },
          attributes: {
            method: response.config?.method,
            url: response.config?.url,
            headers: response.config?.headers,
            params: response.config?.params,
            data: response.config?.data
          }
        })
      }

      return response
    },
    (error: AxiosError) => {
      captureException(error, {
        extra: {
          request: {
            method: error.config?.method,
            url: error.config?.url,
            headers: error.config?.headers,
            params: error.config?.params as unknown,
            data: error.config?.data as unknown
          },
          response: {
            status: error.response?.status,
            data: JSON.stringify(error.response?.data)
          }
        }
      })

      if (logService) {
        logService.error({
          context: LogContext.SAP,
          body: {
            status: error.response?.status,
            data: error.response?.data
          },
          attributes: {
            method: error.config?.method,
            url: error.config?.url,
            headers: error.config?.headers,
            params: error.config?.params,
            data: error.config?.data
          }
        })
      }

      throw error
    }
  )

  return instance
}
