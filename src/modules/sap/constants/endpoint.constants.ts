export const SAP_ENDPOINTS = {
  TEST: {
    EXAMPLE_V2: '/sap/opu/odata/sap/example',
    EXAMPLE_V4: '/sap/opu/odata4/sap/example'
  },
  CERTIFICATES: {
    INDEX: '/sap/opu/odata/sap/ZCZ_SRV/listCertificatesSet'
  },
  CONTAINER_TYPE: {
    INDEX: '/sap/opu/odata/sap/ZCZ_SRV/containerTypesSet'
  },
  CONTRACT_LINE: {
    INDEX: '/sap/opu/odata/sap/ZSC_ACWSS_POC_SRV/listSet',
    GENERATE_PDF: '/sap/opu/odata/sap/ZSC_ACWSS_POC_SRV/pdf'
  },
  CUSTOMER: {
    INDEX: '/sap/opu/odata4/sap/zapi_psc_presales_tool_o4/srvd_a2x/sap/zsd_psc_presales_tool_cz_m/0001/CustomerVH'
  },
  CUSTOMER_SALES_ORGANIZATION: {
    INDEX: '/sap/opu/odata4/sap/zapi_psc_presales_tool_o4/srvd_a2x/sap/zsd_psc_presales_tool_cz_m/0001/CustomerDefaultSalesOrg'
  },
  DOCUMENT: {
    UPLOAD: '/sap/opu/odata/OTX/ALDS_ODATA_SRV/ArchiveLinkDocumentContentSet',
    INDEX: '/sap/opu/odata/OTX/ALDS_ODATA_SRV/ArchiveLinkDocumentSet',
    CREATE: '/sap/opu/odata/OTX/ALDS_ODATA_SRV/$batch'
  },
  EWC_CODE: {
    INDEX: '/sap/opu/odata/sap/ZMDX_TAOF_SRV/wasteSet'
    // Example with parameter:
    // DETAIL: (id: string) => `/sap/opu/odata/sap/ZMDX_TAOF_SRV/wasteSet/${uuid}`,
  },
  GUIDANCE_LETTER: {
    INDEX: '/sap/opu/odata/sap/ZCZ_SRV/guidanceLetterSet'
  },
  PACKAGING_TYPE: {
    INDEX: '/sap/opu/odata/sap/ZCZ_SRV/packTypesSet'
  },
  PAYER: {
    INDEX: '/sap/opu/odata/sap/ZCZ_SRV/getPayers'
  },
  PICK_UP_ADDRESS: {
    INDEX: '/sap/opu/odata4/sap/zapi_psc_presales_tool_o4/srvd_a2x/sap/zsd_psc_presales_tool_cz_m/0001/PickupAddressVH'
  },
  PICK_UP_REQUEST: {
    CREATE: '/sap/opu/odata/sap/ZCZ_SRV/CreateWMRSet',
    INDEX: '/sap/opu/odata/sap/ZCZ_SRV/listWMRSet',
    DETAIL: '/sap/opu/odata/sap/ZCZ_SRV/wmrItemsSet',
    INDASCAN_DETAIL: '/sap/opu/odata/sap/ZCZ_SRV/disposalRequestSet',
    DETAIL_COMMENT: '/sap/opu/odata/sap/ZCZ_SRV/wmrCommentSet',
    DETAIL_CONTACTS: '/sap/opu/odata/sap/ZCZ_SRV/wmrContactsSet',
    DETAIL_ATTACHMENTS: '/sap/opu/odata/sap/ZCZ_SRV/wmrAttachmentsSet',
    UPDATE: '/sap/opu/odata/sap/ZCZ_SRV/ChangeWMRSet'
  },
  INVOICE: {
    INDEX: '/sap/opu/odata/sap/ZCZ_SRV/listInvoiceSet',
    DRAFT_INDEX: '/sap/opu/odata/sap/ZCZ_SRV/listDraftInvoiceSet',
    APPROVE: {
      CUSTOMER: '/sap/opu/odata/sap/ZCZ_SRV/approveProformaByCustomer',
      SALES_REP: '/sap/opu/odata/sap/ZCZ_SRV/approveProformaBySales'
    },
    REJECT: {
      CUSTOMER: '/sap/opu/odata/sap/ZCZ_SRV/rejectProformaByCustomer',
      SALES_REP: '/sap/opu/odata/sap/ZCZ_SRV/rejectProformaBySales'
    }
  },
  IS_COST_CENTER_MANDATORY: {
    CHECK: '/sap/opu/odata/sap/ZCZ_SRV/isCostCenterMandatory'
  },
  IS_PO_REFERENCE_MANDATORY: {
    CHECK: '/sap/opu/odata/sap/ZCZ_SRV/isPoRefMandatory'
  },
  SALES_ORGANISATION: {
    INDEX: '/sap/opu/odata4/sap/zapi_psc_presales_tool_o4/srvd_a2x/sap/zsd_psc_presales_tool_cz_m/0001/SalesOrganizationVH'
  },
  TANKER_TYPE: {
    INDEX: '/sap/opu/odata/sap/ZCZ_SRV/bulkTypesSet'
  },
  TRANSPORT_TYPE: {
    INDEX: '/sap/opu/odata/sap/ZCZ_SRV/transportTypesSet'
  },
  UN_NUMBER: {
    INDEX: '/sap/opu/odata/sap/ZMDX_TAOF_SRV/unSet',
    INDEX_FOR_PICK_UP_REQUEST: '/sap/opu/odata/sap/ZMDX_TRACE_40_SRV/listSet'
  },
  WASTE_INQUIRY: {
    CREATE: '/sap/opu/odata4/sap/zapi_psc_presales_tool_o4/srvd_a2x/sap/zsd_psc_presales_tool_cz_m/0001/PstRequest',
    INDEX: '/sap/opu/odata4/sap/zapi_psc_presales_tool_o4/srvd_a2x/sap/zsd_psc_presales_tool_cz_m/0001/PstRequest'
  },
  WASTE_PRODUCER: {
    INDEX: '/sap/opu/odata4/sap/zapi_psc_presales_tool_o4/srvd_a2x/sap/zsd_psc_presales_tool_cz_m/0001/WasteProducerVH'
  },
  WEEKLY_PLANNING_REQUEST: {
    CREATE: '/sap/opu/odata/sap/ZCZ_SRV/getWeekPLID'
  },
  X_CSRF_TOKEN: '/sap/opu/odata/sap/ZCZ_SRV/transportTypesSet'
} as const
