import { afterEach, before, describe, it } from 'node:test'
import Sinon, { assert, createStubInstance, SinonStubbedInstance } from 'sinon'
import { expect } from 'expect'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { GenerateContractLinesPdfUseCase } from '../generate-contract-lines-pdf.use-case.js'
import { GenerateContractLinesPdfValidator } from '../generate-contract-lines-pdf.validator.js'
import { SapGenerateContractLinesPdfUseCase } from '../../../../sap/use-cases/generate-contract-lines-pdf/generate-contract-lines-pdf.use-case.js'
import { SapGenerateContractLinesPdfResponseBuilder } from '../../../../sap/use-cases/generate-contract-lines-pdf/tests/generate-contract-lines-pdf.response.builder.js'
import { ViewContractLineIndexUseCase } from '../../view-contract-line-index/view-contract-line-index.use-case.js'
import { EmptyContractLinesSelectionError } from '../../../errors/empty-contract-lines-selection.error.js'
import { GenerateContractLinesPdfQuerySelectionCommandBuilder } from './generate-contract-lines-pdf-query-selection.command.builder.js'
import { GenerateContractLinesPdfSelectionCommandBuilder } from './generate-contract-lines-pdf-selection.command.builder.js'

describe('GenerateContractLinesPdfUseCase - unit test', () => {
  let useCase: GenerateContractLinesPdfUseCase

  let validator: SinonStubbedInstance<GenerateContractLinesPdfValidator>
  let viewContractLineIndexUseCase: SinonStubbedInstance<ViewContractLineIndexUseCase>
  let generateContractLinesPdf: SinonStubbedInstance<SapGenerateContractLinesPdfUseCase>

  before(() => {
    TestBench.setupUnitTest()

    validator = createStubInstance(GenerateContractLinesPdfValidator)
    viewContractLineIndexUseCase = createStubInstance(ViewContractLineIndexUseCase)
    generateContractLinesPdf = createStubInstance(SapGenerateContractLinesPdfUseCase)

    useCase = new GenerateContractLinesPdfUseCase(
      validator,
      viewContractLineIndexUseCase,
      generateContractLinesPdf
    )

    mockMethods()
  })

  afterEach(() => {
    Sinon.resetHistory()

    mockMethods()
  })

  function mockMethods () {
    validator.validate.resolves()
    viewContractLineIndexUseCase.execute.resolves({
      items: [],
      meta: {
        limit: 100,
        offset: 0,
        total: 0
      }
    })
    generateContractLinesPdf.execute.resolves(
      new SapGenerateContractLinesPdfResponseBuilder().build()
    )
  }

  it ('Throws an error if no selection is provided', async () => {
    const command = new GenerateContractLinesPdfSelectionCommandBuilder().build()
    command.selection = undefined
    command.querySelection = undefined

    await expect(useCase.execute(command)).rejects.toThrow(EmptyContractLinesSelectionError)
  })

  describe('for selection', () => {
    it('Calls all methods once', async () => {
      const command = new GenerateContractLinesPdfSelectionCommandBuilder().build()

      await useCase.execute(command)

      assert.calledOnce(validator.validate)
      assert.calledOnce(generateContractLinesPdf.execute)
    })
  })

  describe('for query selection', () => {
    it('Calls all methods once', async () => {
      const command = new GenerateContractLinesPdfQuerySelectionCommandBuilder().build()

      await useCase.execute(command)

      assert.calledOnce(validator.validate)
      assert.calledOnce(viewContractLineIndexUseCase.execute)
      assert.calledOnce(generateContractLinesPdf.execute)
    })
  })
})
