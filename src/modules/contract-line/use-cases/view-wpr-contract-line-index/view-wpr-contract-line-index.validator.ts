import { Injectable } from '@nestjs/common'
import { AuthContext } from '../../../auth/auth.context.js'
import { CustomerWasteProducerAuthService } from '../../../auth/services/customer-waste-producer-auth.service.js'
import { UserWasteProducerAuthService } from '../../../auth/services/user-waste-producer-auth.service.js'
import { CustomerPickUpAddressAuthService } from '../../../auth/services/customer-pick-up-address-auth.service.js'
import { MissingRequiredFieldError } from '../../../exceptions/generic/missing-required-field.error.js'
import { WasteProducerNotAccessibleError } from '../../../waste-producer/errors/waste-producer-not-accessible.error.js'
import { PickUpAddressNotAccessibleError } from '../../../pick-up-address/errors/pick-up-address-not-accessible.error.js'
import { SelectedCustomerFilterMismatchError } from '../../../customer/errors/selected-customer-filter-mismatch.error.js'
import { ViewWprContractLineIndexQuery } from './view-wpr-contract-line-index.query.js'

@Injectable()
export class ViewWprContractLineIndexValidator {
  constructor (
    private readonly authContext: AuthContext,
    private readonly customerWasteProducerAuthService: CustomerWasteProducerAuthService,
    private readonly userWasteProducerAuthService: UserWasteProducerAuthService,
    private readonly customerPickUpAddressAuthService: CustomerPickUpAddressAuthService
  ) {}

  async validate (query: ViewWprContractLineIndexQuery): Promise<void> {
    this.validateCustomer(query.filter.customerId)

    if (query.filter?.wasteProducerId !== undefined) {
      if (query.filter.customerId === undefined) {
        throw new MissingRequiredFieldError({ pointer: '$.customerId' })
      }

      await this.validateWasteProducer(
        query.filter.customerId,
        query.filter.wasteProducerId
      )
    }
    if (query.filter?.pickUpAddressIds !== undefined) {
      if (query.filter.customerId === undefined) {
        throw new MissingRequiredFieldError({ pointer: '$.customerId' })
      }

      await this.validatePickUpAddresses(
        query.filter.customerId,
        query.filter.pickUpAddressIds
      )
    }
  }

  validateCustomer (customerId: string): void {
    const selectedCustomerId = this.authContext.getSelectedCustomerId()
    if (selectedCustomerId != null && selectedCustomerId !== customerId) {
      throw new SelectedCustomerFilterMismatchError({ pointer: '$.customerId' })
    }
  }

  private async validateWasteProducer (
    customerId: string,
    wasteProducerId: string
  ): Promise<void> {
    const canCustomerAccessWasteProducer = await this.customerWasteProducerAuthService
      .canCustomerAccessWasteProducer(
        customerId,
        wasteProducerId
      )

    if (!canCustomerAccessWasteProducer) {
      throw new WasteProducerNotAccessibleError({ pointer: '$.wasteProducerId' })
    }

    const userId = this.authContext.getAzureEntraUpn()

    const canUserAccessWasteProducer = await this.userWasteProducerAuthService
      .canUserAccessWasteProducer(
        userId,
        customerId,
        wasteProducerId
      )

    if (!canUserAccessWasteProducer) {
      throw new WasteProducerNotAccessibleError({ pointer: '$.wasteProducerId' })
    }
  }

  private async validatePickUpAddresses (
    customerId: string,
    pickUpAddressIds: string[]
  ): Promise<void> {
    if (pickUpAddressIds.length === 0) return

    const canCustomerAccessPickUpAddresses = await this.customerPickUpAddressAuthService
      .canCustomerAccessPickUpAddresses(
        customerId,
        pickUpAddressIds
      )

    if (!canCustomerAccessPickUpAddresses) {
      throw new PickUpAddressNotAccessibleError({ pointer: '$.pickUpAddressId' })
    }
  }
}
