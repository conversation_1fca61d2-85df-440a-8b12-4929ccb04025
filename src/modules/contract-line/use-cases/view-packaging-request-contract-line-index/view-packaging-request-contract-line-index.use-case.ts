import { Injectable } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { typeormPagination } from '@wisemen/pagination'
import { GetCustomerDefaultSalesOrganisationIdUseCase } from '../../../customer/use-cases/get-customer-default-sales-organisation-id/get-customer-default-sales-organisation-id.use-case.js'
import { SapGetContractLineIndexUseCase } from '../../../sap/use-cases/get-contract-line-index/get-contract-line-index.use-case.js'
import { MapContractLineSapService } from '../../services/map-contract-line.service.js'
import { SapGetContractLineIndexResponse } from '../../../sap/use-cases/get-contract-line-index/get-contract-line-index.response.js'
import { SapQuery } from '../../../sap/query/sap-query.js'
import { ViewPackagingRequestContractLineIndexValidator } from './view-packaging-request-contract-line-index.validator.js'
import { ViewPackagingRequestContractLineIndexQuery } from './query/view-packaging-request-contract-line-index.query.js'
import { ViewPackagingRequestContractLineIndexResponse } from './view-packaging-request-contract-line-index.response.js'

@Injectable()
export class ViewPackagingRequestContractLineIndexUseCase {
  constructor (
    private readonly configService: ConfigService,
    private readonly validator: ViewPackagingRequestContractLineIndexValidator,
    private readonly getCustomerSalesOrganisationId: GetCustomerDefaultSalesOrganisationIdUseCase,
    private readonly sapGetContractLineIndex: SapGetContractLineIndexUseCase
  ) {}

  public async execute (
    query: ViewPackagingRequestContractLineIndexQuery
  ): Promise<ViewPackagingRequestContractLineIndexResponse> {
    await this.validator.validate(query)

    const customerDefaultSalesOrganisationId = await this.getCustomerSalesOrganisationId
      .getOrganisationIdOrFail(
        query.filter.customerId
      )

    const sapQuery = this.getSapQuery(query, customerDefaultSalesOrganisationId)
    const sapResult = await this.sapGetContractLineIndex.execute(sapQuery)

    // const imageBaseUrl: string = await this.configService.getOrThrow(
    // 'PACKAGING_WASTE_MATERIAL_IMAGE_BASE_URL'
    // ) TODO: Uncomment when image base URL is available

    const imageBaseUrl = 'https://stgindaverpro.blob.core.windows.net/customerzone/recipients/'

    const contractLines = MapContractLineSapService.mapResultsToContractLines(
      sapResult,
      imageBaseUrl
    )

    const pagination = typeormPagination(query.pagination)
    return new ViewPackagingRequestContractLineIndexResponse(
      contractLines,
      null,
      pagination.take,
      pagination.skip
    )
  }

  private getSapQuery (
    query: ViewPackagingRequestContractLineIndexQuery,
    customerDefaultSalesOrganisationId: string
  ): SapQuery<SapGetContractLineIndexResponse> {
    const sapQuery = new SapQuery<SapGetContractLineIndexResponse>(query)
      .addSelect([
        'Vbeln',
        'Posnr',
        'Matnr',
        'MaterialDescription',
        'PackagingIndicator',
        'MaterialType'
      ])
      .where((qb) => {
        return qb.where('MaterialType', 'YPAC')
          .orWhere('MaterialType', 'YSER')
      })
      .andWhere('Kunnr', query.filter.customerId)
      .andWhere('Vkorg', customerDefaultSalesOrganisationId)
      .andWhere('KunnrY2', query.filter.wasteProducerId)

    if (
      query.filter.deliveryAddressIds !== undefined
      && query.filter.deliveryAddressIds.length > 0
    ) {
      const deliveryAddressIds = query.filter.deliveryAddressIds
      sapQuery.andWhere((qb) => {
        for (let i = 0; i < query.filter.deliveryAddressIds!.length; i++) {
          if (i === 0) {
            qb.where('KunnrWe', deliveryAddressIds[i])
          } else {
            qb.orWhere('KunnrWe', deliveryAddressIds[i])
          }
        }

        return qb
      })
    }

    return sapQuery
  }
}
