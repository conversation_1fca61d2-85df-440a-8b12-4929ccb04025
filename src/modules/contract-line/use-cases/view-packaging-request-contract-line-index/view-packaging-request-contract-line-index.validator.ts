import { Injectable } from '@nestjs/common'
import { UserCustomerAuthService } from '../../../auth/services/user-customer-auth.service.js'
import { UserWasteProducerAuthService } from '../../../auth/services/user-waste-producer-auth.service.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { CustomerWasteProducerAuthService } from '../../../auth/services/customer-waste-producer-auth.service.js'
import { CustomerPickUpAddressAuthService } from '../../../auth/services/customer-pick-up-address-auth.service.js'
import { DeliveryAddressNotAccessibleError } from '../../../packaging-request/errors/delivery-address-not-accessible.error.js'
import { WasteProducerNotAccessibleError } from '../../../waste-producer/errors/waste-producer-not-accessible.error.js'
import { SelectedCustomerFilterMismatchError } from '../../../customer/errors/selected-customer-filter-mismatch.error.js'
import { ViewPackagingRequestContractLineIndexQuery } from './query/view-packaging-request-contract-line-index.query.js'

@Injectable()
export class ViewPackagingRequestContractLineIndexValidator {
  constructor (
    private readonly authContext: AuthContext,
    private readonly userCustomerAuthService: UserCustomerAuthService,
    private readonly customerWasteProducerAuthService: CustomerWasteProducerAuthService,
    private readonly userWasteProducerAuthService: UserWasteProducerAuthService,
    private readonly customerPickUpAddressAuthService: CustomerPickUpAddressAuthService
  ) {}

  async validate (query: ViewPackagingRequestContractLineIndexQuery): Promise<void> {
    this.validateCustomer(query.filter.customerId)

    await this.validateWasteProducer(
      query.filter.customerId,
      query.filter.wasteProducerId
    )

    if (query.filter?.deliveryAddressIds !== undefined) {
      await this.validateDeliveryAddresses(
        query.filter.customerId,
        query.filter.deliveryAddressIds
      )
    }
  }

  validateCustomer (customerId: string): void {
    const selectedCustomerId = this.authContext.getSelectedCustomerId()
    if (selectedCustomerId != null && selectedCustomerId !== customerId) {
      throw new SelectedCustomerFilterMismatchError({ pointer: '$.customerId' })
    }
  }

  private async validateWasteProducer (
    customerId: string,
    wasteProducerId: string
  ): Promise<void> {
    const canCustomerAccessWasteProducer = await this.customerWasteProducerAuthService
      .canCustomerAccessWasteProducer(
        customerId,
        wasteProducerId
      )

    if (!canCustomerAccessWasteProducer) {
      throw new WasteProducerNotAccessibleError({ pointer: '$.wasteProducerId' })
    }

    const userId = this.authContext.getAzureEntraUpn()

    const canUserAccessWasteProducer = await this.userWasteProducerAuthService
      .canUserAccessWasteProducer(
        userId,
        customerId,
        wasteProducerId
      )

    if (!canUserAccessWasteProducer) {
      throw new WasteProducerNotAccessibleError({ pointer: '$.wasteProducerId' })
    }
  }

  private async validateDeliveryAddresses (
    customerId: string,
    deliveryAddressIds: string[]
  ): Promise<void> {
    const canCustomerAccessPickUpAddress = await this.customerPickUpAddressAuthService
      .canCustomerAccessPickUpAddresses(
        customerId,
        deliveryAddressIds
      )

    if (!canCustomerAccessPickUpAddress) {
      throw new DeliveryAddressNotAccessibleError({ pointer: '$.deliveryAddressIds' })
    }
  }
}
