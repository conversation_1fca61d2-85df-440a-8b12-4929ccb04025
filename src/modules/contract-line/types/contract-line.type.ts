import { ContractLinePackagingType } from '../enums/contract-line-packaging-type.enum.js'

export interface ContractLine {
  contractLineId: string

  contractNumber: string
  contractItem: string
  customerReference: string | null
  wasteMaterial: string | null
  materialNumber: string | null
  treatmentCenterName: string | null
  installationName: string | null
  customerId: string | null
  customerName: string | null
  wasteProducerId: string | null
  wasteProducerName: string | null
  pickUpAddressId: string | null
  pickUpAddressName: string | null
  asn: string | null
  tfs: boolean | null
  isHazardous: boolean | null
  packaged: ContractLinePackagingType | null
  tcNumber: string | null
  materialAnalysis: string | null
  ewcCode: string | null
  endTreatmentCenterId: string | null
  endTreatmentCenterName: string | null
  remarks: string | null
  processCode: string | null
  esnNumber: string | null
  deliveryInfo: string | null
  materialType: string | null
  packagingIndicator: string | null
  isSales: boolean | null
  imageUrl?: string
}
