import { Injectable } from '@nestjs/common'
import { SapGetContractLineIndexUseCase } from '../../sap/use-cases/get-contract-line-index/get-contract-line-index.use-case.js'
import { SapQuery } from '../../sap/query/sap-query.js'
import { SapGetContractLineIndexResponse } from '../../sap/use-cases/get-contract-line-index/get-contract-line-index.response.js'
import { ContractLineNotOfCustomerError } from '../errors/contract-line-not-of-customer.error.js'
import { ContractLineNotOfPickUpAddressesError } from '../errors/contract-line-not-of-pick-up-addresses.error.js'
import { ContractLineNotAccessibleError } from '../errors/contract-line-not-accessible.error.js'
import { ContractLineNotOfWasteProducersError } from '../errors/contract-line-not-of-waste-producers.error.js'

@Injectable()
export class ContractLineValidatorService {
  constructor (
    private readonly sapGetContractLineIndex: SapGetContractLineIndexUseCase
  ) {}

  async checkAccessContractLines (
    type: 'packaging' | 'pick-up',
    contractLineIdentifiers: {
      contractNumber: string // Vbeln
      contractItem: string // Posnr
      tcNumber?: string | null // ActionNr
    }[],
    userRestrictions?: {
      customerId?: string
      wasteProducerIds?: string[]
    },
    options?: {
      pickUpAddressIds?: string[]
    }
  ): Promise<void> {
    const sapQuery = this.buildSapQuery(type, contractLineIdentifiers, userRestrictions)
    const sapResult = await this.sapGetContractLineIndex.execute(sapQuery)

    for (const contractLineIdentifier of contractLineIdentifiers) {
      const foundContractLine = sapResult.find((contractItem) => {
        return contractItem.Vbeln === contractLineIdentifier.contractNumber
          && contractItem.Posnr === contractLineIdentifier.contractItem
          && (
            contractLineIdentifier.tcNumber == null
            || contractItem.ActionNr === contractLineIdentifier.tcNumber
          )
      })

      if (foundContractLine === undefined) {
        throw new ContractLineNotAccessibleError({
          contractNumber: contractLineIdentifier.contractNumber,
          contractLineNumber: contractLineIdentifier.contractItem,
          tcNumber: contractLineIdentifier.tcNumber ?? null
        })
      }

      if (
        userRestrictions?.customerId != null
        && foundContractLine.Kunnr != null
        && foundContractLine.Kunnr !== ''
        && foundContractLine.Kunnr !== userRestrictions?.customerId
      ) {
        throw new ContractLineNotOfCustomerError({
          customerId: userRestrictions.customerId,
          contractNumber: contractLineIdentifier.contractNumber,
          contractLineNumber: contractLineIdentifier.contractItem,
          tcNumber: contractLineIdentifier.tcNumber ?? null
        })
      }

      if (
        userRestrictions?.wasteProducerIds != null
        && userRestrictions.wasteProducerIds.length > 0
        && foundContractLine.KunnrY2 != null
        && foundContractLine.KunnrY2 !== ''
        && !userRestrictions.wasteProducerIds.includes(foundContractLine.KunnrY2)
      ) {
        throw new ContractLineNotOfWasteProducersError({
          wasteProducerIds: userRestrictions.wasteProducerIds,
          contractNumber: contractLineIdentifier.contractNumber,
          contractLineNumber: contractLineIdentifier.contractItem,
          tcNumber: contractLineIdentifier.tcNumber ?? null
        })
      }

      if (
        options?.pickUpAddressIds != null
        && options.pickUpAddressIds.length > 0
        && foundContractLine.KunnrWe != null
        && foundContractLine.KunnrWe !== ''
        && !options.pickUpAddressIds.includes(foundContractLine.KunnrWe)
      ) {
        throw new ContractLineNotOfPickUpAddressesError({
          pickUpAddressIds: options.pickUpAddressIds,
          contractNumber: contractLineIdentifier.contractNumber,
          contractLineNumber: contractLineIdentifier.contractItem,
          tcNumber: contractLineIdentifier.tcNumber ?? null
        })
      }
    }
  }

  private buildSapQuery (
    type: 'packaging' | 'pick-up',
    contractLineIdentifiers: {
      contractNumber: string
      contractItem: string
      tcNumber?: string | null
    }[],
    userRestrictions?: {
      customerId?: string | null
      wasteProducerIds?: string[]
    }
  ): SapQuery<SapGetContractLineIndexResponse> {
    const sapQuery = new SapQuery<SapGetContractLineIndexResponse>()
      .addSelect(['Vbeln', 'Posnr', 'ActionNr', 'Kunnr', 'KunnrWe'])

    if (type === 'packaging') {
      sapQuery.where((qb) => {
        return qb.where('MaterialType', 'YPAC')
          .orWhere('MaterialType', 'YSER')
      })
    } else if (type === 'pick-up') {
      sapQuery.where('MaterialType', 'YWAS')
    }

    sapQuery.andWhere((qb) => {
      for (let i = 0; i < contractLineIdentifiers.length; i++) {
        if (i === 0) {
          qb.where((qb1) => {
            qb1.where('Vbeln', contractLineIdentifiers[i].contractNumber)
              .andWhere('Posnr', contractLineIdentifiers[i].contractItem)

            if (contractLineIdentifiers[i].tcNumber != null) {
              qb1.andWhere('ActionNr', contractLineIdentifiers[i].tcNumber)
            }

            return qb1
          })
        } else {
          qb.orWhere((qb1) => {
            qb1.where('Vbeln', contractLineIdentifiers[i].contractNumber)
              .andWhere('Posnr', contractLineIdentifiers[i].contractItem)

            if (contractLineIdentifiers[i].tcNumber != null) {
              qb1.andWhere('ActionNr', contractLineIdentifiers[i].tcNumber)
            }

            return qb1
          })
        }
      }
      return qb
    })

    if (userRestrictions?.customerId != null) {
      sapQuery.andWhere('Kunnr', userRestrictions.customerId)
    }

    if (
      userRestrictions?.wasteProducerIds !== undefined
      && userRestrictions.wasteProducerIds.length > 0
    ) {
      const wasteProducerIds = userRestrictions.wasteProducerIds
      sapQuery.andWhere((qb) => {
        for (let i = 0; i < wasteProducerIds.length; i++) {
          if (i === 0) {
            qb.where('KunnrY2', wasteProducerIds[i])
          } else {
            qb.orWhere('KunnrY2', wasteProducerIds[i])
          }
        }
        return qb
      })
    }

    return sapQuery
  }
}
