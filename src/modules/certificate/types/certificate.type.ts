import { CertificateDocType } from '../enums/certificate-doc-type.enum.js'

export interface Certificate {
  docType: CertificateDocType | null
  salesOrder: string | null
  salesOrderLine: string | null
  description: string | null
  collectionDate: Date | null
  deliveryDate: Date | null
  dispositionPickUpDate: Date | null
  dispositionDeliveryDate: Date | null
  treatmentCentre: string | null
  endTreatmentCentre: string | null
  ewcCode: string | null
  wtfForm: string | null
  tfs: string | null
  disposalDate: Date | null
  printDate: Date | null
  wasteProducerName: string | null
  wasteProducerId: string | null
  contract: string | null
  contractItem: string | null
  fileName: string
  pickUpAddressName: string | null
  pickUpAddressId: string | null
  invoice: string | null
  customerId: string | null
  customerName: string | null
}
