export interface CertificateDynamicTableFields {
  docType: string | null
  salesOrder: string | null
  salesOrderLine: string | null
  description: string | null
  collectionDate: string | null
  deliveryDate: string | null
  dispositionPickUpDate: string | null
  dispositionDeliveryDate: string | null
  treatmentCentre: string | null
  endTreatmentCentre: string | null
  ewcCode: string | null
  wtfForm: string | null
  tfs: string | null
  disposalDate: string | null
  printDate: string | null
  wasteProducerName: string | null
  wasteProducerId: string | null
  contract: string | null
  contractItem: string | null
  invoice: string | null
  pickUpAddressName: string | null
  pickUpAddressId: string | null
  customerId: string | null
  customerName: string | null
}
