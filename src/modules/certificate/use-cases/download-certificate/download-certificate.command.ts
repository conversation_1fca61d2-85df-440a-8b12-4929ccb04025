import { ApiProperty } from '@nestjs/swagger'
import { IsEnum, IsNotEmpty, IsString } from 'class-validator'
import { CertificateTypeApiProperty, CertificateType } from '../../enums/certificate-type.enum.js'

export class DownloadCertificateCommand {
  @ApiProperty({ type: String })
  @IsString()
  @IsNotEmpty()
  fileName: string

  @CertificateTypeApiProperty()
  @IsEnum(CertificateType)
  type: CertificateType
}
