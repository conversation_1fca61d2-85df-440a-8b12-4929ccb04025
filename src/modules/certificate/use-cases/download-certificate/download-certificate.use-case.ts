import { HttpStatus, Injectable } from '@nestjs/common'
import { Response } from 'express'
import { AxiosError } from 'axios'
import { SharepointDownloadDocumentUseCase } from '../../../sharepoint/use-cases/download-document/download-document.use-case.js'
import { SharepointCommand } from '../../../sharepoint/types/sharepoint.command.js'
import { SharepointCommandBuilder } from '../../../sharepoint/builders/sharepoint-command.builder.js'
import { CertificateType } from '../../enums/certificate-type.enum.js'
import { SharepointLibraryNameEnvMapper } from '../../../sharepoint/mappers/sharepoint-library.mapper.js'
import { SHAREPOINT_LIBRARY } from '../../../sharepoint/constants/sharepoint-library.constant.js'
import { streamFileResponse } from '../../../../utils/streams/stream-file-response.js'
import { CertificateFileNotFoundError } from '../../errors/certificate-file-not-found.error.js'
import { DEFAULT_SHAREPOINT_SITE_TITLE } from '../../../sharepoint/constants/sharepoint-site-title.constant.js'
import { exhaustiveCheck } from '../../../../utils/helpers/exhaustive-check.helper.js'
import { DownloadCertificateCommand } from './download-certificate.command.js'

@Injectable()
export class DownloadCertificateUseCase {
  constructor (
    private sharepointDownloadUseCase: SharepointDownloadDocumentUseCase
  ) {}

  async execute (
    response: Response,
    command: DownloadCertificateCommand
  ): Promise<void> {
    const filesNames = this.getFileNames(command)
    const commands = filesNames.map(name => this.buildSharepointCommand(name))

    return commands.length === 1
      ? this.downloadCorFile(response, commands[0])
      : this.downloadCotCobFile(response, commands)
  }

  private buildSharepointCommand (
    fileName: string
  ): SharepointCommand {
    const libraryName = SharepointLibraryNameEnvMapper
      .toSharepointLibraryName(SHAREPOINT_LIBRARY.CERTIFICATE)

    return new SharepointCommandBuilder()
      .addFileName(fileName)
      .addLibraryName(libraryName)
      .addSiteTitle(DEFAULT_SHAREPOINT_SITE_TITLE)
      .build()
  }

  private getFileNames (command: DownloadCertificateCommand): string[] {
    switch (command.type) {
      case CertificateType.COR:
        return [`COR_${command.fileName}`]
      case CertificateType.COT_COB:
        return [
          `COT_${command.fileName}`,
          `COB_${command.fileName}`
        ]
      default:
        exhaustiveCheck(command.type)
    }
  }

  private async downloadCorFile (
    response: Response,
    command: SharepointCommand
  ): Promise<void> {
    try {
      const result = await this.sharepointDownloadUseCase.execute(command)
      return streamFileResponse(
        result.buffer,
        result.mimeType,
        result.fileName,
        response
      )
    } catch (error) {
      const axiosError = error as AxiosError

      if (axiosError.status === HttpStatus.NOT_FOUND) {
        throw new CertificateFileNotFoundError()
      }

      throw error
    }
  }

  private async downloadCotCobFile (
    response: Response,
    commands: SharepointCommand[]
  ): Promise<void> {
    const promises = commands.map(command => this.sharepointDownloadUseCase.execute(command))
    const promiseResults = await Promise.allSettled(promises)
    const successResponses = promiseResults.filter(r => r.status === 'fulfilled')

    if (successResponses.length === 0) {
      throw new CertificateFileNotFoundError()
    }

    const firstSuccessResponse = successResponses[0].value

    return streamFileResponse(
      firstSuccessResponse.buffer,
      firstSuccessResponse.mimeType,
      firstSuccessResponse.fileName,
      response
    )
  }
}
