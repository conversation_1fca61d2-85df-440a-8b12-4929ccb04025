import { Body, Controller, HttpCode, HttpStatus, Post, Res } from '@nestjs/common'
import { ApiTags, ApiOAuth2, ApiOkResponse } from '@nestjs/swagger'
import { Response } from 'express'
import { ApiNotFoundErrorResponse } from '../../../exceptions/api-errors/api-error-response.decorator.js'
import { CertificateFileNotFoundError } from '../../errors/certificate-file-not-found.error.js'
import { Permission } from '../../../permission/permission.enum.js'
import { Permissions } from '../../../permission/permission.decorator.js'
import { DownloadCertificateUseCase } from './download-certificate.use-case.js'
import { DownloadCertificateCommand } from './download-certificate.command.js'

@ApiTags('Certificate')
@ApiOAuth2([])
@Controller('certificates/download')
export class DownloadCertificateController {
  constructor (
    private readonly useCase: DownloadCertificateUseCase
  ) { }

  @Post()
  @HttpCode(HttpStatus.OK)
  @Permissions(Permission.CERTIFICATE_MANAGE, Permission.CERTIFICATE_READ)
  @ApiOkResponse()
  @ApiNotFoundErrorResponse(CertificateFileNotFoundError)
  public async downloadCertificate (
    @Res() response: Response,
    @Body() command: DownloadCertificateCommand
  ): Promise<void> {
    await this.useCase.execute(response, command)
  }
}
