import { after, before, describe, it } from 'node:test'
import request from 'supertest'
import { expect } from 'expect'
import { HttpStatus } from '@nestjs/common'
import { EndToEndTestSetup } from '../../../../../../test/setup/end-to-end-test-setup.js'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { TestUser } from '../../../../../app/users/tests/setup-user.type.js'
import { Permission } from '../../../../permission/permission.enum.js'
import { CertificateType } from '../../../enums/certificate-type.enum.js'
import { DownloadCertificateCommandBuilder } from './builders/download-certificate.command.builder.js'

describe('DownloadCertificate - E2E Tests', () => {
  let setup: EndToEndTestSetup
  let user: TestUser

  before(async () => {
    setup = await TestBench.setupEndToEndTest()
    user = await setup.authContext.getUser([Permission.CERTIFICATE_READ])
  })

  after(async () => await setup.teardown())

  it('downloads the certificate', async () => {
    const command = new DownloadCertificateCommandBuilder()
      .withFileName('123')
      .withType(CertificateType.COR)
      .build()

    const response = await request(setup.httpServer)
      .post(`/certificates/download`)
      .set('Authorization', `Bearer ${user.token}`)
      .send(command)

    expect(response).toHaveStatus(HttpStatus.OK)
  })
})
