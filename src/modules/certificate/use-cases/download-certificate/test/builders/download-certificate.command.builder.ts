import { CertificateType } from '../../../../enums/certificate-type.enum.js'
import { DownloadCertificateCommand } from '../../download-certificate.command.js'

export class DownloadCertificateCommandBuilder {
  private command: DownloadCertificateCommand

  constructor () {
    this.command = new DownloadCertificateCommand()
    this.command.fileName = 'example.pdf'
  }

  withFileName (fileName: string): this {
    this.command.fileName = fileName
    return this
  }

  withType (type: CertificateType): this {
    this.command.type = type
    return this
  }

  build (): DownloadCertificateCommand {
    return this.command
  }
}
