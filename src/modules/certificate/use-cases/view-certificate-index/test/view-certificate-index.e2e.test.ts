import { after, before, describe, it } from 'node:test'
import request from 'supertest'
import { expect } from 'expect'
import { HttpStatus } from '@nestjs/common'
import { EndToEndTestSetup } from '../../../../../../test/setup/end-to-end-test-setup.js'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { TestUser } from '../../../../../app/users/tests/setup-user.type.js'
import { Permission } from '../../../../permission/permission.enum.js'

describe('ViewCertificateIndex - E2E Tests', () => {
  let setup: EndToEndTestSetup
  let user: TestUser

  before(async () => {
    setup = await TestBench.setupEndToEndTest()
    user = await setup.authContext.getUser([Permission.CERTIFICATE_READ])
  })

  after(async () => await setup.teardown())

  it('returns the certificates', async () => {
    const response = await request(setup.httpServer)
      .get(`/certificates`)
      .set('Authorization', `Bearer ${user.token}`)

    expect(response).toHaveStatus(HttpStatus.OK)
  })
})
