import { Injectable } from '@nestjs/common'
import { typeormPagination } from '@wisemen/pagination'
import dayjs from 'dayjs'
import { SapGetCertificateIndexUseCase } from '../../../sap/use-cases/get-certificate-index/get-certificate-index.use-case.js'
import { SapQuery } from '../../../sap/query/sap-query.js'
import { SapGetCertificateIndexResponse } from '../../../sap/use-cases/get-certificate-index/get-certificate-index.response.js'
import { CertificateMapper } from '../../mappers/certificate.mapper.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { exhaustiveCheck } from '../../../../utils/helpers/exhaustive-check.helper.js'
import { FilterOperator } from '../../../sap/enums/odata-filter-operator.enum.js'
import { SapDateFormatterService } from '../../../sap/services/sap-date-formatter.service.js'
import { CertificateDocTypeMapper } from '../../mappers/sap-doc-type.mapper.js'
import { ViewCertificateIndexResponse } from './view-certificate-index.response.js'
import { ViewCertificateIndexFilterQuery, ViewCertificateIndexQuery, ViewCertificateIndexSortKey } from './view-certificate-index.query.js'

@Injectable()
export class ViewCertificateIndexUseCase {
  constructor (
    private readonly authContext: AuthContext,
    private readonly sapUseCase: SapGetCertificateIndexUseCase
  ) {}

  async execute (query: ViewCertificateIndexQuery): Promise<ViewCertificateIndexResponse> {
    const sapQuery = this.getSapQuery(query)

    const responses = await this.sapUseCase.execute(sapQuery)
    const certificates = CertificateMapper.fromResponses(responses)
    const pagination = typeormPagination(query.pagination)
    return new ViewCertificateIndexResponse(certificates, null, pagination.take, pagination.skip)
  }

  private getSapQuery (query: ViewCertificateIndexQuery): SapQuery<SapGetCertificateIndexResponse> {
    const sapQuery = new SapQuery<SapGetCertificateIndexResponse>(query, {
      defaultOrderBy: {
        column: 'SalesOrder',
        direction: 'asc'
      },
      keyMapper: (key: string) => this.mapSortKeyToSapField(key as ViewCertificateIndexSortKey)
    })

    const selectedCustomerId = this.authContext.getSelectedCustomerId()

    if (selectedCustomerId != null) {
      sapQuery.where('Kunnr', selectedCustomerId)
    }

    if (query.filter !== undefined) {
      this.applySapFilters(query.filter, sapQuery)
    }

    return sapQuery
  }

  private mapSortKeyToSapField (
    sortKey: ViewCertificateIndexSortKey
  ): keyof SapGetCertificateIndexResponse {
    switch (sortKey) {
      case ViewCertificateIndexSortKey.SALES_ORDER:
        return 'SalesOrder'
      case ViewCertificateIndexSortKey.SALES_ORDER_LINE:
        return 'SalesOrderPos'
      case ViewCertificateIndexSortKey.COLLECTION_DATE:
        return 'Yycolldate'
      case ViewCertificateIndexSortKey.DELIVERY_DATE:
        return 'Vdatu'
      case ViewCertificateIndexSortKey.DISPOSITION_PICK_UP_DATE:
        return 'Dareg'
      case ViewCertificateIndexSortKey.DISPOSITION_DELIVERY_DATE:
        return 'Dalbg'
      case ViewCertificateIndexSortKey.TREATMENT_CENTRE:
        return 'NameY0'
      case ViewCertificateIndexSortKey.END_TREATMENT_CENTRE:
        return 'NameYe'
      case ViewCertificateIndexSortKey.EWC:
        return 'Yeural'
      case ViewCertificateIndexSortKey.WTF_FORM:
        return 'Yc1Form'
      case ViewCertificateIndexSortKey.TFS:
        return 'TfsKey'
      case ViewCertificateIndexSortKey.DISPOSAL_DATE:
        return 'Ketdat'
      case ViewCertificateIndexSortKey.PRINT_DATE:
        return 'Erdat'
      case ViewCertificateIndexSortKey.INVOICE:
        return 'Invoice'
      case ViewCertificateIndexSortKey.CONTRACT:
        return 'contract'
      case ViewCertificateIndexSortKey.CONTRACT_ITEM:
        return 'ContractPos'
      case ViewCertificateIndexSortKey.CUSTOMER_ID:
        return 'Kunnr'
      case ViewCertificateIndexSortKey.PICK_UP_ADDRESS_ID:
        return 'KunnrWe'
      case ViewCertificateIndexSortKey.WASTE_PRODUCER_ID:
        return 'KunnrY2'
      default:
        exhaustiveCheck(sortKey)
    }
  }

  private applySapFilters (
    queryFilters: ViewCertificateIndexFilterQuery,
    sapQuery: SapQuery<SapGetCertificateIndexResponse>
  ): void {
    if (queryFilters.docTypes !== undefined && queryFilters.docTypes.length > 0) {
      sapQuery.andWhere('DocType', CertificateDocTypeMapper.toSap(queryFilters.docTypes[0]))

      for (let i = 0; i < queryFilters.docTypes.length; i++) {
        sapQuery.orWhere('DocType', CertificateDocTypeMapper.toSap(queryFilters.docTypes[i]))
      }
    }

    if (queryFilters.salesOrder !== undefined) {
      sapQuery.andWhere('SalesOrder', queryFilters.salesOrder)
    }

    if (queryFilters.salesOrderLine !== undefined) {
      sapQuery.andWhere('SalesOrderPos', queryFilters.salesOrderLine)
    }

    if (queryFilters.description !== undefined) {
      sapQuery.andWhere('Arktx', queryFilters.description, FilterOperator.SUBSTRING_OF)
    }

    if (queryFilters.collectionDate !== undefined) {
      const from = dayjs(queryFilters.collectionDate.from).startOf('day').toDate()
      const to = dayjs(queryFilters.collectionDate.to).endOf('day').toDate()
      sapQuery.andWhere((qb) => {
        return qb.andWhere('Yycolldate', SapDateFormatterService.toSapFilterDate(from), FilterOperator.GREATER_THAN_OR_EQUAL)
          .andWhere('Yycolldate', SapDateFormatterService.toSapFilterDate(to), FilterOperator.LESS_THAN_OR_EQUAL)
      })
    }

    if (queryFilters.deliveryDate !== undefined) {
      const from = dayjs(queryFilters.deliveryDate.from).startOf('day').toDate()
      const to = dayjs(queryFilters.deliveryDate.to).endOf('day').toDate()
      sapQuery.andWhere((qb) => {
        return qb.andWhere('Ketdat', SapDateFormatterService.toSapFilterDate(from), FilterOperator.GREATER_THAN_OR_EQUAL)
          .andWhere('Ketdat', SapDateFormatterService.toSapFilterDate(to), FilterOperator.LESS_THAN_OR_EQUAL)
      })
    }

    if (queryFilters.dispositionPickUpDate !== undefined) {
      const from = dayjs(queryFilters.dispositionPickUpDate.from).startOf('day').toDate()
      const to = dayjs(queryFilters.dispositionPickUpDate.to).endOf('day').toDate()
      sapQuery.andWhere((qb) => {
        return qb.andWhere('Dareg', SapDateFormatterService.toSapFilterDate(from), FilterOperator.GREATER_THAN_OR_EQUAL)
          .andWhere('Dareg', SapDateFormatterService.toSapFilterDate(to), FilterOperator.LESS_THAN_OR_EQUAL)
      })
    }

    if (queryFilters.dispositionDeliveryDate !== undefined) {
      const from = dayjs(queryFilters.dispositionDeliveryDate.from).startOf('day').toDate()
      const to = dayjs(queryFilters.dispositionDeliveryDate.to).endOf('day').toDate()
      sapQuery.andWhere((qb) => {
        return qb.andWhere('Dalbg', SapDateFormatterService.toSapFilterDate(from), FilterOperator.GREATER_THAN_OR_EQUAL)
          .andWhere('Dalbg', SapDateFormatterService.toSapFilterDate(to), FilterOperator.LESS_THAN_OR_EQUAL)
      })
    }

    if (queryFilters.treatmentCentre !== undefined) {
      sapQuery.andWhere('NameY0', queryFilters.treatmentCentre, FilterOperator.SUBSTRING_OF)
    }

    if (queryFilters.endTreatmentCentre !== undefined) {
      sapQuery.andWhere('NameYe', queryFilters.endTreatmentCentre, FilterOperator.SUBSTRING_OF)
    }

    if (queryFilters.ewc !== undefined) {
      sapQuery.andWhere('Yeural', queryFilters.ewc)
    }

    if (queryFilters.wtfForm !== undefined) {
      sapQuery.andWhere('Yc1Form', queryFilters.wtfForm, FilterOperator.SUBSTRING_OF)
    }

    if (queryFilters.tfs !== undefined) {
      sapQuery.andWhere('TfsKey', queryFilters.tfs, FilterOperator.SUBSTRING_OF)
    }

    if (queryFilters.disposalDate !== undefined) {
      const from = dayjs(queryFilters.disposalDate.from).startOf('day').toDate()
      const to = dayjs(queryFilters.disposalDate.to).endOf('day').toDate()
      sapQuery.andWhere((qb) => {
        return qb.andWhere('Yvdatdes', SapDateFormatterService.toSapFilterDate(from), FilterOperator.GREATER_THAN_OR_EQUAL)
          .andWhere('Yvdatdes', SapDateFormatterService.toSapFilterDate(to), FilterOperator.LESS_THAN_OR_EQUAL)
      })
    }

    if (queryFilters.printDate !== undefined) {
      const from = dayjs(queryFilters.printDate.from).startOf('day').toDate()
      const to = dayjs(queryFilters.printDate.to).endOf('day').toDate()
      sapQuery.andWhere((qb) => {
        return qb.andWhere('Erdat', SapDateFormatterService.toSapFilterDate(from), FilterOperator.GREATER_THAN_OR_EQUAL)
          .andWhere('Erdat', SapDateFormatterService.toSapFilterDate(to), FilterOperator.LESS_THAN_OR_EQUAL)
      })
    }

    if (queryFilters.wasteProducerId !== undefined) {
      sapQuery.andWhere('KunnrY2', queryFilters.wasteProducerId)
    }

    if (queryFilters.contract !== undefined) {
      sapQuery.andWhere('contract', queryFilters.contract)
    }

    if (queryFilters.contractItem !== undefined) {
      sapQuery.andWhere('ContractPos', queryFilters.contractItem)
    }

    if (queryFilters.customerId !== undefined) {
      sapQuery.andWhere('Kunnr', queryFilters.customerId)
    }

    if (queryFilters.pickUpAddressId !== undefined) {
      sapQuery.andWhere('KunnrWe', queryFilters.pickUpAddressId)
    }

    if (queryFilters.invoice !== undefined) {
      sapQuery.andWhere('Invoice', queryFilters.invoice)
    }
  }
}
