import { SapGetPayerIndexResponse } from '../../sap/use-cases/get-payer-index/get-payer-index.response.js'
import { Payer } from '../types/payer.type.js'

export class SapPayerMapper {
  static toPayers (payers: SapGetPayerIndexResponse[]): Payer[] {
    return payers.map(payer => this.toPayer(payer))
  }

  static toPayer (payer: SapGetPayerIndexResponse): Payer {
    return { id: payer.Number, name: payer.Name }
  }
}
