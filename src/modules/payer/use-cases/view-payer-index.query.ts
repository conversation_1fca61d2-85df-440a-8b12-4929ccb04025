import { SearchQuery } from '@wisemen/pagination'
import { Equals, IsNotEmpty, ValidateNested } from 'class-validator'
import { ApiProperty } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { ViewPayerIndexFilterQuery } from './view-payer-index.filter.query.js'

export class ViewPayerIndexQuery implements SearchQuery {
  @Equals(undefined)
  sort?: never

  @ApiProperty({ type: ViewPayerIndexFilterQuery })
  @Type(() => ViewPayerIndexFilterQuery)
  @ValidateNested()
  @IsNotEmpty()
  filter: ViewPayerIndexFilterQuery

  @Equals(undefined)
  search?: never
}
