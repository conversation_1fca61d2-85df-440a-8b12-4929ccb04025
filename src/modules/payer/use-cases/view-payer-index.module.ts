import { Module } from '@nestjs/common'
import { CustomerModule } from '../../customer/customer.module.js'
import { SapModule } from '../../sap/sap.module.js'
import { ViewPayerIndexController } from './view-payer-index.controller.js'
import { ViewPayerIndexUseCase } from './view-payer-index.use-case.js'
import { ViewPayerIndexValidator } from './view-payer-index.validator.js'

@Module({
  imports: [
    SapModule,
    CustomerModule
  ],
  controllers: [
    ViewPayerIndexController
  ],
  providers: [
    ViewPayerIndexUseCase,
    ViewPayerIndexValidator
  ]
})
export class ViewPayerIndexModule {}
