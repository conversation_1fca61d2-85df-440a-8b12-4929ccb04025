import { Injectable } from '@nestjs/common'
import { SapGetPayerIndexUseCase } from '../../sap/use-cases/get-payer-index/get-payer-index.use-case.js'
import { GetCustomerDefaultSalesOrganisationIdUseCase } from '../../customer/use-cases/get-customer-default-sales-organisation-id/get-customer-default-sales-organisation-id.use-case.js'
import { SapPayerMapper } from '../helpers/sap-payer.mapper.js'
import { ViewPayerIndexResponse } from './view-payer-index.response.js'
import { ViewPayerIndexQuery } from './view-payer-index.query.js'
import { ViewPayerIndexValidator } from './view-payer-index.validator.js'

@Injectable()
export class ViewPayerIndexUseCase {
  constructor (
    private readonly getCustomerSalesOrganisationId: GetCustomerDefaultSalesOrganisationIdUseCase,
    private readonly validator: <PERSON>PayerIndexValidator,
    private readonly getPayerIndex: SapGetPayerIndexUseCase
  ) {}

  async execute (
    query: ViewPayerIndexQuery
  ): Promise <ViewPayerIndexResponse> {
    this.validator.validate(query.filter.customerId)
    const customerDefaultSalesOrganisationId = await this.getCustomerSalesOrganisationId
      .getOrganisationIdOrFail(
        query.filter.customerId
      )
    const sapQuery = this.getSapQuery(
      query.filter.customerId,
      customerDefaultSalesOrganisationId
    )

    const response = await this.getPayerIndex.execute(sapQuery)

    const payers = SapPayerMapper.toPayers(response)

    return new ViewPayerIndexResponse(payers)
  }

  // TODO: Refactor to something like SapQuery

  private getSapQuery (
    customerId: string,
    salesOrganisationId: string
  ): string {
    return `?Customer='${customerId}'&Salesorg='${salesOrganisationId}'`
  }
}
