import { randomUUID } from 'crypto'
import { ViewPayerIndexQuery } from '../view-payer-index.query.js'
import { ViewPayerIndexFilterQuery } from '../view-payer-index.filter.query.js'

export class ViewPayerIndexQueryBuilder {
  private query: ViewPayerIndexQuery

  constructor () {
    this.reset()
  }

  reset () {
    this.query = new ViewPayerIndexQuery()
    this.query.filter = new ViewPayerIndexFilterQuery()
    this.query.filter.customerId = randomUUID()
  }

  build (): ViewPayerIndexQuery {
    const built = this.query

    this.reset()

    return built
  }
}
