import { after, before, describe, it, mock } from 'node:test'
import { randomUUID } from 'crypto'
import request from 'supertest'
import { expect } from 'expect'
import qs from 'qs'
import { TestBench } from '../../../../../test/setup/test-bench.js'
import { EndToEndTestSetup } from '../../../../../test/setup/end-to-end-test-setup.js'
import { Permission } from '../../../permission/permission.enum.js'
import { UserCustomerAuthService } from '../../../auth/services/user-customer-auth.service.js'
import { ViewPayerIndexQueryBuilder } from './view-payer-index-query.builder.js'

describe('View payer index e2e test', () => {
  let setup: EndToEndTestSetup

  before(async () => {
    setup = await TestBench.setupEndToEndTest()
  })

  after(async () => await setup.teardown())

  it('returns 401 when not authenticated', async () => {
    const response = await request(setup.httpServer)
      .get('/payers')
      .query({ 'filter[customerId]': randomUUID() })

    expect(response).toHaveStatus(401)
  })

  it('returns 403 when unauthorized', async () => {
    const user = await setup.authContext.getUser([])

    const response = await request(setup.httpServer)
      .get('/payers')
      .query({ 'filter[customerId]': randomUUID() })
      .set('Authorization', `Bearer ${user.token}`)

    expect(response).toHaveStatus(403)
  })

  it('returns 400 when customerId filter is missing', async () => {
    const user = await setup.authContext.getUser([Permission.INVOICE_READ])

    const response = await request(setup.httpServer)
      .get('/payers')
      .set('Authorization', `Bearer ${user.token}`)

    expect(response).toHaveStatus(400)
  })

  it('returns 200 when authorized with permission', async () => {
    const user = await setup.authContext.getUser([Permission.INVOICE_READ])

    const query = new ViewPayerIndexQueryBuilder().build()

    mock.method(UserCustomerAuthService.prototype, 'canUserAccessCustomer', () => {
      return true
    })

    mock.method(UserCustomerAuthService.prototype, 'canUserAccessCustomer', () => {
      return true
    })

    const response = await request(setup.httpServer)
      .get('/payers')
      .query(qs.stringify(query))
      .set('Authorization', `Bearer ${user.token}`)

    expect(response).toHaveStatus(200)
  })
})
