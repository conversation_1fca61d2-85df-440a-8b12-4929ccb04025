import { before, describe, it, afterEach } from 'node:test'
import { randomUUID } from 'crypto'
import { createStubInstance, SinonStubbedInstance } from 'sinon'
import { expect } from 'expect'
import { randCompanyName } from '@ngneat/falso'
import { TestBench } from '../../../../../test/setup/test-bench.js'
import { ViewPayerIndexUseCase } from '../view-payer-index.use-case.js'
import { ViewPayerIndexValidator } from '../view-payer-index.validator.js'
import { SapGetPayerIndexUseCase } from '../../../sap/use-cases/get-payer-index/get-payer-index.use-case.js'
import { GetCustomerDefaultSalesOrganisationIdUseCase } from '../../../customer/use-cases/get-customer-default-sales-organisation-id/get-customer-default-sales-organisation-id.use-case.js'
import { SapGetPayerIndexResponseBuilder } from '../../../sap/use-cases/get-payer-index/tests/get-payer-index-response.builder.js'
import { ViewPayerIndexQuery } from '../view-payer-index.query.js'

describe('View payer index use-case unit test', () => {
  let useCase: ViewPayerIndexUseCase

  let validator: SinonStubbedInstance<ViewPayerIndexValidator>
  let getCustomerSalesOrganisationId: SinonStubbedInstance<
    GetCustomerDefaultSalesOrganisationIdUseCase
  >
  let sapGetPayerIndex: SinonStubbedInstance<SapGetPayerIndexUseCase>

  before(() => {
    TestBench.setupUnitTest()

    validator = createStubInstance(ViewPayerIndexValidator)
    getCustomerSalesOrganisationId = createStubInstance(
      GetCustomerDefaultSalesOrganisationIdUseCase
    )
    sapGetPayerIndex = createStubInstance(SapGetPayerIndexUseCase)

    useCase = new ViewPayerIndexUseCase(
      getCustomerSalesOrganisationId,
      validator,
      sapGetPayerIndex
    )

    mockMethods()
  })

  afterEach(() => {
    mockMethods()
  })

  function mockMethods () {
    validator.validate.returns(undefined)
    getCustomerSalesOrganisationId.getOrganisationIdOrFail.resolves(randomUUID())
    sapGetPayerIndex.execute.resolves([])
  }

  it('should validate customer ID', async () => {
    const customerId = randomUUID()
    const query: ViewPayerIndexQuery = {
      filter: { customerId }
    }

    await useCase.execute(query)

    expect(validator.validate.calledWith(customerId)).toBe(true)
  })

  it('should get customer default sales organisation ID', async () => {
    const customerId = randomUUID()
    const query: ViewPayerIndexQuery = {
      filter: { customerId }
    }

    await useCase.execute(query)

    expect(getCustomerSalesOrganisationId.getOrganisationIdOrFail.calledWith(customerId)).toBe(true)
  })

  it('should return mapped payers from SAP response', async () => {
    const customerId = randomUUID()
    const salesOrganisationId = randomUUID()
    const payerId = randomUUID()
    const payerName = randCompanyName()

    const query: ViewPayerIndexQuery = {
      filter: { customerId }
    }

    const sapResponse = [
      new SapGetPayerIndexResponseBuilder()
        .withNumber(payerId)
        .withName(payerName)
        .build()
    ]

    getCustomerSalesOrganisationId.getOrganisationIdOrFail.resolves(salesOrganisationId)
    sapGetPayerIndex.execute.resolves(sapResponse)

    const result = await useCase.execute(query)

    expect(result.items).toEqual([
      {
        id: payerId,
        name: payerName
      }
    ])
  })

  it('should return empty array when no payers found', async () => {
    const customerId = randomUUID()
    const query: ViewPayerIndexQuery = {
      filter: { customerId }
    }

    sapGetPayerIndex.execute.resolves([])

    const result = await useCase.execute(query)

    expect(result.items).toStrictEqual([])
  })
})
