import { ApiProperty } from '@nestjs/swagger'
import { Payer } from '../types/payer.type.js'

class PayerResponse {
  @ApiProperty({ type: String })
  id: string

  @ApiProperty({ type: String })
  name: string

  constructor (payer: { id: string, name: string }) {
    this.id = payer.id
    this.name = payer.name
  }
}

export class ViewPayerIndexResponse {
  @ApiProperty({ type: PayerResponse, isArray: true })
  items: PayerResponse[]

  constructor (items: Payer[]) {
    this.items = items.map(payer => new PayerResponse(payer))
  }
}
