import { Controller, Get, Query } from '@nestjs/common'
import { ApiTags, ApiOAuth2, ApiOkResponse } from '@nestjs/swagger'
import { Permission } from '../../permission/permission.enum.js'
import { GlobalCustomerRequired } from '../../auth/decorators/no-customer-required.decorator.js'
import { Permissions } from '../../permission/permission.decorator.js'
import { ViewPayerIndexUseCase } from './view-payer-index.use-case.js'
import { ViewPayerIndexQuery } from './view-payer-index.query.js'
import { ViewPayerIndexResponse } from './view-payer-index.response.js'

@ApiTags('Payer')
@ApiOAuth2([])
@Controller('payers')
export class ViewPayerIndexController {
  constructor (
    private readonly useCase: ViewPayerIndexUseCase
  ) { }

  @Get()
  @GlobalCustomerRequired()
  @Permissions(
    Permission.INVOICE_READ,
    Permission.INVOICE_MANAGE
  )
  @ApiOkResponse({ type: ViewPayerIndexResponse })
  public async viewPayerIndex (
    @Query() query: ViewPayerIndexQuery
  ): Promise<ViewPayerIndexResponse> {
    return await this.useCase.execute(query)
  }
}
