{"announcement": {"announcement_not_found": "Ann<PERSON>ce introuvable"}, "auth": {"azure_unauthorized_id": "L'ID Azure Entra est requis", "azure_unauthorized_id_auth_context": "L'ID Azure Entra n'est pas disponible dans le contexte d'authentification", "azure_unauthorized_roles": "", "azure_unauthorized_upn": "L'UPN Azure Entra est requis", "azure_unauthorized_upn_auth_context": "L'UPN Azure Entra n'est pas disponible dans le contexte d'authentification", "customer_not_found": "Client sélectionné introuvable", "impersonated_user_not_found": "Utilisateur usurpé introuvable", "selected_customer_required": "Un client sélectionné est requis"}, "contact": {"contact_already_exists": "Un contact avec cet email existe déjà", "contact_not_found": "Contact introuvable"}, "contract-line": {"empty_contract_lines_selection": "Aucune ligne de contrat sélectionnée", "contract_line_not_accessible": "Cet article de contrat n'est plus disponible. Veuillez contacter votre représentant Indaver.", "contract_line_not_found": "La ligne de contrat est introuvable", "contract_line_not_of_customer": "Cet article de contrat n'appartient pas au client spécifié.", "contract_line_not_of_pick_up_addresses": "La ligne de contrat n'appartient pas aux adresses de ramassage spécifiées", "contract_line_not_of_waste_producers": "La ligne de contrat n'appartient pas aux producteurs de déchets spécifiés"}, "customer": {"customer_not_accessible": "Ce client n'est pas accessible", "customer_not_found": "Client avec l'identifiant {customerId} introuvable", "customer_not_provided": "Le client doit être fourni", "selected_customer_filter_mismatch": "Le client sélectionné ne correspond pas au filtre"}, "delivery-address": {"delivery-address_not_accessible": "<PERSON>tte adresse de livraison n'est pas accessible", "delivery-address_not_found": "Adress<PERSON> de livraison avec l'identifiant {deliveryAddressId} introuvable", "delivery-address_not_provided": "<PERSON>tte adresse de livraison n'est pas fournie"}, "dynamic-tables": {"column_not_filterable": "Cette colonne n'est pas filtrable", "column_not_found": "Impossible de trouver la colonne", "column_not_hidable": "La colonne {columnName} ne peut pas être masquée", "column_not_sortable": "Cette colonne n'est pas triable", "duplicate_column": "Une colonne est incluse plusieurs fois", "duplicate_view_name": "Le nom de la vue ne peut pas être identique à une vue globale ou actuelle", "dynamic_table_view_not_found": "Vue de tableau dynamique introuvable", "global_default_view_not_deletable": "La vue globale par défaut d'un tableau dynamique ne peut pas être supprimée", "invalid_global_default": "La vue du tableau doit être globale pour être définie comme vue globale par défaut", "last_global_view_not_deletable": "La dernière vue globale d'un tableau dynamique ne peut pas être supprimée", "unauthorized_manage_global_view": "Seuls les administrateurs système peuvent gérer les vues globales", "unauthorized_delete_view": "Se<PERSON> le créateur peut supprimer cette vue", "unauthorized_update_view": "Se<PERSON> le créateur peut mettre à jour cette vue"}, "ewc-code": {"ewc_code_not_found": "Ce code CED n'existe pas", "missing_ewc_levels": "Ne possède pas tous les niveaux CED requis"}, "exceptions": {"internal_server_error": "Le serveur n'a pas pu traiter votre demande. Veuillez réessayer plus tard.", "service_unavailable": "Le serveur n'a pas pu traiter votre demande. Veuillez réessayer plus tard."}, "files": {"file_not_accessible": "Ce fichier n'est pas accessible", "file_not_found": "Fichier avec l'uuid {fileUuid} introuvable", "duplicate_file_uuid": "Les fileUuids en double ne sont pas autorisés", "invalid_entity_part": "Cette partie d'entité n'est pas autorisée"}, "generic": {"date_must_be_after": "La date doit être postérieure à {date}", "field_must_be_null": "Ce champ doit être vide", "missing_required_field": "Ce champ est obligatoire", "not_found": "Introuvable", "unauthorized": "Non autorisé"}, "invoice": {"invalid_invoice_status_combination_default": "Combinaison d'états de facture non valide", "not_found": "Facture introuvable", "non_approve_or_rejectable_draft_invoice": "Ne peut qu'approuver ou rejeter les projets de factures avec statut à_be_approvées"}, "nats": {"nats_cache_unavailable": "Le cache NATS n'est pas configuré", "nats_client_unavailable": "Le client NATS n'est pas configuré"}, "news": {"news-item-translation-exists": "Cette traduction existe déjà", "news-item-translation-expected": "Au moins 1 traduction d'article d'actualité est attendue", "no-start-date-or-end-date-expected": "Aucune date de début ou de fin n'est attendue"}, "packaging-request": {"packaging_request_already_submitted": "La demande d'emballage est déjà soumise", "packaging_request_not_found": "Demande d'emballage introuvable", "invalid-transport-mode": "Doit être un mode de transport emballé avec bâche coulissante", "submit_only_packaging_request": "Seule la demande d'emballage peut être soumise de cette manière", "delete_only_packaging_request": "Seule la demande d'emballage peut être supprimée de cette manière", "update_only_packaging_request": "Seule la demande d'emballage peut être mise à jour de cette manière", "copy_non_submitted_packaging_request": "Copie uniquement autorisée sur les demandes d'emballage soumises", "invalid-packaging-request-copy": "La copie demandée n'est pas une demande d'emballage"}, "pick-up-address": {"not_found": "Adresse d'enlèvement avec l'identifiant {pickUpAddressId} introuvable", "pick_up_address_not_accessible": "Cette adresse d'enlèvement n'est pas accessible", "pick_up_address_not_provided": "Cette adresse d'enlèvement n'est pas fournie"}, "pick-up-request": {"contract-line-id-mismatch": "L'identifiant de la ligne de contrat doit être le même", "exactly_one_contract_line_expected": "Exactement une ligne de contrat est attendue", "invalid_pick_up_request_status_combination": "Impossible de combiner le statut de demande d'enlèvement 'brouillon' avec d'autres statuts", "invalid_pick_up_request_status_combination_default": "Combinaison invalide des statuts de demande d'enlèvement", "invalid_total_quantity_pallets": "La quantité totale de palettes doit être égale ou supérieure à la quantité de matériaux", "invalid_transport_mode": "Ne devrait pas avoir {transportMode} dans la demande de planification hebdomadaire", "invalid_transport_mode_default": "Mode de transport invalide", "pick_up_request_already_submitted": "La demande d'enlèvement a déjà été soumise", "pick_up_request_not_found": "Demande d'enlèvement introuvable", "too_many_contract_lines": "Vous avez sélectionné trop de lignes de contrat", "copy_non_submitted_pick_up_request": "Copie uniquement autorisée sur les demandes de ramassage soumises", "invalid-indascan-submit-status": "Les demandes de ramassage SAP ne peuvent être soumises que lorsqu'elle a le statut d'Indascan", "contract-line-not-found": "La ligne de contrat en position {position} n'a pas été trouvée pour la demande de ramassage {number}", "invalid-sap-waste-materials-update": "Je ne peux pas ajouter ou supprimer les déchets sur une demande de ramassage SAP", "invalid-pick-up-request-copy": "La copie demandée n'est pas une demande de ramassage"}, "roles": {"role_name_already_in_use": "Le nom de rôle {name} est déjà utilisé par un autre rôle", "role_not_editable": "Ce rôle n'est pas modifiable", "role_not_found": "<PERSON><PERSON><PERSON> {roleUuid} introuvable"}, "template": {"not-submittable": "Un modèle n'est pas soumettable"}, "user": {"invalid_impersonation_token": "Le jeton d'usurpation n'est pas valide", "user_not_found": "Utilisateur {userUuid} introuvable"}, "waste-inquiry": {"invalid_stable_temperature": "La température stable fournie n'est pas valide", "invalid_waste_inquiry_status_combination": "Impossible de combiner le statut de demande de déchets 'brouillon' avec d'autres statuts", "invalid_waste_inquiry_status_combination_default": "Combinaison de statuts de demande de déchets invalide", "min_temperature_must_be_less_than_max": "La température minimale doit être inférieure à la température maximale", "no_analysis_report_files_expected": "Aucun fichier de rapport d'analyse attendu", "no_option_expected_when_none_selected": "Aucune autre option ne doit être sélectionnée lorsque l'option 'Aucun' est sélectionnée", "no_sds_files_expected": "<PERSON><PERSON>n fichier FDS attendu", "no_svhc_extra_expected": "Aucune information SVHC supplémentaire attendue", "temperature_does_not_to_be_provided": "Les températures min, max ou moyenne ne doivent pas être fournies lorsque le type est ambiant", "temperature_must_be_provided": "Les températures minimale et maximale ou la température moyenne doivent être fournies", "transport_options_invalid": "Options de transport non valides", "waste_inquiry_already_submitted": "La demande de déchets a déjà été soumise", "not_found": "<PERSON><PERSON><PERSON> de d<PERSON>chets introuvable"}, "waste-producer": {"waste_producer_not_accessible": "Ce producteur de déchets n'est pas accessible", "not_found": "Producteur de déchets avec l'identifiant {wasteProducerId} introuvable"}, "weekly-planning-request": {"needs-exactly-one-material": "La demande d'enlèvement nécessite exactement un matériau", "not_submitted_error": "La demande d'enlèvement n'a pas encore été soumise", "pick-up-request-id-mismatch": "Il y a une incohérence avec l'identifiant dans la demande d'enlèvement", "weekly-planning-contract-sap-not-found": "", "weekly-planning-id-mismatch": "Il y a une incohérence avec l'identifiant dans la demande de planification hebdomadaire", "weekly_planning_request_already_submitted_error": "La demande de planification hebdomadaire a déjà été soumise", "weekly_planning_request_not_found": "Demande de planification hebdomadaire introuvable"}, "document": {"not-found": "Document introuvable"}}