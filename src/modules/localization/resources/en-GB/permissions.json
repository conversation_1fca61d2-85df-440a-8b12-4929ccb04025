{"all_permissions": {"description": "This permissions is a special permission which overrules all other permissions", "group-name": "Admin", "name": "All permissions"}, "announcement": {"group-name": "Announcements", "manage": {"description": "View, create, update and delete announcements", "name": "Manage"}}, "balanced-scorecard": {"group-name": "Balanced Scorecard", "read": {"description": "View balanced scorecard", "name": "Read"}}, "certificate": {"group-name": "Certificates", "read": {"description": "View certificates", "name": "Read"}, "manage": {"description": "View, create, update and delete certificates", "name": "Manage"}}, "contact": {"group-name": "Contact", "manage": {"description": "View, create, update and delete contact persons", "name": "Manage"}, "read": {"description": "View overview and detailed info about contact persons", "name": "Read"}}, "contract-line": {"group-name": "Contract Lines", "read": {"description": "View contract lines", "name": "Read"}, "manage": {"description": "View, create, update and delete contract lines", "name": "Manage"}}, "document": {"group-name": "Documents", "master-table": {"description": "Read master table documents", "name": "Master Table"}, "tfs": {"description": "Read TFS documents", "name": "TFS"}, "quotation": {"description": "Read quotation documents", "name": "Quotation"}, "minutes-and-presentations": {"description": "Read minutes and presentations documents", "name": "Minutes and Presentations"}, "manual": {"description": "Read manual documents", "name": "Manual"}, "bsc": {"description": "Read BSC documents", "name": "BSC"}, "contract": {"description": "Read contract documents", "name": "Contract"}, "transport": {"description": "Read transport documents", "name": "Transport"}}, "dynamic-table-view": {"group-name": "Dynamic Table Views", "manage": {"name": "Manage", "description": "Create, update and delete global dynamic table views"}}, "ecmr": {"group-name": "eCMR", "read": {"description": "View eCMR documents", "name": "Read"}}, "event-log": {"group-name": "Event Log", "read": {"description": "View event logs", "name": "Read"}}, "guidance-letter": {"group-name": "Guidance Letters", "read": {"detail": {"description": "Read guidance letters", "name": "Read"}}}, "invoice": {"group-name": "Invoices", "manage": {"description": "Manage invoices", "name": "Manage"}, "read": {"description": "View invoices", "name": "Read"}}, "jobs": {"group-name": "Jobs", "read": {"detail": {"description": "View detailed job information", "name": "View Details"}, "index": {"description": "View job list", "name": "View List"}}}, "news-item": {"group-name": "News Items", "manage": {"description": "View, create, update and delete news items", "name": "Manage"}}, "newsletter": {"group-name": "Newsletters", "subscribe": {"description": "Subscribe to newsletters", "name": "Subscribe"}}, "packaging-request": {"group-name": "Packaging Requests", "manage": {"description": "View, create, update and delete packaging requests", "name": "Manage"}, "read": {"description": "View packaging requests", "name": "Read"}}, "pick-up-request": {"group-name": "Pick-up Requests", "manage": {"description": "View, create, update and delete pick-up requests", "name": "Manage"}, "read": {"description": "View pick-up requests", "name": "Read"}}, "power-bi": {"group-name": "Power BI", "read": {"description": "View Power BI reports", "name": "Read"}}, "role": {"group-name": "Roles", "manage": {"description": "View, create, update and delete roles", "name": "Manage"}, "read": {"description": "View roles and permissions", "name": "Read"}}, "send_push_notification": {"description": "Send push notifications to users", "group-name": "Push Notifications", "name": "Send Push"}, "typesense": {"group-name": "Typesense", "name": "Typesense", "description": "Able to import and migrate data to Typesense"}, "useful-link": {"group-name": "Useful Links", "ecmr": {"description": "Access ECMR useful links", "name": "ECMR"}, "permits": {"description": "Access Permits useful links", "name": "Permits"}, "reporting": {"description": "Access Reporting useful links", "name": "Reporting"}}, "user": {"group-name": "Users", "impersonate": {"description": "Impersonate other user accounts", "name": "Impersonate"}, "manage": {"description": "View, create, update and delete user accounts", "name": "Manage"}, "read": {"description": "View user accounts", "name": "Read"}}, "waste-inquiry": {"group-name": "Waste Inquiries", "manage": {"description": "View, create and update waste inquiries", "name": "Manage"}, "read": {"description": "View waste inquiries", "name": "Read"}}, "weekly-planning-request": {"group-name": "Weekly Planning Requests", "manage": {"description": "View, create, update and submit weekly planning requests", "name": "Manage"}, "read": {"description": "View weekly planning requests", "name": "Read"}}}