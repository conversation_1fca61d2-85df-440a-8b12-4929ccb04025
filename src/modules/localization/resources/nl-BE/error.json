{"announcement": {"announcement_not_found": "Mededeling niet gevonden"}, "auth": {"azure_unauthorized_id": "Azure Entra ID is vereist", "azure_unauthorized_id_auth_context": "Azure Entra ID is niet be<PERSON><PERSON> in de authenticatiecontext", "azure_unauthorized_roles": "", "azure_unauthorized_upn": "Azure Entra UPN is vereist", "azure_unauthorized_upn_auth_context": "Azure Entra UPN is niet be<PERSON><PERSON> in de authenticatiecontext", "customer_not_found": "Geselecteerde klant niet gevonden", "impersonated_user_not_found": "Geïmiteerde gebruiker niet gevonden", "selected_customer_required": "Gese<PERSON><PERSON><PERSON> klant is vereist"}, "contact": {"contact_already_exists": "<PERSON><PERSON> <PERSON><PERSON><PERSON> al een contact<PERSON><PERSON> met dit e-mailadres", "contact_not_found": "Contactpersoon niet gevonden"}, "contract-line": {"empty_contract_lines_selection": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> geselecteerd", "contract_line_not_accessible": "Deze contractregel is niet meer besch<PERSON>. Neem contact op met uw Indaver-vertegenwoordiger.", "contract_line_not_found": "De contractregel kan niet worden gevonden", "contract_line_not_of_customer": "Deze contractregel behoort niet tot de opgegeven klant.", "contract_line_not_of_pick_up_addresses": "De contractregel behoort niet tot de opgegeven ophaaladressen", "contract_line_not_of_waste_producers": "De contractregel behoort niet tot de opgegeven afvalproducenten"}, "customer": {"customer_not_accessible": "<PERSON><PERSON> klant is niet <PERSON>", "customer_not_found": "<PERSON><PERSON> met id {customerId} niet gevonden", "customer_not_provided": "De klant moet worden opgegeven", "selected_customer_filter_mismatch": "De geselecteerde klant komt niet overeen met de filter"}, "delivery-address": {"delivery-address_not_accessible": "Dit leveringsadres is niet toe<PERSON>", "delivery-address_not_found": "Leveringsadres met id {deliveryAddressId} niet gevonden", "delivery-address_not_provided": "Dit leveringsadres is niet opgegeven"}, "dynamic-tables": {"column_not_filterable": "Deze kolom kan niet worden gefilterd", "column_not_found": "<PERSON><PERSON><PERSON> niet gevonden", "column_not_hidable": "De kolom {columnName} kan niet worden verborgen", "column_not_sortable": "Deze kolom kan niet worden gesorteerd", "duplicate_column": "<PERSON><PERSON> kolom komt meer dan <PERSON> keer voor", "duplicate_view_name": "De na<PERSON> van de weergave kan niet hetzelfde zijn als een globale of huidige weergave", "dynamic_table_view_not_found": "Dynamische tabelweergave niet gevonden", "global_default_view_not_deletable": "De globale standaardweergave van een dynamische tabel kan niet worden verwijderd", "invalid_global_default": "Tabelweergave moet globaal zijn om als globale standaard te worden ingesteld", "last_global_view_not_deletable": "De laatste globale weergave van een dynamische tabel kan niet worden verwijderd", "unauthorized_manage_global_view": "Alleen systeembeheerders kunnen globale weergaven beheren", "unauthorized_delete_view": "Alleen de maker kan deze weergave verwijderen", "unauthorized_update_view": "Alleen de maker kan deze weergave bijwerken"}, "ewc-code": {"ewc_code_not_found": "Deze EURAL-code bestaat niet", "missing_ewc_levels": "He<PERSON>t niet alle vereiste EURAL-niveaus"}, "exceptions": {"internal_server_error": "De server kon uw verzoek niet verwerken. Probeer het later opnieuw.", "service_unavailable": "De server kon uw verzoek niet verwerken. Probeer het later opnieuw."}, "files": {"file_not_accessible": "Dit bestand is niet <PERSON>", "file_not_found": "<PERSON><PERSON> met uuid {fileUuid} niet gevonden", "duplicate_file_uuid": "Dubbele fileUuids zijn niet toe<PERSON>", "invalid_entity_part": "Dit entiteitsdeel is niet toe<PERSON>an"}, "generic": {"date_must_be_after": "De datum moet na {date} liggen", "field_must_be_null": "Dit veld moet leeg zijn", "missing_required_field": "Dit veld is verplicht", "not_found": "<PERSON><PERSON>", "unauthorized": "<PERSON><PERSON>"}, "invoice": {"invalid_invoice_status_combination_default": "Ongeldige combinatie van factuurstatussen", "not_found": "Factuur niet gevonden", "non_approve_or_rejectable_draft_invoice": "Kan de conceptfacturen alleen maar go<PERSON>keuren of afwijzen", "document_not_found": "Factuurdocument niet gevonden"}, "nats": {"nats_cache_unavailable": "De NATS-cache is niet geconfigureerd", "nats_client_unavailable": "De NATS-client is niet geconfigureerd"}, "news": {"news-item-translation-exists": "Deze vertaling bestaat al", "news-item-translation-expected": "Er wordt ten minste 1 nieuwsberichtvertaling verwacht", "no-start-date-or-end-date-expected": "<PERSON><PERSON> start- of eind<PERSON><PERSON> verwacht"}, "packaging-request": {"packaging_request_already_submitted": "De verpakkingsaanvraag is al ingediend", "packaging_request_not_found": "Verpakkingsaanvraag niet gevonden", "invalid-transport-mode": "<PERSON><PERSON> transportmodus <PERSON> met s<PERSON><PERSON><PERSON><PERSON>jn", "submit_only_packaging_request": "Alleen verpakkingsaanvragen kunnen op deze manier worden ingediend", "delete_only_packaging_request": "Alleen verpakkingsaanvragen kunnen op deze manier worden verwijderd", "update_only_packaging_request": "Alleen verpakkingsaanvragen kunnen op deze manier worden bijgewerkt", "copy_non_submitted_packaging_request": "Kopie alleen toegestaan bij verzonden verpakkingsverzoeken", "invalid-packaging-request-copy": "Gevraagde kopie is geen verpakkingsverzoek"}, "pick-up-address": {"not_found": "<PERSON><PERSON><PERSON><PERSON><PERSON> met id {pickUpAddressId} niet gevonden", "pick_up_address_not_accessible": "<PERSON>t ophaala<PERSON> is niet <PERSON>", "pick_up_address_not_provided": "Dit ophaala<PERSON> is niet opgegeven"}, "pick-up-request": {"contract-line-id-mismatch": "De contractregel-id moet hetzelfde zijn", "exactly_one_contract_line_expected": "Er wordt precies één contractregel verwacht", "invalid_pick_up_request_status_combination": "<PERSON><PERSON> op<PERSON>verzoekstatus 'concept' niet <PERSON><PERSON> met andere <PERSON>sen", "invalid_pick_up_request_status_combination_default": "Ongeldige combinatie van ophaalverzoekstatussen", "invalid_total_quantity_pallets": "Totaal aantal pallets moet gelijk zijn aan of groter zijn dan de ho<PERSON>he<PERSON> in materialen", "invalid_transport_mode": "Mag geen {transportMode} hebben in wekelijks planningsverzoek", "invalid_transport_mode_default": "Ongeldige transportmodus", "pick_up_request_already_submitted": "<PERSON><PERSON> ophaalverzoek is al ingediend", "pick_up_request_not_found": "Ophaalverzoek niet gevonden", "too_many_contract_lines": "U heeft te veel contractregels geselecteerd", "copy_non_submitted_pick_up_request": "<PERSON><PERSON> alleen toegestaan op ingediende pick -up verzoeken", "invalid-indascan-submit-status": "SAP ophaalaanvragen kunnen alleen worden ingediend wanneer het de status van Indascan heeft", "contract-line-not-found": "Contractregel op positie {position} werd niet gevonden voor ophaalverzoek {number}", "invalid-sap-waste-materials-update": "Kan afvalmaterialen niet toe<PERSON>n of verwijderen op een SAP ophaalverzoek", "invalid-pick-up-request-copy": "<PERSON><PERSON><PERSON><PERSON><PERSON> kopie is geen pick -up verzoek"}, "roles": {"role_name_already_in_use": "R<PERSON><PERSON><PERSON> {name} is al in gebruik door een andere rol", "role_not_editable": "Deze rol kan niet worden bewerkt", "role_not_found": "Rol {roleUuid} niet gevonden"}, "sap-file": {"not-found": "Sap-bestand niet gevonden"}, "template": {"not-submittable": "Een template is niet in te dienen"}, "user": {"invalid_impersonation_token": "Ongeldig token voor imitatie", "user_not_found": "Gebruiker {userUuid} niet gevonden"}, "waste-inquiry": {"invalid_stable_temperature": "De opgegeven stabiele temperatuur is ongeldig", "invalid_waste_inquiry_status_combination": "Kan afvalverzoekstatus 'concept' niet <PERSON><PERSON> met and<PERSON>sen", "invalid_waste_inquiry_status_combination_default": "Ongeldige combinatie van afvalverzoekstatussen", "min_temperature_must_be_less_than_max": "Minimumtemperatuur moet lager zijn dan maximumtemperatuur", "no_analysis_report_files_expected": "<PERSON>n analyserapportbestanden verwacht", "no_option_expected_when_none_selected": "Er mag geen andere optie worden geselecteerd wanneer de optie 'Geen' is geselecteerd", "no_sds_files_expected": "<PERSON>n VIB-best<PERSON><PERSON> ver<PERSON>cht", "no_svhc_extra_expected": "Geen aanvullende ZZS-informatie verwacht", "temperature_does_not_to_be_provided": "Min-, max- of gemiddelde temperatuur mag niet worden opgegeven bij omgevingstemperatuur", "temperature_must_be_provided": "Ofwel min- en maximumtemperatuur of gemiddelde temperatuur moet worden opgegeven", "transport_options_invalid": "Transportopties zijn ongeldig", "waste_inquiry_already_submitted": "Het afvalverzoek is al ingediend", "not_found": "Afvalverzoek niet gevonden", "summary_document_not_found": "Samenvatting Document van afvalonderzoek niet gevonden", "document_not_found": "Afvalverzoekdocument niet gevonden"}, "waste-producer": {"waste_producer_not_accessible": "Deze afvalproducent is niet <PERSON>", "not_found": "Afvalproducent met id {wasteProducerId} niet gevonden"}, "weekly-planning-request": {"needs-exactly-one-material": "Het ophaalverzoek heeft precies één materiaal nodig", "not_submitted_error": "Het op<PERSON><PERSON> is nog niet ingediend", "pick-up-request-id-mismatch": "Er is een verschil in id bij het ophaalverzoek", "weekly-planning-contract-sap-not-found": "", "weekly-planning-id-mismatch": "Er is een verschil in id bij het wekelijkse planningsverzoek", "weekly_planning_request_already_submitted_error": "Het wekelijkse planningsverzoek is al ingediend", "weekly_planning_request_not_found": "Wekelijks planningsverzoek niet gevonden"}, "document": {"not-found": "Document niet gevonden"}, "certificate": {"file_not_found": "Certificaatbestand niet gevonden"}}