/* DO NOT EDIT, file generated by nestjs-i18n */

/* eslint-disable */
/* prettier-ignore */
import { Path } from "nestjs-i18n";
/* prettier-ignore */
export type I18nTranslations = {
    "common": {
        "duration": {
            "hours": {
                "one": string;
                "other": string;
            };
        };
        "missing-translation": string;
    };
    "error": {
        "announcement": {
            "announcement_not_found": string;
        };
        "auth": {
            "azure_unauthorized_id": string;
            "azure_unauthorized_id_auth_context": string;
            "azure_unauthorized_roles": string;
            "azure_unauthorized_upn": string;
            "azure_unauthorized_upn_auth_context": string;
            "customer_not_found": string;
            "impersonated_user_not_found": string;
            "selected_customer_required": string;
        };
        "contact": {
            "contact_already_exists": string;
            "contact_not_found": string;
        };
        "contract-line": {
            "empty_contract_lines_selection": string;
            "contract_line_not_accessible": string;
            "contract_line_not_found": string;
            "contract_line_not_of_customer": string;
            "contract_line_not_of_pick_up_addresses": string;
            "contract_line_not_of_waste_producers": string;
        };
        "customer": {
            "customer_not_accessible": string;
            "customer_not_found": string;
            "customer_not_provided": string;
            "selected_customer_filter_mismatch": string;
        };
        "delivery-address": {
            "delivery-address_not_accessible": string;
            "delivery-address_not_found": string;
            "delivery-address_not_provided": string;
        };
        "dynamic-tables": {
            "column_not_filterable": string;
            "column_not_found": string;
            "column_not_hidable": string;
            "column_not_sortable": string;
            "duplicate_column": string;
            "duplicate_view_name": string;
            "dynamic_table_view_not_found": string;
            "global_default_view_not_deletable": string;
            "invalid_global_default": string;
            "last_global_view_not_deletable": string;
            "unauthorized_manage_global_view": string;
            "unauthorized_delete_view": string;
            "unauthorized_update_view": string;
        };
        "ewc-code": {
            "ewc_code_not_found": string;
            "missing_ewc_levels": string;
        };
        "exceptions": {
            "internal_server_error": string;
            "service_unavailable": string;
        };
        "files": {
            "file_not_accessible": string;
            "file_not_found": string;
            "duplicate_file_uuid": string;
            "invalid_entity_part": string;
        };
        "generic": {
            "date_must_be_after": string;
            "field_must_be_null": string;
            "missing_required_field": string;
            "not_found": string;
            "unauthorized": string;
        };
        "invoice": {
            "invalid_invoice_status_combination_default": string;
            "not_found": string;
            "non_approve_or_rejectable_draft_invoice": string;
        };
        "nats": {
            "nats_cache_unavailable": string;
            "nats_client_unavailable": string;
        };
        "news": {
            "news-item-translation-exists": string;
            "news-item-translation-expected": string;
            "no-start-date-or-end-date-expected": string;
        };
        "packaging-request": {
            "packaging_request_already_submitted": string;
            "packaging_request_not_found": string;
            "invalid-transport-mode": string;
            "submit_only_packaging_request": string;
            "delete_only_packaging_request": string;
            "update_only_packaging_request": string;
            "copy_non_submitted_packaging_request": string;
            "invalid-packaging-request-copy": string;
        };
        "pick-up-address": {
            "not_found": string;
            "pick_up_address_not_accessible": string;
            "pick_up_address_not_provided": string;
        };
        "pick-up-request": {
            "contract-line-id-mismatch": string;
            "exactly_one_contract_line_expected": string;
            "invalid_pick_up_request_status_combination": string;
            "invalid_pick_up_request_status_combination_default": string;
            "invalid_total_quantity_pallets": string;
            "invalid_transport_mode": string;
            "invalid_transport_mode_default": string;
            "pick_up_request_already_submitted": string;
            "pick_up_request_not_found": string;
            "too_many_contract_lines": string;
            "copy_non_submitted_pick_up_request": string;
            "invalid-indascan-submit-status": string;
            "contract-line-not-found": string;
            "invalid-sap-waste-materials-update": string;
            "invalid-pick-up-request-copy": string;
        };
        "roles": {
            "role_name_already_in_use": string;
            "role_not_editable": string;
            "role_not_found": string;
        };
        "template": {
            "not-submittable": string;
        };
        "user": {
            "invalid_impersonation_token": string;
            "user_not_found": string;
        };
        "waste-inquiry": {
            "invalid_stable_temperature": string;
            "invalid_waste_inquiry_status_combination": string;
            "invalid_waste_inquiry_status_combination_default": string;
            "min_temperature_must_be_less_than_max": string;
            "no_analysis_report_files_expected": string;
            "no_option_expected_when_none_selected": string;
            "no_sds_files_expected": string;
            "no_svhc_extra_expected": string;
            "temperature_does_not_to_be_provided": string;
            "temperature_must_be_provided": string;
            "transport_options_invalid": string;
            "waste_inquiry_already_submitted": string;
            "not_found": string;
        };
        "waste-producer": {
            "waste_producer_not_accessible": string;
            "not_found": string;
        };
        "weekly-planning-request": {
            "needs-exactly-one-material": string;
            "not_submitted_error": string;
            "pick-up-request-id-mismatch": string;
            "weekly-planning-contract-sap-not-found": string;
            "weekly-planning-id-mismatch": string;
            "weekly_planning_request_already_submitted_error": string;
            "weekly_planning_request_not_found": string;
        };
        "document": {
            "not-found": string;
        };
    };
    "event-log": {
        "announcement": {
            "created": {
                "v1": string;
            };
            "deleted": {
                "v1": string;
            };
            "updated": {
                "v1": string;
            };
        };
        "contact": {
            "created": {
                "v1": string;
            };
            "deleted": {
                "v1": string;
            };
            "updated": {
                "v1": string;
            };
        };
        "dynamic-table-view": {
            "created": {
                "v1": string;
            };
            "deleted": {
                "v1": string;
            };
            "updated": {
                "v1": string;
            };
        };
        "file": {
            "created": {
                "v1": string;
            };
            "deleted": {
                "v1": string;
            };
            "uploaded": {
                "v1": string;
            };
        };
        "news-item": {
            "created": {
                "v1": string;
            };
            "deleted": {
                "v1": string;
            };
            "updated": {
                "v1": string;
            };
        };
        "notification": {
            "created": {
                "v1": string;
            };
            "preference": {
                "preset": {
                    "updated": {
                        "v1": string;
                    };
                };
            };
            "read": {
                "all": {
                    "v1": string;
                };
                "v1": string;
            };
            "types": {
                "migrated": {
                    "v1": string;
                };
            };
            "unread": {
                "v1": string;
            };
        };
        "packaging-request": {
            "copied": {
                "v1": string;
            };
            "created": {
                "v1": string;
            };
            "submitted": {
                "v1": string;
            };
            "deleted": {
                "v1": string;
            };
            "updated": {
                "v1": string;
            };
        };
        "pick-up-request": {
            "copied": {
                "v1": string;
            };
            "created": {
                "v1": string;
            };
            "deleted": {
                "v1": string;
            };
            "submitted": {
                "v1": string;
            };
            "updated": {
                "v1": string;
            };
        };
        "pick-up-request-template": {
            "created": {
                "v1": string;
            };
            "updated": {
                "v1": string;
            };
            "deleted": {
                "v1": string;
            };
        };
        "role": {
            "created": {
                "v1": string;
            };
            "deleted": {
                "v1": string;
            };
            "permissions": {
                "cache": {
                    "cleared": {
                        "v1": string;
                    };
                };
                "updated": {
                    "v1": string;
                };
            };
            "renamed": {
                "v1": string;
            };
        };
        "test-notification": {
            "sent": {
                "v1": string;
            };
        };
        "user": {
            "created": {
                "v1": string;
            };
            "default-notification-preferences": {
                "assigned": {
                    "v1": string;
                };
            };
            "notification": {
                "created": {
                    "v1": string;
                };
            };
            "role-assigned": {
                "v1": string;
            };
            "updated": {
                "v1": string;
            };
        };
        "waste-inquiry": {
            "copied": {
                "v1": string;
            };
            "created": {
                "v1": string;
            };
            "deleted": {
                "v1": string;
            };
            "submitted": {
                "v1": string;
            };
            "updated": {
                "v1": string;
            };
        };
        "weekly-planning-request": {
            "created": {
                "v1": string;
            };
            "submitted": {
                "v1": string;
            };
            "updated": {
                "v1": string;
            };
        };
    };
    "notification": {
        "test-notification": string;
        "user": {
            "created": string;
        };
    };
    "permissions": {
        "all_permissions": {
            "description": string;
            "group-name": string;
            "name": string;
        };
        "announcement": {
            "group-name": string;
            "manage": {
                "description": string;
                "name": string;
            };
        };
        "balanced-scorecard": {
            "group-name": string;
            "read": {
                "description": string;
                "name": string;
            };
        };
        "certificate": {
            "group-name": string;
            "read": {
                "description": string;
                "name": string;
            };
        };
        "contact": {
            "group-name": string;
            "manage": {
                "description": string;
                "name": string;
            };
            "read": {
                "description": string;
                "name": string;
            };
        };
        "contract-line": {
            "group-name": string;
            "manage": {
                "description": string;
                "name": string;
            };
            "read": {
                "description": string;
                "name": string;
            };
        };
        "document": {
            "bsc": {
                "description": string;
                "name": string;
            };
            "contract": {
                "description": string;
                "name": string;
            };
            "group-name": string;
            "manual": {
                "description": string;
                "name": string;
            };
            "master-table": {
                "description": string;
                "name": string;
            };
            "minutes-and-presentations": {
                "description": string;
                "name": string;
            };
            "quotation": {
                "description": string;
                "name": string;
            };
            "tfs": {
                "description": string;
                "name": string;
            };
            "transport": {
                "description": string;
                "name": string;
            };
        };
        "dynamic-table-view": {
            "group-name": string;
            "manage": {
                "description": string;
                "name": string;
            };
        };
        "ecmr": {
            "group-name": string;
            "read": {
                "description": string;
                "name": string;
            };
        };
        "event-log": {
            "group-name": string;
            "read": {
                "description": string;
                "name": string;
            };
        };
        "invoice": {
            "group-name": string;
            "read": {
                "description": string;
                "name": string;
            };
            "manage": {
                "description": string;
                "name": string;
            };
        };
        "jobs": {
            "group-name": string;
            "read": {
                "detail": {
                    "description": string;
                    "name": string;
                };
                "index": {
                    "description": string;
                    "name": string;
                };
            };
        };
        "news-item": {
            "group-name": string;
            "manage": {
                "description": string;
                "name": string;
            };
        };
        "newsletter": {
            "group-name": string;
            "subscribe": {
                "description": string;
                "name": string;
            };
        };
        "packaging-request": {
            "group-name": string;
            "manage": {
                "description": string;
                "name": string;
            };
            "read": {
                "description": string;
                "name": string;
            };
        };
        "pick-up-request": {
            "group-name": string;
            "manage": {
                "description": string;
                "name": string;
            };
            "read": {
                "description": string;
                "name": string;
            };
        };
        "power-bi": {
            "group-name": string;
            "read": {
                "description": string;
                "name": string;
            };
        };
        "role": {
            "group-name": string;
            "manage": {
                "description": string;
                "name": string;
            };
            "read": {
                "description": string;
                "name": string;
            };
        };
        "send_push_notification": {
            "description": string;
            "group-name": string;
            "name": string;
        };
        "useful-link": {
            "ecmr": {
                "description": string;
                "name": string;
            };
            "group-name": string;
            "permits": {
                "description": string;
                "name": string;
            };
            "reporting": {
                "description": string;
                "name": string;
            };
        };
        "user": {
            "group-name": string;
            "impersonate": {
                "description": string;
                "name": string;
            };
            "manage": {
                "description": string;
                "name": string;
            };
            "read": {
                "description": string;
                "name": string;
            };
        };
        "waste-inquiry": {
            "group-name": string;
            "manage": {
                "description": string;
                "name": string;
            };
            "read": {
                "description": string;
                "name": string;
            };
        };
        "weekly-planning-request": {
            "group-name": string;
            "manage": {
                "description": string;
                "name": string;
            };
            "read": {
                "description": string;
                "name": string;
            };
        };
        "typesense": {
            "group-name": string;
            "name": string;
            "description": string;
        };
    };
};
/* prettier-ignore */
export type I18nPath = Path<I18nTranslations>;
