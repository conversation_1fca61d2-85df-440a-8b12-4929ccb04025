import { PickUpRequest } from '../entities/pick-up-request.entity.js'

export class PickUpRequestGeneralInfoValidationUtil {
  private readonly pickUpRequest: PickUpRequest
  private readonly isSubmit: boolean

  constructor (
    pickUpRequest: PickUpRequest,
    isSubmit: boolean
  ) {
    this.pickUpRequest = pickUpRequest
    this.isSubmit = isSubmit
  }

  // Customer
  get isCustomerIdAllowed (): boolean {
    return !this.pickUpRequest.isWprPickUpRequest
      || this.isSubmit
      || this.pickUpRequest.customerName !== null
  }

  get isCustomerIdRequired (): boolean {
    return this.isCustomerIdAllowed
  }

  get isCustomerNameAllowed (): boolean {
    return !this.pickUpRequest.isWprPickUpRequest
      || this.isSubmit
      || this.pickUpRequest.customerId !== null
  }

  get isCustomerNameRequired (): boolean {
    return this.isCustomerNameAllowed
  }

  // Waste producer
  get isWasteProducerIdAllowed (): boolean {
    return !this.pickUpRequest.isWprPickUpRequest
      || this.isSubmit
      || this.pickUpRequest.wasteProducerName !== null
  }

  get isWasteProducerIdRequired (): boolean {
    return this.isWasteProducerIdAllowed && !this.pickUpRequest.isWprPickUpRequest
  }

  get isWasteProducerNameAllowed (): boolean {
    return !this.pickUpRequest.isWprPickUpRequest
      || this.isSubmit
      || this.pickUpRequest.wasteProducerId !== null
  }

  get isWasteProducerNameRequired (): boolean {
    return this.isWasteProducerNameAllowed && !this.pickUpRequest.isWprPickUpRequest
  }

  // Pick-up addresses
  get isPickUpAddressIdsAllowed (): boolean {
    return !this.pickUpRequest.isWprPickUpRequest
      || this.isSubmit
      || (this.pickUpRequest.pickUpAddressNames?.length ?? 0) > 0
  }

  get isPickUpAddressIdsRequired (): boolean {
    return false
  }

  get isPickUpAddressNamesAllowed (): boolean {
    return !this.pickUpRequest.isWprPickUpRequest
      || this.isSubmit
      || (this.pickUpRequest.pickUpAddressIds?.length ?? 0) > 0
  }

  get isPickUpAddressNamesRequired (): boolean {
    return false
  }
}
