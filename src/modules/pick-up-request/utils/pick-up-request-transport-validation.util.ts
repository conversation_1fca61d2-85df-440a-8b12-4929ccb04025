import { SAP_COUNTRY_CODE } from '../../sap/constants/country-code.constant.js'
import { PickUpRequest } from '../entities/pick-up-request.entity.js'
import { PickUpTransportMode } from '../enums/pick-up-transport-mode.enum.js'

export interface PickUpRequestSapTransportValidation {
  isPoNumberRequired?: boolean
  isCostCenterRequired?: boolean
  customerCountryCode?: string
}

export class PickUpRequestTransportValidationUtil {
  private readonly pickUpRequest: PickUpRequest
  private readonly sapValidationInfo?: PickUpRequestSapTransportValidation

  constructor (
    pickUpRequest: PickUpRequest,
    sapValidationInfo?: PickUpRequestSapTransportValidation
  ) {
    this.pickUpRequest = pickUpRequest
    this.sapValidationInfo = sapValidationInfo
  }

  get estimatedWeightOrVolumeValueAllowed (): boolean {
    return this.pickUpRequest.transportMode === PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK
      || this.pickUpRequest.transportMode === PickUpTransportMode.BULK_SKIPS_CONTAINER
      || this.pickUpRequest.transportMode === PickUpTransportMode.BULK_VACUUM_TANKERS_ROAD_TANKERS
      || this.pickUpRequest.transportMode === PickUpTransportMode.BULK_ISO_TANK
  }

  get estimatedWeightOrVolumeValueRequired (): boolean {
    return this.pickUpRequest.transportMode === PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK
      || this.pickUpRequest.transportMode === PickUpTransportMode.BULK_SKIPS_CONTAINER
      || this.pickUpRequest.transportMode === PickUpTransportMode.BULK_VACUUM_TANKERS_ROAD_TANKERS
      || this.pickUpRequest.transportMode === PickUpTransportMode.BULK_ISO_TANK
  }

  get estimatedWeightOrVolumeUnitAllowed (): boolean {
    return this.pickUpRequest.transportMode === PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK
      || this.pickUpRequest.transportMode === PickUpTransportMode.BULK_SKIPS_CONTAINER
      || this.pickUpRequest.transportMode === PickUpTransportMode.BULK_VACUUM_TANKERS_ROAD_TANKERS
      || this.pickUpRequest.transportMode === PickUpTransportMode.BULK_ISO_TANK
  }

  get estimatedWeightOrVolumeUnitRequired (): boolean {
    return this.pickUpRequest.transportMode === PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK
      || this.pickUpRequest.transportMode === PickUpTransportMode.BULK_SKIPS_CONTAINER
      || this.pickUpRequest.transportMode === PickUpTransportMode.BULK_VACUUM_TANKERS_ROAD_TANKERS
      || this.pickUpRequest.transportMode === PickUpTransportMode.BULK_ISO_TANK
  }

  get packagingTypeAllowed (): boolean {
    return this.pickUpRequest.transportMode === PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK
  }

  get packagingTypeRequired (): boolean {
    return this.pickUpRequest.transportMode === PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK
  }

  get quantityPackagesAllowed (): boolean {
    return this.pickUpRequest.transportMode === PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK
  }

  get quantityPackagesRequired (): boolean {
    return this.pickUpRequest.transportMode === PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK
  }

  get quantityLabelsAllowed (): boolean {
    return this.pickUpRequest.transportMode === PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK
  }

  get quantityLabelsRequired (): boolean {
    return this.quantityLabelsAllowed
      && this.sapValidationInfo?.customerCountryCode !== SAP_COUNTRY_CODE.DE
  }

  get quantityPalletsAllowed (): boolean {
    return this.pickUpRequest.transportMode === PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK
  }

  get quantityPalletsRequired (): boolean {
    return this.quantityPalletsAllowed
      && this.sapValidationInfo?.customerCountryCode === SAP_COUNTRY_CODE.DE
  }

  get unNumberAllowed (): boolean {
    return this.pickUpRequest.transportMode === PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK
      || this.pickUpRequest.transportMode === PickUpTransportMode.BULK_SKIPS_CONTAINER
      || this.pickUpRequest.transportMode === PickUpTransportMode.BULK_VACUUM_TANKERS_ROAD_TANKERS
      || this.pickUpRequest.transportMode === PickUpTransportMode.BULK_ISO_TANK
  }

  get unNumberRequired (): boolean {
    return this.pickUpRequest.transportMode === PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK
      || this.pickUpRequest.transportMode === PickUpTransportMode.BULK_SKIPS_CONTAINER
      || this.pickUpRequest.transportMode === PickUpTransportMode.BULK_VACUUM_TANKERS_ROAD_TANKERS
      || this.pickUpRequest.transportMode === PickUpTransportMode.BULK_ISO_TANK
  }

  get costCenterAllowed (): boolean {
    return this.pickUpRequest.transportMode === PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK
      || this.pickUpRequest.transportMode === PickUpTransportMode.BULK_SKIPS_CONTAINER
      || this.pickUpRequest.transportMode === PickUpTransportMode.BULK_VACUUM_TANKERS_ROAD_TANKERS
      || this.pickUpRequest.transportMode === PickUpTransportMode.BULK_ISO_TANK
  }

  get costCenterRequired (): boolean {
    return this.sapValidationInfo?.isCostCenterRequired ?? false
  }

  get poNumberAllowed (): boolean {
    return this.pickUpRequest.transportMode === PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK
      || this.pickUpRequest.transportMode === PickUpTransportMode.BULK_SKIPS_CONTAINER
      || this.pickUpRequest.transportMode === PickUpTransportMode.BULK_VACUUM_TANKERS_ROAD_TANKERS
      || this.pickUpRequest.transportMode === PickUpTransportMode.BULK_ISO_TANK
  }

  get poNumberRequired (): boolean {
    return this.sapValidationInfo?.isPoNumberRequired ?? false
  }

  get containerTypeAllowed (): boolean {
    return this.pickUpRequest.transportMode === PickUpTransportMode.BULK_SKIPS_CONTAINER
  }

  get containerTypeRequired (): boolean {
    return this.containerTypeAllowed
      && this.sapValidationInfo?.customerCountryCode === SAP_COUNTRY_CODE.DE
  }

  get containerNumberAllowed (): boolean {
    return this.pickUpRequest.transportMode === PickUpTransportMode.BULK_SKIPS_CONTAINER
      || this.pickUpRequest.transportMode === PickUpTransportMode.BULK_ISO_TANK
  }

  get containerNumberRequired (): boolean {
    return false
  }

  get isContainerVolumeSizeAllowed (): boolean {
    return true
  }

  get isContainerVolumeSizeRequired (): boolean {
    return this.pickUpRequest.transportMode === PickUpTransportMode.BULK_SKIPS_CONTAINER
      && (this.sapValidationInfo?.customerCountryCode === SAP_COUNTRY_CODE.DE
        || this.sapValidationInfo?.customerCountryCode === SAP_COUNTRY_CODE.IE)
  }

  get containerTransportTypeAllowed (): boolean {
    return this.pickUpRequest.transportMode === PickUpTransportMode.BULK_SKIPS_CONTAINER
      || this.pickUpRequest.transportMode === PickUpTransportMode.BULK_ISO_TANK
  }

  get containerTransportTypeRequired (): boolean {
    return this.pickUpRequest.transportMode === PickUpTransportMode.BULK_SKIPS_CONTAINER
      || this.pickUpRequest.transportMode === PickUpTransportMode.BULK_ISO_TANK
  }

  get isContainerCoverAllowed (): boolean {
    return this.pickUpRequest.transportMode === PickUpTransportMode.BULK_SKIPS_CONTAINER
  }

  get isContainerCoverRequired (): boolean {
    return false
  }

  get isTankerTypeAllowed (): boolean {
    return this.pickUpRequest.transportMode == PickUpTransportMode.BULK_VACUUM_TANKERS_ROAD_TANKERS
  }

  get isTankerTypeRequired (): boolean {
    return false
  }

  get isReconciliationNumberAllowed (): boolean {
    return this.sapValidationInfo?.customerCountryCode === SAP_COUNTRY_CODE.DE
  }

  get isReconciliationNumberRequired (): boolean {
    return false
  }

  get isTotalQuantityPalletsAllowed (): boolean {
    return this.pickUpRequest.transportMode == PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK
  }

  get isTotalQuantityPalletsRequired (): boolean {
    return this.pickUpRequest.transportMode == PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK
  }

  get isReturnPackagingAllowed (): boolean {
    return this.pickUpRequest.transportMode == PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK
  }

  get isReturnPackagingRequired (): boolean {
    return false
  }

  get isPackagingRemarkAllowed (): boolean {
    return this.pickUpRequest.transportMode == PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK
  }

  get isPackagingRemarkRequired (): boolean {
    return this.pickUpRequest.isReturnPackaging === true
  }

  get isHazardInducersAllowed (): boolean {
    return this.sapValidationInfo?.customerCountryCode === SAP_COUNTRY_CODE.DE
  }

  isHazardInducersRequired (unNumberHazardous: boolean): boolean {
    return this.isHazardInducersAllowed && unNumberHazardous
  }

  get isQuantityContainersAllowed (): boolean {
    return this.pickUpRequest.transportMode == PickUpTransportMode.BULK_SKIPS_CONTAINER
  }

  get isQuantityContainersRequired (): boolean {
    return false
  }

  isTfsNumberAllowed (tfs: boolean): boolean {
    return tfs
  }

  get isTfsNumberRequired (): boolean {
    return false
  }

  isSerialNumberAllowed (tfs: boolean): boolean {
    return tfs
  }

  get isSerialNumberRequired (): boolean {
    return false
  }
}
