import { PickUpRequest } from '../entities/pick-up-request.entity.js'

export class PickUpRequestPlanningValidationUtil {
  private readonly pickUpRequest: PickUpRequest

  constructor (pickUpRequest: PickUpRequest) {
    this.pickUpRequest = pickUpRequest
  }

  get isStartDateAllowed (): boolean {
    return true
  }

  get isStartDateRequired (): boolean {
    return this.isStartDateAllowed
  }

  get isEndDateAllowed (): boolean {
    return !this.pickUpRequest.isWprPickUpRequest
  }

  get isEndDateRequired (): boolean {
    return false
  }

  get isRemarksAllowed (): boolean {
    return !this.pickUpRequest.isWprPickUpRequest
  }

  get isRemarksRequired (): boolean {
    return false
  }

  get isSendCopyToContactAllowed (): boolean {
    return !this.pickUpRequest.isWprPickUpRequest
  }

  get isSendCopyToContactRequired (): boolean {
    return false
  }

  get isStartTimeAllowed (): boolean {
    return this.pickUpRequest.isWprPickUpRequest
  }

  get isStartTimeRequired (): boolean {
    return false
  }

  get isAdditionalFilesAllowed (): boolean {
    return !this.pickUpRequest.isWprPickUpRequest
  }

  get isAdditionalFilesRequired (): boolean {
    return false
  }

  get isWicRequired (): boolean {
    return this.pickUpRequest.needsWicConfirmation
  }
}
