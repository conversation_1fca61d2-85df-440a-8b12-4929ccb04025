import assert from 'assert'
import { ApiProperty } from '@nestjs/swagger'
import { CustomerResponse } from '../../customer/responses/customer.response.js'
import { WasteProducerResponse } from '../../waste-producer/responses/waste-producer.response.js'
import { PickUpAddressResponse } from '../../pick-up-address/responses/pick-up-address.response.js'
import { FileLinkResponse } from '../../files/responses/file-link.response.js'
import { ContactTypeResponse } from '../../contact/responses/contact.response.js'
import { MaterialResponse } from '../use-cases/view-pick-up-request/view-pick-up-request-material.response.js'
import { PickUpTransportMode } from '../enums/pick-up-transport-mode.enum.js'
import { PickUpRequest } from '../entities/pick-up-request.entity.js'
import { PickUpRequestStatus } from '../enums/pick-up-request-status.enum.js'
import { UniformPickUpRequest } from '../types/uniform-pick-up-request.type.js'
import { UniformDetailPickUpRequest } from '../types/uniform-pick-up-request-detail.type.js'
import { SapPickUpAddress } from '../../pick-up-address/types/sap-pick-up-address.type.js'
import { PickUpAddress } from '../../pick-up-address/types/pick-up-address.type.js'
import { FileLink } from '../../files/entities/file-link.entity.js'
import { ExternalFile } from '../../files/types/external-file.type.js'
import { PackagingRequestMaterialResponse } from '../../packaging-request/use-cases/view-packaging-request/packaging-request-material.response.js'

export abstract class AbstractViewPickUpRequestResponse {
  @ApiProperty({ type: String, enum: PickUpRequestStatus, enumName: 'PickUpRequestStatus' })
  status: PickUpRequestStatus

  @ApiProperty({ type: CustomerResponse, nullable: true })
  customer: CustomerResponse | null

  @ApiProperty({ type: WasteProducerResponse, nullable: true })
  wasteProducer: WasteProducerResponse | null

  @ApiProperty({ type: PickUpAddressResponse, isArray: true })
  pickUpAddresses: PickUpAddressResponse[]

  @ApiProperty({ type: String, enum: PickUpTransportMode, enumName: 'PickUpTransportMode', nullable: true })
  transportMode: PickUpTransportMode | null

  @ApiProperty({ type: Boolean, nullable: true })
  isTransportByIndaver: boolean | null

  @ApiProperty({ type: String, nullable: true, format: 'date' })
  startDate: string | null

  @ApiProperty({ type: String, nullable: true, format: 'date' })
  endDate: string | null

  @ApiProperty({ type: String, nullable: true })
  remarks: string | null

  @ApiProperty({ type: Number, nullable: true })
  totalQuantityPallets: number | null

  @ApiProperty({ type: Boolean, nullable: true })
  isReturnPackaging: boolean | null

  @ApiProperty({ type: String, nullable: true })
  packagingRemark: string | null

  @ApiProperty({ type: ContactTypeResponse, isArray: true })
  sendCopyToContacts: ContactTypeResponse[]

  @ApiProperty({ type: MaterialResponse, isArray: true })
  materials: MaterialResponse[]

  @ApiProperty({ type: PackagingRequestMaterialResponse, isArray: true })
  packagingRequestMaterials: PackagingRequestMaterialResponse[]

  @ApiProperty({ type: FileLinkResponse, isArray: true })
  additionalFiles: FileLinkResponse[]

  @ApiProperty({ type: Boolean, nullable: true })
  isWicConfirmed: boolean | null

  @ApiProperty({ type: Boolean })
  needsWicConfirmation: boolean

  constructor (pickUpRequest: PickUpRequest | UniformDetailPickUpRequest) {
    assert(pickUpRequest.customer !== undefined)
    assert(pickUpRequest.wasteProducer !== undefined)
    assert(pickUpRequest.pickUpAddresses !== undefined)
    assert(pickUpRequest.materialsWithContractLine !== undefined)
    assert(pickUpRequest.additionalFiles !== undefined)

    this.status = pickUpRequest instanceof UniformPickUpRequest
      || pickUpRequest instanceof UniformDetailPickUpRequest
      ? pickUpRequest.status
      : PickUpRequestStatus.DRAFT
    this.customer = pickUpRequest.customer !== null
      ? new CustomerResponse(pickUpRequest.customer)
      : null
    this.wasteProducer = pickUpRequest.wasteProducer !== null
      ? new WasteProducerResponse(pickUpRequest.wasteProducer)
      : null
    this.pickUpAddresses = pickUpRequest.pickUpAddresses.map(
      (address: PickUpAddress | SapPickUpAddress) => new PickUpAddressResponse(address)
    )
    this.transportMode = pickUpRequest.transportMode
    this.isTransportByIndaver = pickUpRequest.isTransportByIndaver
    this.startDate = pickUpRequest.startDate
    this.endDate = pickUpRequest.endDate
    this.remarks = pickUpRequest.remarks
    this.sendCopyToContacts = pickUpRequest.sendCopyToContacts
    this.totalQuantityPallets = pickUpRequest.totalQuantityPallets
    this.isReturnPackaging = pickUpRequest.isReturnPackaging
    this.packagingRemark = pickUpRequest.packagingRemark
    this.materials = pickUpRequest.materialsWithContractLine.map((material) => {
      return new MaterialResponse(material)
    })
    this.packagingRequestMaterials = pickUpRequest.packagingRequestMaterials.map(
      material => new PackagingRequestMaterialResponse(material)
    )
    this.additionalFiles = pickUpRequest.additionalFiles.map(
      (file: FileLink | ExternalFile) => new FileLinkResponse(file)
    )
    this.isWicConfirmed = pickUpRequest.isWicConfirmed
    this.needsWicConfirmation = pickUpRequest.needsWicConfirmation
  }
}
