import { <PERSON>umn, CreateDateColumn, Entity, Index, JoinColumn, ManyToOne, PrimaryGeneratedColumn, Relation, UpdateDateColumn } from 'typeorm'
import { Customer } from '../../customer/types/customer.type.js'
import { WasteProducer } from '../../waste-producer/types/waste-producer.type.js'
import { PickUpAddress } from '../../pick-up-address/types/pick-up-address.type.js'
import { PickUpTransportMode } from '../enums/pick-up-transport-mode.enum.js'
import { User } from '../../../app/users/entities/user.entity.js'
import { PickUpRequestMaterial } from '../types/pick-up-request-material.type.js'
import { Contact } from '../../contact/types/contact.type.js'
import { PickUpRequestMaterialWithContractLine } from '../types/pick-up-request-material-with-contract-line.type.js'
import { FileLink } from '../../files/entities/file-link.entity.js'
import { WeeklyPlanningRequest } from '../../weekly-planning-request/entities/weekly-planning-request.entity.js'
import { PackagingRequestMaterial } from '../types/packaging-request-material.type.js'
import { Templatable } from '../../template/abstracts/templatable.abstract.js'
import { WIC_PROCESS_CODE } from '../constants/wic-process-code.constant.js'

@Entity()
export class PickUpRequest extends Templatable {
  @PrimaryGeneratedColumn('uuid')
  uuid: string

  @CreateDateColumn({ precision: 3 })
  createdAt: Date

  @UpdateDateColumn({ precision: 3 })
  updatedAt: Date

  @Column({ type: 'varchar', nullable: true })
  @Index()
  customerId: string | null

  @Column({ type: 'varchar', nullable: true })
  @Index()
  wasteProducerId: string | null

  @Column({ type: 'varchar', array: true, default: [] })
  @Index()
  pickUpAddressIds: string[]

  @Column({ type: 'varchar', nullable: true })
  @Index()
  deliveryAddressId: string | null

  @Index()
  @Column({ type: 'enum', enum: PickUpTransportMode, nullable: true })
  transportMode: PickUpTransportMode | null

  @Index()
  @Column({ type: 'boolean', nullable: true })
  isTransportByIndaver: boolean | null

  @Column({ type: 'jsonb', default: [] })
  materials: PickUpRequestMaterial[]

  @Column({ type: 'jsonb', default: [] })
  packagingRequestMaterials: PackagingRequestMaterial[]

  @Column({ type: 'boolean', default: false })
  isOnlyPackagingRequest: boolean

  @Index()
  @Column({ type: 'date', nullable: true })
  startDate: string | null

  @Index()
  @Column({ type: 'date', nullable: true })
  endDate: string | null

  @Column({ type: 'varchar', nullable: true })
  remarks: string | null

  @Column({ type: 'jsonb', default: [] })
  sendCopyToContacts: Contact[]

  @Index()
  @Column({ type: 'timestamp', nullable: true })
  submittedOn: Date | null

  @Index()
  @Column({ type: 'varchar', nullable: true })
  requestNumber: string | null

  @Column({ type: 'integer', nullable: true })
  totalQuantityPallets: number | null

  @Column({ type: 'boolean', nullable: true })
  isReturnPackaging: boolean | null

  @Column({ type: 'varchar', nullable: true })
  packagingRemark: string | null

  @Column({ type: 'time', nullable: true })
  startTime: string | null

  @Column({ type: 'uuid' })
  @Index()
  createdByUserUuid: string

  @ManyToOne(() => User, user => user.wasteInquiries)
  @JoinColumn({ name: 'created_by_user_uuid' })
  createdByUser?: Relation<User>

  @Column({ type: 'uuid', nullable: true })
  @Index()
  weeklyPlanningRequestUuid: string | null

  @ManyToOne(() => WeeklyPlanningRequest,
    weeklyPlanningRequest => weeklyPlanningRequest.pickUpRequests,
    { nullable: true }
  )
  @JoinColumn({ name: 'weekly_planning_request_uuid' })
  weeklyPlanningRequest?: Relation<WeeklyPlanningRequest> | null

  @Column({ type: 'boolean', nullable: true })
  isWicConfirmed: boolean | null

  get isWprPickUpRequest (): boolean {
    return this.weeklyPlanningRequestUuid !== null
  }

  get needsWicConfirmation (): boolean {
    return this.materials.some(material => material.processCode === WIC_PROCESS_CODE)
      || this.materialsWithContractLine.some(material => material.processCode === WIC_PROCESS_CODE)
  }

  customer?: Customer | null
  wasteProducer?: WasteProducer | null
  pickUpAddresses?: PickUpAddress[]
  deliveryAddress?: PickUpAddress | null
  additionalFiles?: FileLink[]
  materialsWithContractLine: PickUpRequestMaterialWithContractLine[] = []
}
