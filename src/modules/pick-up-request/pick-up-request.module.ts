import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { ContractLineModule } from '../contract-line/contract-line.module.js'
import { FileModule } from '../files/file.module.js'
import { File } from '../files/entities/file.entity.js'
import { SapModule } from '../sap/sap.module.js'
import { CustomerModule } from '../customer/customer.module.js'
import { PickUpRequest } from './entities/pick-up-request.entity.js'
import { CreatePickUpRequestModule } from './use-cases/create-pick-up-request/create-pick-up-request.module.js'
import { ViewPickUpRequestModule } from './use-cases/view-pick-up-request/view-pick-up-request.module.js'
import { UpdatePickUpRequestModule } from './use-cases/update-pick-up-request/update-pick-up-request.module.js'
import { SubmitPickUpRequestModule } from './use-cases/submit-pick-up-request/submit-pick-up-request.module.js'
import { PickUpRequestTransportValidator } from './validators/pick-up-request-transport.validator.js'
import { PickUpRequestGeneralInfoValidator } from './validators/pick-up-request-general-info.validator.js'
import { PickUpRequestPlanningValidator } from './validators/pick-up-request-planning.validator.js'
import { PickUpRequestWasteDetailValidator } from './validators/pick-up-request-waste-detail.validator.js'
import { GetIsPoNumberAndCostCenterRequiredModule } from './use-cases/get-is-po-reference-required/get-is-po-number-and-cost-center-required.module.js'
import { ViewPickUpRequestIndexModule } from './use-cases/view-pick-up-request-index/view-pick-up-request-index.module.js'
import { BulkDeletePickUpRequestModule } from './use-cases/bulk-delete-pick-up-request/bulk-delete-pick-up-request.module.js'
import { ViewPickUpRequestSapModule } from './use-cases/view-pick-up-request-sap/view-pick-up-request-sap.module.js'
import { CopyPickUpRequestSapModule } from './use-cases/copy-pick-up-request-sap/copy-pick-up-request-sap.module.js'
import { UpdatePickUpRequestSapModule } from './use-cases/update-pick-up-request-sap/update-pick-up-request-sap.module.js'
import { SubmitPickUpRequestSapModule } from './use-cases/submit-pick-up-request-sap/submit-pick-up-request-sap.module.js'
import { CreatePickUpRequestTemplateModule } from './use-cases/create-pick-up-request-template/create-pick-up-request-template.module.js'
import { ViewPickUpRequestTemplateIndexModule } from './use-cases/view-pick-up-request-template-index/view-pick-up-request-template-index.module.js'
import { UpdatePickUpRequestTemplateModule } from './use-cases/update-pick-up-request-template/update-pick-up-request-template.module.js'
import { BulkDeletePickUpRequestTemplatesModule } from './use-cases/bulk-delete-pick-up-request-templates/bulk-delete-pick-up-request-templates.module.js'
import { ViewPickUpRequestTemplateModule } from './use-cases/view-pick-up-request-template/view-pick-up-request-template.module.js'
import { CreatePickUpRequestFromTemplateModule } from './use-cases/create-pick-up-request-from-template/create-pick-up-request-from-template.module.js'
import { UploadDocumentSubmittedPickUpRequestModule } from './use-cases/upload-document-submitted-pick-up-request/upload-document-submitted-pick-up-request.module.js'
import { DownloadDocumentSubmittedPickUpRequestModule } from './use-cases/download-document-submitted-pick-up-request/download-document-submitted-pick-up-request.module.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([PickUpRequest, File]),

    GetIsPoNumberAndCostCenterRequiredModule,
    ViewPickUpRequestIndexModule,
    CreatePickUpRequestModule,
    ViewPickUpRequestModule,
    UpdatePickUpRequestModule,
    SubmitPickUpRequestModule,
    BulkDeletePickUpRequestModule,
    ViewPickUpRequestSapModule,
    CreatePickUpRequestTemplateModule,
    ViewPickUpRequestTemplateModule,
    ViewPickUpRequestTemplateIndexModule,
    UpdatePickUpRequestTemplateModule,
    BulkDeletePickUpRequestTemplatesModule,
    CreatePickUpRequestFromTemplateModule,
    UploadDocumentSubmittedPickUpRequestModule,
    DownloadDocumentSubmittedPickUpRequestModule,

    SapModule,
    ContractLineModule,
    FileModule,
    CopyPickUpRequestSapModule,
    UpdatePickUpRequestSapModule,
    SubmitPickUpRequestSapModule,
    CustomerModule
  ],
  providers: [
    PickUpRequestGeneralInfoValidator,
    PickUpRequestTransportValidator,
    PickUpRequestWasteDetailValidator,
    PickUpRequestPlanningValidator
  ],
  exports: [
    PickUpRequestGeneralInfoValidator,
    PickUpRequestTransportValidator,
    PickUpRequestWasteDetailValidator,
    PickUpRequestPlanningValidator
  ]
})
export class PickUpRequestModule {}
