import { Injectable } from '@nestjs/common'
import { Any, Repository } from 'typeorm'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { MapUniformPickUpRequestDetailSapService } from '../../services/map-uniform-pick-up-request-detail-sap.service.js'
import { NotFoundError } from '../../../exceptions/generic/not-found.error.js'
import { SapQuery } from '../../../sap/query/sap-query.js'
import { SapGetPickUpRequestDetailUseCase } from '../../../sap/use-cases/get-pick-up-request-detail/get-pick-up-request-detail.use-case.js'
import { SapGetPickUpRequestDetailResponse } from '../../../sap/use-cases/get-pick-up-request-detail/get-pick-up-request-detail.response.js'
import { SapGetPickUpRequestCommentUseCase } from '../../../sap/use-cases/get-pick-up-request-comment/get-pick-up-request-comment.use-case.js'
import { SapGetPickUpRequestCommentResponse } from '../../../sap/use-cases/get-pick-up-request-comment/get-pick-up-request-comment.response.js'
import { SapGetPickUpRequestContactsUseCase } from '../../../sap/use-cases/get-pick-up-request-contacts/get-pick-up-request-contacts.use-case.js'
import { SapGetPickUpRequestContactsResponse } from '../../../sap/use-cases/get-pick-up-request-contacts/get-pick-up-request-contacts.response.js'
import { SapGetPickUpRequestAttachmentsUseCase } from '../../../sap/use-cases/get-pick-up-request-attachments/get-pick-up-request-attachments.use-case.js'
import { UniformDetailPickUpRequest } from '../../types/uniform-pick-up-request-detail.type.js'
import { UnNumber } from '../../../un-number/entities/un-number.entity.js'
import { SapGetPickUpRequestAttachmentsResponse } from '../../../sap/use-cases/get-pick-up-request-attachments/get-pick-up-request-attachments.response.js'
import { ViewPickUpRequestSapResponse } from './view-pick-up-request-sap.response.js'
import { ViewPickUpRequestSapValidator } from './view-pick-up-request-sap.validator.js'

@Injectable()
export class ViewPickUpRequestSapUseCase {
  constructor (
    private readonly sapGetPickUpRequestDetail: SapGetPickUpRequestDetailUseCase,
    private readonly sapGetPickUpRequestComment: SapGetPickUpRequestCommentUseCase,
    private readonly sapGetPickUpRequestContacts: SapGetPickUpRequestContactsUseCase,
    private readonly sapGetPickUpRequestAttachments: SapGetPickUpRequestAttachmentsUseCase,
    private readonly validator: ViewPickUpRequestSapValidator,
    @InjectRepository(UnNumber)
    private readonly unNumberRepository: Repository<UnNumber>
  ) {}

  async execute (requestNumber: string): Promise<ViewPickUpRequestSapResponse> {
    const [
      sapDetailResponse,
      sapCommentResponse,
      sapContactsResponse,
      sapAttachmentsResponse
    ] = await Promise.all([
      this.sapGetPickUpRequestDetail.execute(this.getDetailSapQuery(requestNumber)),
      this.sapGetPickUpRequestComment.execute(this.getCommentSapQuery(requestNumber)),
      this.sapGetPickUpRequestContacts.execute(this.getContactsSapQuery(requestNumber)),
      this.sapGetPickUpRequestAttachments.execute(this.getAttachmentsSapQuery(requestNumber))
    ])

    if (sapDetailResponse.length === 0) {
      throw new NotFoundError()
    }

    const uniformPickUpRequest = MapUniformPickUpRequestDetailSapService
      .mapResultToUniformPickUpRequest(
        sapDetailResponse,
        sapCommentResponse.length > 0 ? sapCommentResponse[0] : null,
        sapContactsResponse,
        sapAttachmentsResponse
      )

    await this.validator.validate(uniformPickUpRequest)

    await this.loadUnNumberDetails(uniformPickUpRequest)

    return new ViewPickUpRequestSapResponse(uniformPickUpRequest)
  }

  private getDetailSapQuery (
    requestNumber: string
  ): SapQuery<SapGetPickUpRequestDetailResponse> {
    const sapQuery = new SapQuery<SapGetPickUpRequestDetailResponse>()
      .where('Reqno', requestNumber)

    return sapQuery
  }

  private getCommentSapQuery (
    requestNumber: string
  ): SapQuery<SapGetPickUpRequestCommentResponse> {
    const sapQuery = new SapQuery<SapGetPickUpRequestCommentResponse>()
      .where('Reqno', requestNumber)
      .setTop(1)

    return sapQuery
  }

  private getContactsSapQuery (
    requestNumber: string
  ): SapQuery<SapGetPickUpRequestContactsResponse> {
    const sapQuery = new SapQuery<SapGetPickUpRequestContactsResponse>()
      .where('Reqno', requestNumber)

    return sapQuery
  }

  private getAttachmentsSapQuery (
    requestNumber: string
  ): SapQuery<SapGetPickUpRequestAttachmentsResponse> {
    const sapQuery = new SapQuery<SapGetPickUpRequestAttachmentsResponse>()
      .where('Reqno', requestNumber)

    return sapQuery
  }

  private async loadUnNumberDetails (
    uniformPickUpRequest: UniformDetailPickUpRequest
  ): Promise<void> {
    const unNumbers = uniformPickUpRequest.materialsWithContractLine
      .map(material => material.unNumber)
      .filter(unNumber => unNumber != null)

    if (unNumbers.length === 0) return

    const uniqueUnNumbers = Array.from(new Set(unNumbers))
    const unNumberEntities = await this.unNumberRepository.find({
      where: {
        number: Any(uniqueUnNumbers)
      }
    })

    for (const material of uniformPickUpRequest.materialsWithContractLine) {
      if (material.unNumber == null) continue

      const unNumberEntity = unNumberEntities.find(u => u.number === material.unNumber)
      if (unNumberEntity !== undefined) {
        material.unNumberDescription = unNumberEntity.description
        material.packingGroup = unNumberEntity.packingGroup
        material.dangerLabel1 = unNumberEntity.dangerLabel1
        material.dangerLabel2 = unNumberEntity.dangerLabel2
        material.dangerLabel3 = unNumberEntity.dangerLabel3
        material.isHazardous = unNumberEntity.isHazardous
      }
    }
  }
}
