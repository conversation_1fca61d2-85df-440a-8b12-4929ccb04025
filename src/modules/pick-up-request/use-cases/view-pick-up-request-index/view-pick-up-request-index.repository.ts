import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import dayjs from 'dayjs'
import { SortDirection } from '@wisemen/pagination'
import { PaginatedOffsetQuery, typeormPagination } from '@wisemen/pagination'
import { Repository, Brackets, SelectQueryBuilder } from 'typeorm'
import { PickUpRequest } from '../../entities/pick-up-request.entity.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { ViewPickUpRequestIndexFilterQuery } from './query/view-pick-up-request-index.filter-query.js'
import { ViewPickUpRequestIndexSortKey, ViewPickUpRequestIndexSortQuery } from './query/view-pick-up-request-index-sort.query.js'

@Injectable()
export class ViewPickUpRequestIndexRepository {
  constructor (
    @InjectRepository(PickUpRequest)
    private readonly pickUpRequestRepository: Repository<PickUpRequest>,
    private readonly authContext: AuthContext
  ) {}

  async getDraftPickUpRequestsByUserUuid (
    userUuid: string,
    paginationQuery?: PaginatedOffsetQuery,
    filters?: ViewPickUpRequestIndexFilterQuery,
    sorts?: ViewPickUpRequestIndexSortQuery[]
  ): Promise<PickUpRequest[]> {
    const pagination = typeormPagination(paginationQuery)
    const dbQuery = this.pickUpRequestRepository.createQueryBuilder('pickUpRequest')
      .where('pickUpRequest.submittedOn IS NULL')
      .andWhere('pickUpRequest.createdByUserUuid = :userUuid', { userUuid })
      .andWhere('pickUpRequest.weeklyPlanningRequestUuid IS NULL')
      .andWhere('pickUpRequest.templateName IS NULL')
      .leftJoinAndSelect('pickUpRequest.createdByUser', 'createdByUser')
      .take(pagination.take)
      .skip(pagination.skip)

    if (this.authContext.getSelectedCustomerId() != null) {
      dbQuery.andWhere(
        new Brackets((qb) => {
          qb.where('pickUpRequest.customerId IS NULL')
            .orWhere('pickUpRequest.customerId = :selectedCustomerId', {
              selectedCustomerId: this.authContext.getSelectedCustomerId()
            })
        })
      )
    }

    this.applyFilters(dbQuery, filters)
    this.applySorts(dbQuery, sorts ?? [])

    return dbQuery.getMany()
  }

  private applyFilters (
    queryBuilder: SelectQueryBuilder<PickUpRequest>,
    filters?: ViewPickUpRequestIndexFilterQuery
  ): void {
    if (filters === undefined) {
      return
    }

    if (filters.requestNumber !== undefined) {
      queryBuilder.andWhere('pickUpRequest.requestNumber = :requestNumber', { requestNumber: filters.requestNumber })
    }

    if (filters.wasteMaterial !== undefined) {
      queryBuilder.andWhere(`EXISTS (
        SELECT 1 
        FROM jsonb_array_elements(pickUpRequest.materials) AS elem
        WHERE elem->>'wasteMaterial' ILIKE :wasteMaterial
        )`, { wasteMaterial: this.toLikeString(filters.wasteMaterial) })
    }

    if (filters.customerReference !== undefined) {
      queryBuilder.andWhere(`EXISTS (
        SELECT 1 
        FROM jsonb_array_elements(pickUpRequest.materials) AS elem
        WHERE elem->>'customerReference' ILIKE :customerReference
        )`, { customerReference: this.toLikeString(filters.customerReference) })
    }

    if (filters.contractNumber !== undefined) {
      queryBuilder.andWhere(`EXISTS (
        SELECT 1 
        FROM jsonb_array_elements(pickUpRequest.materials) AS elem
        WHERE elem->>'contractNumber' = :contractNumber
        )`, { contractNumber: filters.contractNumber })
    }

    if (filters.contractItem !== undefined) {
      queryBuilder.andWhere(`EXISTS (
        SELECT 1 
        FROM jsonb_array_elements(pickUpRequest.materials) AS elem
        WHERE elem->>'contractItem' = :contractItem
        )`, { contractItem: filters.contractItem })
    }

    if (filters.transportMode !== undefined) {
      queryBuilder.andWhere('pickUpRequest.transportMode = :transportMode', { transportMode: filters.transportMode })
    }

    if (filters.dateOfRequest !== undefined) {
      const from = dayjs(filters.dateOfRequest.from).startOf('day').toDate()
      const to = dayjs(filters.dateOfRequest.to).endOf('day').toDate()
      queryBuilder.andWhere(new Brackets((qb) => {
        qb.where('pickUpRequest.submittedOn >= :from', { from })
          .andWhere('pickUpRequest.submittedOn <= :to', { to })
      }))
    }

    if (filters.customerId !== undefined) {
      queryBuilder.andWhere('pickUpRequest.customerId = :customerId', { customerId: filters.customerId })
    }

    if (filters.wasteProducerId !== undefined) {
      queryBuilder.andWhere('pickUpRequest.wasteProducerId = :wasteProducerId', { wasteProducerId: filters.wasteProducerId })
    }

    if (filters.pickUpAddressId !== undefined) {
      queryBuilder.andWhere('pickUpRequest.pickUpAddressIds = ANY(:pickUpAddressId)', { pickUpAddressId: filters.pickUpAddressId })
    }

    if (filters.costCenter !== undefined) {
      queryBuilder.andWhere(`EXISTS (
        SELECT 1 
        FROM jsonb_array_elements(pickUpRequest.materials) AS elem
        WHERE elem->>'costCenter' ILIKE :costCenter
        )`, { costCenter: this.toLikeString(filters.costCenter) })
    }

    if (filters.isTransportByIndaver !== undefined) {
      queryBuilder.andWhere('pickUpRequest.isTransportByIndaver = :isTransportByIndaver', { isTransportByIndaver: filters.isTransportByIndaver })
    }

    if (filters.requestDate !== undefined) {
      const from = dayjs(filters.requestDate.from).startOf('day').toDate()
      const to = dayjs(filters.requestDate.to).endOf('day').toDate()

      queryBuilder.andWhere(new Brackets((qb) => {
        qb.where('pickUpRequest.startDate <= :to', { to })
          .andWhere('pickUpRequest.endDate >= :from', { from })
      }))
    }

    if (filters.isHazardous !== undefined) {
      queryBuilder.andWhere(`EXISTS (
        SELECT 1 
        FROM jsonb_array_elements(pickUpRequest.materials) AS elem
        WHERE (elem->>'isHazardous')::boolean = :isHazardous
        )`, { isHazardous: filters.isHazardous })
    }

    if (filters.nameOfApplicant !== undefined) {
      queryBuilder.andWhere(`(createdByUser.firstName || ' ' || createdByUser.lastName) ILIKE :applicantName`, { applicantName: this.toLikeString(filters.nameOfApplicant) })
    }

    if (filters.ewc !== undefined) {
      queryBuilder.andWhere(`EXISTS (
        SELECT 1 
        FROM jsonb_array_elements(pickUpRequest.materials) AS elem
        WHERE elem->>'ewcCode' = :ewc
        )`, { ewc: filters.ewc })
    }

    if (filters.containerNumber !== undefined) {
      queryBuilder.andWhere(`EXISTS (
        SELECT 1 
        FROM jsonb_array_elements(pickUpRequest.materials) AS elem
        WHERE elem->>'containerNumber' = :containerNumber
        )`, { containerNumber: filters.containerNumber })
    }
  }

  private toLikeString (value: string): string {
    return `%${value}%`
  }

  private applySorts (
    queryBuilder: SelectQueryBuilder<PickUpRequest>,
    sorts: ViewPickUpRequestIndexSortQuery[]
  ): void {
    if (sorts.length === 0) {
      queryBuilder
        .orderBy('pickUpRequest.startDate', 'DESC', 'NULLS LAST')
        .addOrderBy('pickUpRequest.uuid', 'DESC')
      return
    }

    for (const sort of sorts) {
      switch (sort.key) {
        case ViewPickUpRequestIndexSortKey.REQUEST_NUMBER:
          queryBuilder.orderBy('pickUpRequest.requestNumber', this.toSortDirection(sort.order))
          break
        case ViewPickUpRequestIndexSortKey.APPLICATION_DATE:
          queryBuilder.orderBy('pickUpRequest.submittedOn', this.toSortDirection(sort.order))
          break
        case ViewPickUpRequestIndexSortKey.WASTE_PRODUCER_ID:
          queryBuilder.orderBy('pickUpRequest.wasteProducerId', this.toSortDirection(sort.order))
          break
        case ViewPickUpRequestIndexSortKey.REQUESTED_START_DATE:
          queryBuilder.orderBy('pickUpRequest.startDate', this.toSortDirection(sort.order))
          break
      }
    }
  }

  private toSortDirection (direction: SortDirection): 'ASC' | 'DESC' {
    return direction === SortDirection.ASC ? 'ASC' : 'DESC'
  }
}
