import { Injectable } from '@nestjs/common'
import dayjs from 'dayjs'
import { typeormPagination } from '@wisemen/pagination'
import { AuthContext } from '../../../auth/auth.context.js'
import { mapPickUpRequestStatusToSapValue, PickUpRequestStatus } from '../../enums/pick-up-request-status.enum.js'
import { MapUniformPickUpRequestDbService } from '../../services/map-uniform-pick-up-request-db.service.js'
import { SapQuery } from '../../../sap/query/sap-query.js'
import { SapGetPickUpRequestIndexResponse } from '../../../sap/use-cases/get-pick-up-request-index/get-pick-up-request-index.response.js'
import { SapGetPickUpRequestIndexUseCase } from '../../../sap/use-cases/get-pick-up-request-index/get-pick-up-request-index.use-case.js'
import { MapUniformPickUpRequestSapService } from '../../services/map-uniform-pick-up-request-sap.service.js'
import { FilterOperator } from '../../../sap/enums/odata-filter-operator.enum.js'
import { UserWasteProducerAuthService } from '../../../auth/services/user-waste-producer-auth.service.js'
import { mapTransportModeToSapValue } from '../../enums/pick-up-transport-mode.enum.js'
import { SapDateFormatterService } from '../../../sap/services/sap-date-formatter.service.js'
import { SapBooleanFormatterService } from '../../../sap/services/sap-boolean-formatter.service.js'
import { exhaustiveCheck } from '../../../../utils/helpers/exhaustive-check.helper.js'
import { ViewPickUpRequestIndexSortKey } from './query/view-pick-up-request-index-sort.query.js'
import { ViewPickUpRequestIndexQuery } from './query/view-pick-up-request-index.query.js'
import { ViewPickUpRequestIndexRepository } from './view-pick-up-request-index.repository.js'
import { ViewPickUpRequestIndexResponse } from './view-pick-up-request-index.response.js'
import { ViewPickUpRequestIndexValidator } from './view-pick-up-request-index.validator.js'

@Injectable()
export class ViewPickUpRequestIndexUseCase {
  constructor (
    private readonly validator: ViewPickUpRequestIndexValidator,
    private readonly authContext: AuthContext,
    private readonly repository: ViewPickUpRequestIndexRepository,
    private readonly userWasteProducerAuthService: UserWasteProducerAuthService,
    private readonly sapGetPickUpRequestIndex: SapGetPickUpRequestIndexUseCase
  ) {}

  async execute (
    query: ViewPickUpRequestIndexQuery
  ): Promise<ViewPickUpRequestIndexResponse> {
    this.validator.validate(query)

    const user = this.authContext.getAuthOrFail()

    if (query.filter.statuses.includes(PickUpRequestStatus.DRAFT)) {
      const dbPickUpRequests = await this.repository.getDraftPickUpRequestsByUserUuid(
        user.uuid,
        query.pagination,
        query.filter,
        query.sort
      )

      const uniformPickUpRequests = MapUniformPickUpRequestDbService
        .mapResultsToUniformPickUpRequests(
          dbPickUpRequests
        )

      const pagination = typeormPagination(query.pagination)
      return new ViewPickUpRequestIndexResponse(
        uniformPickUpRequests,
        null,
        pagination.take,
        pagination.skip
      )
    }

    const sapQuery = await this.getSapQuery(query)
    const sapResponse = await this.sapGetPickUpRequestIndex.execute(sapQuery)

    const uniformPickUpRequests = MapUniformPickUpRequestSapService
      .mapResultsToUniformPickUpRequests(
        sapResponse.items
      )

    const pagination = typeormPagination(query.pagination)
    return new ViewPickUpRequestIndexResponse(
      uniformPickUpRequests,
      null,
      pagination.take,
      pagination.skip
    )
  }

  private async getSapQuery (
    query: ViewPickUpRequestIndexQuery
  ): Promise<SapQuery<SapGetPickUpRequestIndexResponse>> {
    const sapStatuses: string[] = []

    const filterStatuses = query.filter.statuses
    const filterStatusesWithoutIndaScan = filterStatuses
      .filter(status => status !== PickUpRequestStatus.INDASCAN_DRAFT)

    for (const status of filterStatusesWithoutIndaScan) {
      sapStatuses.push(...mapPickUpRequestStatusToSapValue(status))
    }

    const sapQuery = new SapQuery<SapGetPickUpRequestIndexResponse>(query, {
      defaultOrderBy: {
        column: 'Reqno',
        direction: 'desc'
      },
      keyMapper: (key: string) => this.mapSortKeyToSapKey(key as ViewPickUpRequestIndexSortKey)
    })
      .addSelect([
        'Reqno',
        'Status',
        'Arktx',
        'Kunnr',
        'Kunnrname',
        'Kunnry2',
        'Kunnry2name',
        'Kunnrwe',
        'Kunnrwename',
        'Yyklantmat',
        'Vbeln',
        'Posnr',
        'Wastetype',
        'Dateapplication',
        'Accountmanager',
        'Costcenter',
        'Transportby',
        'Requesteddate',
        'Confirmeddate',
        'Salesdoc',
        'Dangerous',
        'Nameapplicant',
        'Ordernumber',
        'Cont1nr',
        'Eural',
        'Tfsnumber',
        'Lifnry0name',
        'Lifnry3name',
        'Deliveryinfo',
        'Materialanalysis',
        'ReqDateTo'
      ])

    if (filterStatuses.includes(PickUpRequestStatus.INDASCAN_DRAFT)) {
      sapQuery.where((qb) => {
        return qb.where('Status', mapPickUpRequestStatusToSapValue(PickUpRequestStatus.INDASCAN_DRAFT)[0])
          .andWhere('Extid', '', FilterOperator.NOT_EQUAL)
      })

      if (filterStatusesWithoutIndaScan.length > 0) {
        sapQuery.orWhere((qb) => {
          qb.where('Status', sapStatuses[0])
          for (let i = 1; i < sapStatuses.length; i++) {
            qb.orWhere('Status', sapStatuses[i])
          }
          return qb
        })
      }
    } else if (filterStatusesWithoutIndaScan.length > 0) {
      sapQuery.where((qb) => {
        qb.where('Status', sapStatuses[0])
        for (let i = 1; i < sapStatuses.length; i++) {
          qb.orWhere('Status', sapStatuses[i])
        }
        return qb
      })
    }

    // Add filters based on selected customer and accessible waste producers
    const selectedCustomerId = this.authContext.getSelectedCustomerId()
    if (selectedCustomerId != null) {
      sapQuery.andWhere('Kunnr', selectedCustomerId)

      const restrictedWasteProducerIds = await this.userWasteProducerAuthService
        .getRestrictedWasteProducerIds(
          this.authContext.getAzureEntraUpn(),
          selectedCustomerId
        )
      if (restrictedWasteProducerIds !== undefined && restrictedWasteProducerIds.length > 0) {
        sapQuery.andWhere((qb) => {
          for (let i = 0; i < restrictedWasteProducerIds.length; i++) {
            if (i === 0) {
              qb.where('Kunnry2', restrictedWasteProducerIds[i])
            } else {
              qb.orWhere('Kunnry2', restrictedWasteProducerIds[i])
            }
          }
          return qb
        })
      }
    }

    // TODO https://linear.app/wisemen/issue/IND-322/api-retrieve-submitted-pick-up-requests-from-sap#comment-b5958772
    // .addOrderBy('Requesteddate', 'desc')

    if (query.filter.requestNumber !== undefined) {
      sapQuery.andWhere('Reqno', query.filter.requestNumber)
    }

    if (query.filter.wasteMaterial !== undefined) {
      sapQuery.andWhere('Arktx', query.filter.wasteMaterial, FilterOperator.SUBSTRING_OF)
    }

    if (query.filter.customerReference !== undefined) {
      sapQuery.andWhere('Yyklantmat', query.filter.customerReference, FilterOperator.SUBSTRING_OF)
    }

    if (query.filter.contractNumber !== undefined) {
      sapQuery.andWhere('Vbeln', query.filter.contractNumber)
    }

    if (query.filter.contractItem !== undefined) {
      sapQuery.andWhere('Posnr', query.filter.contractItem)
    }

    if (query.filter.transportMode !== undefined) {
      sapQuery.andWhere('Wastetype', mapTransportModeToSapValue(query.filter.transportMode))
    }

    if (query.filter.dateOfRequest !== undefined) {
      const from = dayjs(query.filter.dateOfRequest.from).startOf('day').toDate()
      const to = dayjs(query.filter.dateOfRequest.to).endOf('day').toDate()
      sapQuery.andWhere((qb) => {
        return qb.andWhere('Dateapplication', SapDateFormatterService.toSapFilterDate(from), FilterOperator.GREATER_THAN_OR_EQUAL)
          .andWhere('Dateapplication', SapDateFormatterService.toSapFilterDate(to), FilterOperator.LESS_THAN_OR_EQUAL)
      })
    }

    if (query.filter.customerId !== undefined) {
      sapQuery.andWhere('Kunnr', query.filter.customerId)
    }

    if (query.filter.wasteProducerId !== undefined) {
      sapQuery.andWhere('Kunnry2', query.filter.wasteProducerId)
    }

    if (query.filter.pickUpAddressId !== undefined) {
      sapQuery.andWhere('Kunnrwe', query.filter.pickUpAddressId)
    }

    if (query.filter.treatmentCenterName !== undefined) {
      sapQuery.andWhere('Lifnry0name', query.filter.treatmentCenterName, FilterOperator.SUBSTRING_OF)
    }

    if (query.filter.costCenter !== undefined) {
      sapQuery.andWhere('Costcenter', query.filter.costCenter, FilterOperator.SUBSTRING_OF)
    }

    if (query.filter.isTransportByIndaver !== undefined) {
      sapQuery.andWhere('Transportby', SapBooleanFormatterService.toSapBoolean(query.filter.isTransportByIndaver))
    }

    if (query.filter.requestDate !== undefined) {
      const from = dayjs(query.filter.requestDate.from).startOf('day').toDate()
      const to = dayjs(query.filter.requestDate.to).endOf('day').toDate()
      sapQuery.andWhere((qb) => {
        return qb
          .andWhere('ReqDateTo', SapDateFormatterService.toSapFilterDate(from), FilterOperator.GREATER_THAN_OR_EQUAL)
          .andWhere('Requesteddate', SapDateFormatterService.toSapFilterDate(to), FilterOperator.LESS_THAN_OR_EQUAL)
      })
    }

    if (query.filter.confirmedTransportDate !== undefined) {
      const from = dayjs(query.filter.confirmedTransportDate.from).startOf('day').toDate()
      const to = dayjs(query.filter.confirmedTransportDate.to).endOf('day').toDate()
      sapQuery.andWhere((qb) => {
        return qb.andWhere('Confirmeddate', SapDateFormatterService.toSapFilterDate(from), FilterOperator.GREATER_THAN_OR_EQUAL)
          .andWhere('Confirmeddate', SapDateFormatterService.toSapFilterDate(to), FilterOperator.LESS_THAN_OR_EQUAL)
      })
    }

    if (query.filter.salesOrder !== undefined) {
      sapQuery.andWhere('Salesdoc', query.filter.salesOrder)
    }

    if (query.filter.isHazardous !== undefined) {
      sapQuery.andWhere('Dangerous', SapBooleanFormatterService.toSapBoolean(query.filter.isHazardous))
    }

    if (query.filter.nameOfApplicant !== undefined) {
      sapQuery.andWhere('Nameapplicant', query.filter.nameOfApplicant, FilterOperator.SUBSTRING_OF)
    }

    if (query.filter.orderNumber !== undefined) {
      sapQuery.andWhere('Ordernumber', query.filter.orderNumber)
    }

    if (query.filter.materialAnalysis !== undefined) {
      sapQuery.andWhere('Materialanalysis', query.filter.materialAnalysis, FilterOperator.SUBSTRING_OF)
    }

    if (query.filter.deliveryInfo !== undefined) {
      sapQuery.andWhere('Deliveryinfo', query.filter.deliveryInfo, FilterOperator.SUBSTRING_OF)
    }

    if (query.filter.nameInstallation !== undefined) {
      sapQuery.andWhere('Lifnry3name', query.filter.nameInstallation, FilterOperator.SUBSTRING_OF)
    }

    if (query.filter.disposalCertificateNumber !== undefined) {
      sapQuery.andWhere('Yyesn', query.filter.disposalCertificateNumber)
    }

    if (query.filter.ewc !== undefined) {
      sapQuery.andWhere('Eural', query.filter.ewc)
    }

    if (query.filter.accountManager !== undefined) {
      sapQuery.andWhere('Accountmanager', query.filter.accountManager)
    }

    if (query.filter.containerNumber !== undefined) {
      sapQuery.andWhere('Cont1nr', query.filter.containerNumber)
    }

    return sapQuery
  }

  private mapSortKeyToSapKey (
    key: ViewPickUpRequestIndexSortKey
  ): keyof SapGetPickUpRequestIndexResponse {
    switch (key) {
      case ViewPickUpRequestIndexSortKey.REQUEST_NUMBER:
        return 'Reqno'
      case ViewPickUpRequestIndexSortKey.WASTE_MATERIAL:
        return 'Arktx'
      case ViewPickUpRequestIndexSortKey.CUSTOMER_REFERENCE:
        return 'Yyklantmat'
      case ViewPickUpRequestIndexSortKey.CONTRACT_NUMBER:
        return 'Vbeln'
      case ViewPickUpRequestIndexSortKey.CONTRACT_ITEM:
        return 'Posnr'
      case ViewPickUpRequestIndexSortKey.APPLICATION_DATE:
        return 'Dateapplication'
      case ViewPickUpRequestIndexSortKey.WASTE_PRODUCER_ID:
        return 'Kunnry2'
      case ViewPickUpRequestIndexSortKey.PICK_UP_ADDRESS_ID:
        return 'Kunnrwe'
      case ViewPickUpRequestIndexSortKey.TREATMENT_CENTER_NAME:
        return 'Lifnry0name'
      case ViewPickUpRequestIndexSortKey.REQUESTED_START_DATE:
        return 'ReqDateTo'
      case ViewPickUpRequestIndexSortKey.CONFIRMED_TRANSPORT_DATE:
        return 'Confirmeddate'
      case ViewPickUpRequestIndexSortKey.SALES_ORDER:
        return 'Salesdoc'
      case ViewPickUpRequestIndexSortKey.ORDER_NUMBER:
        return 'Ordernumber'
      case ViewPickUpRequestIndexSortKey.CONTAINER_NUMBER:
        return 'Cont1nr'
      case ViewPickUpRequestIndexSortKey.INSTALLATION_NAME:
        return 'Lifnry3name'
      case ViewPickUpRequestIndexSortKey.DISPOSAL_CERTIFICATE_NUMBER:
        return 'Yyesn'
      default:
        return exhaustiveCheck(key)
    }
  }
}
