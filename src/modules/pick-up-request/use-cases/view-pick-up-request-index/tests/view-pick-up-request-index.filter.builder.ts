import { PickUpRequestStatus } from '../../../enums/pick-up-request-status.enum.js'
import { ViewPickUpRequestIndexFilterQuery } from '../query/view-pick-up-request-index.filter-query.js'

export class ViewPickUpRequestIndexFilterQueryBuilder {
  private filter: ViewPickUpRequestIndexFilterQuery

  constructor () {
    this.reset()
  }

  reset (): this {
    this.filter = new ViewPickUpRequestIndexFilterQuery()

    this.filter.statuses = []

    return this
  }

  addStatus (status: PickUpRequestStatus): this {
    this.filter.statuses.push(status)

    return this
  }

  withCustomerId (customerId: string): this {
    this.filter.customerId = customerId

    return this
  }

  build (): ViewPickUpRequestIndexFilterQuery {
    const result = this.filter

    this.reset()

    return result
  }
}
