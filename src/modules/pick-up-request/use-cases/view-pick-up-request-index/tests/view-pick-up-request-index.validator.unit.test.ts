import { before, describe, it } from 'node:test'
import { expect } from 'expect'
import { createStubInstance, SinonStubbedInstance } from 'sinon'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { ViewPickUpRequestIndexValidator } from '../view-pick-up-request-index.validator.js'
import { PickUpRequestStatus } from '../../../enums/pick-up-request-status.enum.js'
import { InvalidPickUpRequestStatusCombinationError } from '../../../errors/invalid-pick-up-request-status-combination.error.js'
import { GeneralInfoValidator } from '../../../../auth/validators/general-info.validator.js'
import { ViewPickUpRequestIndexQueryBuilder } from './view-pick-up-request-index-query.builder.js'
import { ViewPickUpRequestIndexFilterQueryBuilder } from './view-pick-up-request-index.filter.builder.js'

describe('View pick-up request index validator unit test', () => {
  let validator: ViewPickUpRequestIndexValidator

  let generalInfoValidator: SinonStubbedInstance<GeneralInfoValidator>

  before(() => {
    TestBench.setupUnitTest()

    generalInfoValidator = createStubInstance(GeneralInfoValidator)
    generalInfoValidator.validate.resolves()

    validator = new ViewPickUpRequestIndexValidator(
      generalInfoValidator
    )
  })

  it('doesn\'t throw an error when validation passes', async () => {
    const query = new ViewPickUpRequestIndexQueryBuilder()
      .withFilter(
        new ViewPickUpRequestIndexFilterQueryBuilder()
          .addStatus(PickUpRequestStatus.DRAFT)
          .build()
      )
      .build()

    await expect(validator.validate(query)).resolves.not.toThrow()
  })

  it('throws an error when filtering on status draft alongside another status', async () => {
    const query = new ViewPickUpRequestIndexQueryBuilder()
      .withFilter(
        new ViewPickUpRequestIndexFilterQueryBuilder()
          .addStatus(PickUpRequestStatus.DRAFT)
          .addStatus(PickUpRequestStatus.PENDING)
          .build()
      )
      .build()

    await expect(validator.validate(query))
      .rejects.toThrow(InvalidPickUpRequestStatusCombinationError)
  })
})
