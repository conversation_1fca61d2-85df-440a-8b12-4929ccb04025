import { Injectable } from '@nestjs/common'
import { PickUpRequestStatus } from '../../enums/pick-up-request-status.enum.js'
import { InvalidPickUpRequestStatusCombinationError } from '../../errors/invalid-pick-up-request-status-combination.error.js'
import { GeneralInfoValidator } from '../../../auth/validators/general-info.validator.js'
import { ViewPickUpRequestIndexQuery } from './query/view-pick-up-request-index.query.js'

@Injectable()
export class ViewPickUpRequestIndexValidator {
  constructor (
    private readonly generalInfoValidator: GeneralInfoValidator
  ) {}

  async validate (query: ViewPickUpRequestIndexQuery): Promise<void> {
    if (query.filter.statuses != null) {
      const statuses = query.filter.statuses
      if (statuses.length > 1 && statuses.includes(PickUpRequestStatus.DRAFT)) {
        throw new InvalidPickUpRequestStatusCombinationError('error.pick-up-request.invalid_pick_up_request_status_combination')
      }
    }

    await this.generalInfoValidator.validate({
      customerId: query.filter?.customerId,
      wasteProducerId: query.filter?.wasteProducerId,
      pickUpAddressIds: query.filter?.pickUpAddressId
    })
  }
}
