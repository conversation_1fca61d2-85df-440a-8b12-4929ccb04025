import { ApiProperty } from '@nestjs/swagger'
import { PaginatedOffsetResponse } from '@wisemen/pagination'
import { PickUpRequestStatus } from '../../enums/pick-up-request-status.enum.js'
import { UniformPickUpRequest } from '../../types/uniform-pick-up-request.type.js'
import { TransportMode, TransportModeValues } from '../../enums/pick-up-transport-mode.enum.js'
import { PickUpRequestDynamicTableFields } from '../../types/pick-up-request.dynamic-table-fields.type.js'
import { calculatePageSize } from '../../../../utils/helpers/unknown-page-size.helper.js'

export class PickUpRequestResponse implements PickUpRequestDynamicTableFields {
  @ApiProperty({ type: String, nullable: true, description: 'UUID is null when retrieved from SAP' })
  uuid: string | null

  @ApiProperty({ type: String, format: 'date-time', nullable: true, description: 'Only for drafts' })
  createdAt: string | null

  @ApiProperty({ type: String, nullable: true })
  requestNumber: string | null

  @ApiProperty({ enum: PickUpRequestStatus, enumName: 'PickUpRequestStatus' })
  status: PickUpRequestStatus

  @ApiProperty({ type: String, isArray: true })
  wasteMaterial: string[]

  @ApiProperty({ type: String, nullable: true })
  customerId: string | null

  @ApiProperty({ type: String, nullable: true })
  customerName: string | null

  @ApiProperty({ type: String, nullable: true })
  wasteProducerId: string | null

  @ApiProperty({ type: String, nullable: true })
  wasteProducerName: string | null

  @ApiProperty({ type: String, isArray: true })
  pickUpAddressId: string[]

  @ApiProperty({ type: String, isArray: true })
  pickUpAddressName: string[]

  @ApiProperty({ type: String, nullable: true })
  customerReference: string | null

  @ApiProperty({ type: String, isArray: true })
  contractNumber: string[]

  @ApiProperty({ type: String, isArray: true })
  contractItem: string[]

  @ApiProperty({ type: String, enum: TransportModeValues, enumName: 'TransportMode', nullable: true })
  transportMode: TransportMode | null

  @ApiProperty({ type: String, nullable: true })
  dateOfRequest: string | null

  @ApiProperty({ type: String, nullable: true })
  treatmentCenterName: string | null

  @ApiProperty({ type: String, nullable: true })
  accountManager: string | null

  @ApiProperty({ type: String, isArray: true })
  costCenter: string[]

  @ApiProperty({ type: Boolean, nullable: true })
  isTransportByIndaver: boolean | null

  @ApiProperty({ type: String, nullable: true, format: 'date' })
  requestedStartDate: string | null

  @ApiProperty({ type: String, nullable: true, format: 'date' })
  requestedEndDate: string | null

  @ApiProperty({ type: String, nullable: true, format: 'date' })
  confirmedTransportDate: string | null

  @ApiProperty({ type: String, nullable: true })
  salesOrder: string | null

  @ApiProperty({ type: Boolean, isArray: true })
  isHazardous: boolean[]

  @ApiProperty({ type: String, nullable: true })
  nameOfApplicant: string | null

  @ApiProperty({ type: String, nullable: true })
  orderNumber: string | null

  @ApiProperty({ type: String, isArray: true })
  containerNumber: string[]

  @ApiProperty({ type: String, nullable: true })
  materialAnalysis: string | null

  @ApiProperty({ type: String, nullable: true })
  deliveryInfo: string | null

  @ApiProperty({ type: String, nullable: true })
  nameInstallation: string | null

  @ApiProperty({ type: String, nullable: true })
  disposalCertificateNumber: string | null

  @ApiProperty({ type: String, nullable: true })
  ewc: string | null

  @ApiProperty({ type: String, nullable: true })
  tfsNumber: string | null

  constructor (pickUpRequest: UniformPickUpRequest) {
    this.uuid = pickUpRequest.uuid
    this.createdAt = pickUpRequest.createdAt?.toISOString() ?? null
    this.requestNumber = pickUpRequest.requestNumber
    this.status = pickUpRequest.status
    this.wasteMaterial = pickUpRequest.wasteMaterial
    this.customerId = pickUpRequest.customerId
    this.customerName = pickUpRequest.customerName
    this.wasteProducerId = pickUpRequest.wasteProducerId
    this.wasteProducerName = pickUpRequest.wasteProducerName
    this.pickUpAddressId = pickUpRequest.pickUpAddressId
    this.pickUpAddressName = pickUpRequest.pickUpAddressName
    this.customerReference = pickUpRequest.customerReference
    this.contractNumber = pickUpRequest.contractNumber
    this.contractItem = pickUpRequest.contractItem
    this.transportMode = pickUpRequest.transportMode
    this.dateOfRequest = pickUpRequest.dateOfRequest
    this.treatmentCenterName = pickUpRequest.treatmentCenterName
    this.accountManager = pickUpRequest.accountManager
    this.costCenter = pickUpRequest.costCenter
    this.isTransportByIndaver = pickUpRequest.isTransportByIndaver
    this.requestedStartDate = pickUpRequest.requestedStartDate
    this.requestedEndDate = pickUpRequest.requestedEndDate
    this.confirmedTransportDate = pickUpRequest.confirmedTransportDate
    this.salesOrder = pickUpRequest.salesOrder
    this.isHazardous = pickUpRequest.isHazardous
    this.nameOfApplicant = pickUpRequest.nameOfApplicant
    this.orderNumber = pickUpRequest.orderNumber
    this.containerNumber = pickUpRequest.containerNumber
    this.materialAnalysis = pickUpRequest.materialAnalysis
    this.deliveryInfo = pickUpRequest.deliveryInfo
    this.nameInstallation = pickUpRequest.nameInstallation
    this.disposalCertificateNumber = pickUpRequest.disposalCertificateNumber
    this.ewc = pickUpRequest.ewc
    this.tfsNumber = pickUpRequest.tfsNumber
  }
}

export class ViewPickUpRequestIndexResponse extends PaginatedOffsetResponse<PickUpRequestResponse> {
  @ApiProperty({ type: PickUpRequestResponse, isArray: true })
  declare items: PickUpRequestResponse[]

  constructor (items: UniformPickUpRequest[], total: number | null, limit: number, offset: number) {
    const result = items.map(contact => new PickUpRequestResponse(contact))

    if (total === null) {
      total = calculatePageSize(items, offset, limit)
    }

    super(result, total, limit, offset)
  }
}
