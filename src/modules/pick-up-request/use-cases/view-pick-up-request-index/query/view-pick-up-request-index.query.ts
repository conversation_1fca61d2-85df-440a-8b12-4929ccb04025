import { ApiProperty } from '@nestjs/swagger'
import { PaginatedOffsetSearchQuery } from '@wisemen/pagination'
import { Type } from 'class-transformer'
import { ArrayMinSize, ArrayUnique, Equals, IsArray, IsNotEmpty, ValidateNested } from 'class-validator'
import { IsUndefinable } from '@wisemen/validators'
import { ViewPickUpRequestIndexFilterQuery } from './view-pick-up-request-index.filter-query.js'
import { ViewPickUpRequestIndexSortQuery } from './view-pick-up-request-index-sort.query.js'

export class ViewPickUpRequestIndexQuery extends PaginatedOffsetSearchQuery {
  @ApiProperty({ type: ViewPickUpRequestIndexSortQuery, isArray: true, required: false })
  @IsUndefinable()
  @IsArray()
  @ArrayUnique()
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  @Type(() => ViewPickUpRequestIndexSortQuery)
  sort?: ViewPickUpRequestIndexSortQuery[]

  @ApiProperty({ type: ViewPickUpRequestIndexFilterQuery })
  @Type(() => ViewPickUpRequestIndexFilterQuery)
  @ValidateNested()
  @IsNotEmpty()
  filter: ViewPickUpRequestIndexFilterQuery

  @Equals(undefined)
  search?: string
}
