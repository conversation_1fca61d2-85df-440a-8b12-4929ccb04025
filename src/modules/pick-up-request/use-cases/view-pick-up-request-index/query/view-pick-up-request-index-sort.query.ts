import { ApiProperty } from '@nestjs/swagger'
import { SortDirection, SortDirectionApiProperty, SortQuery } from '@wisemen/pagination'
import { IsEnum } from 'class-validator'

export enum ViewPickUpRequestIndexSortKey {
  REQUEST_NUMBER = 'requestNumber',
  WASTE_MATERIAL = 'wasteMaterial',
  CUSTOMER_REFERENCE = 'customerReference',
  CONTRACT_NUMBER = 'contractNumber',
  CONTRACT_ITEM = 'contractItem',
  APPLICATION_DATE = 'applicationDate',
  WASTE_PRODUCER_ID = 'wasteProducerId',
  PICK_UP_ADDRESS_ID = 'pickUpAddressId',
  TREATMENT_CENTER_NAME = 'treatmentCenterName',
  REQUESTED_START_DATE = 'requestedStartDate',
  CONFIRMED_TRANSPORT_DATE = 'confirmedTransportDate',
  SALES_ORDER = 'salesOrder',
  ORDER_NUMBER = 'orderNumber',
  CONTAINER_NUMBER = 'containerNumber',
  INSTALLATION_NAME = 'installationName',
  DISPOSAL_CERTIFICATE_NUMBER = 'disposalCertificateNumber'
}

export class ViewPickUpRequestIndexSortQuery extends SortQuery {
  @ApiProperty({ enumName: 'ViewPickUpRequestIndexSortKey', enum: ViewPickUpRequestIndexSortKey })
  @IsEnum(ViewPickUpRequestIndexSortKey)
  key: ViewPickUpRequestIndexSortKey

  @SortDirectionApiProperty()
  @IsEnum(SortDirection)
  order: SortDirection
}
