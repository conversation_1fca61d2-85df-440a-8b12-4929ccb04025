import { ApiProperty } from '@nestjs/swagger'
import { FilterQuery } from '@wisemen/pagination'
import { IsNotEmpty, IsArray, ArrayMinSize, IsEnum, IsString, IsBooleanString, ValidateNested, IsObject } from 'class-validator'
import { IsUndefinable } from '@wisemen/validators'
import { Type } from 'class-transformer'
import { PickUpRequestStatus } from '../../../enums/pick-up-request-status.enum.js'
import { PickUpTransportMode } from '../../../enums/pick-up-transport-mode.enum.js'
import { DateRange } from '../../../../../utils/types/date-filter-range.js'

export class ViewPickUpRequestIndexFilterQuery extends FilterQuery {
  @ApiProperty({ type: String, enum: PickUpRequestStatus, enumName: 'PickUpRequestStatus', isArray: true })
  @IsNotEmpty()
  @IsArray()
  @ArrayMinSize(1)
  @IsEnum(PickUpRequestStatus, { each: true })
  statuses: PickUpRequestStatus[]

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsString()
  @IsNotEmpty()
  requestNumber?: string

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsString()
  @IsNotEmpty()
  wasteMaterial?: string

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsString()
  @IsNotEmpty()
  customerReference?: string

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsString()
  @IsNotEmpty()
  contractNumber?: string

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsString()
  @IsNotEmpty()
  contractItem?: string

  @ApiProperty({ type: String, enumName: 'PickUpTransportMode', enum: PickUpTransportMode, required: false })
  @IsUndefinable()
  @IsEnum(PickUpTransportMode)
  transportMode?: PickUpTransportMode

  @ApiProperty({ type: DateRange, required: false })
  @IsUndefinable()
  @Type(() => DateRange)
  @ValidateNested()
  @IsObject()
  dateOfRequest?: DateRange

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsString()
  @IsNotEmpty()
  customerId?: string

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsString()
  @IsNotEmpty()
  wasteProducerId?: string

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsString()
  @IsNotEmpty()
  pickUpAddressId?: string

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsString()
  @IsNotEmpty()
  treatmentCenterName?: string

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsString()
  @IsNotEmpty()
  costCenter?: string

  @ApiProperty({ type: Boolean, required: false })
  @IsUndefinable()
  @IsBooleanString()
  isTransportByIndaver?: string

  @ApiProperty({ type: DateRange, required: false })
  @IsUndefinable()
  @Type(() => DateRange)
  @ValidateNested()
  @IsObject()
  requestDate?: DateRange

  @ApiProperty({ type: DateRange, required: false })
  @IsUndefinable()
  @Type(() => DateRange)
  @ValidateNested()
  @IsObject()
  confirmedTransportDate?: DateRange

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsString()
  @IsNotEmpty()
  salesOrder?: string

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsBooleanString()
  @IsNotEmpty()
  isHazardous?: string

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsString()
  @IsNotEmpty()
  nameOfApplicant?: string

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsString()
  @IsNotEmpty()
  orderNumber?: string

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsString()
  @IsNotEmpty()
  materialAnalysis?: string

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsString()
  @IsNotEmpty()
  deliveryInfo?: string

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsString()
  @IsNotEmpty()
  nameInstallation?: string

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsString()
  @IsNotEmpty()
  disposalCertificateNumber?: string

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsString()
  @IsNotEmpty()
  ewc?: string

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsString()
  @IsNotEmpty()
  accountManager?: string

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsString()
  @IsNotEmpty()
  containerNumber: string
}
