import { PickUpRequestMaterial } from '../../../types/pick-up-request-material.type.js'
import { PickUpRequestMaterialCommand } from '../commands/pick-up-request-material.command.js'

export class UpdatePickUpRequestMaterialMapper {
  static mapToMaterial (
    command: PickUpRequestMaterialCommand
  ): PickUpRequestMaterial {
    return {
      contractLineId: command.contractLineId,
      contractNumber: command.contractNumber,
      contractItem: command.contractItem,
      tcNumber: command.tcNumber,
      processCode: command.processCode,
      pickUpAddressId: command.pickUpAddressId,
      materialNumber: command.materialNumber,
      customerReference: command.customerReference,
      isHazardous: command.isHazardous,
      wasteMaterial: command.wasteMaterial,
      ewcCode: command.ewcCode,
      asn: command.asn,
      tfs: command.tfs,
      unNumberHazardous: command.unNumberHazardous,
      estimatedWeightOrVolumeValue: command.estimatedWeightOrVolumeValue,
      estimatedWeightOrVolumeUnit: command.estimatedWeightOrVolumeUnit,
      costCenter: command.costCenter,
      poNumber: command.poNumber,
      unNumber: command.unNumber,
      unNumberDescription: command.unNumberDescription,
      adrClass: command.adrClass,
      packingGroup: command.packingGroup,
      dangerLabel1: command.dangerLabel1,
      dangerLabel2: command.dangerLabel2,
      dangerLabel3: command.dangerLabel3,
      packagingType: command.packagingType,
      quantityPackages: command.quantityPackages,
      quantityLabels: command.quantityLabels,
      quantityPallets: command.quantityPallets,
      containerType: command.containerType,
      containerVolumeSize: command.containerVolumeSize,
      containerNumber: command.containerNumber,
      containerTransportType: command.containerTransportType,
      tankerType: command.tankerType,
      isContainerCovered: command.isContainerCovered,
      reconciliationNumber: command.reconciliationNumber,
      hazardInducers: command.hazardInducers,
      quantityContainers: command.quantityContainers,
      tfsNumber: command.tfsNumber,
      serialNumber: command.serialNumber
    }
  }
}
