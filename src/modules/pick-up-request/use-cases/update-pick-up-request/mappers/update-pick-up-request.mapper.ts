import { PickUpRequest } from '../../../entities/pick-up-request.entity.js'
import { UpdatePickUpRequestCommand } from '../update-pick-up-request.command.js'

export class UpdatePickUpRequestMapper {
  static mapToPickUpRequest (command: UpdatePickUpRequestCommand): Partial<PickUpRequest> {
    return {
      customerId: command.customerId,
      wasteProducerId: command.wasteProducerId,
      pickUpAddressIds: command.pickUpAddressIds,
      transportMode: command.transportMode,
      isTransportByIndaver: command.isTransportByIndaver,
      totalQuantityPallets: command.totalQuantityPallets,
      isReturnPackaging: command.isReturnPackaging,
      packagingRemark: command.packagingRemark,
      remarks: command.remarks,
      startDate: command.startDate,
      startTime: command.startTime,
      endDate: command.endDate,
      sendCopyToContacts: command.sendCopyToContacts,
      materials: command.materials,
      packagingRequestMaterials: command.packagingRequestMaterials,
      isWicConfirmed: command.isWicConfirmed
    }
  }

  static mapToMergedPickUpRequest (
    command: UpdatePickUpRequestCommand,
    pickUpRequest: PickUpRequest
  ): PickUpRequest {
    const mergedPickUpRequest = new PickUpRequest()

    Object.assign(mergedPickUpRequest, pickUpRequest, {
      customerId: command.customerId !== undefined
        ? command.customerId
        : pickUpRequest.customerId,
      wasteProducerId: command.wasteProducerId !== undefined
        ? command.wasteProducerId
        : pickUpRequest.wasteProducerId,
      pickUpAddressIds: command.pickUpAddressIds !== undefined
        ? command.pickUpAddressIds
        : pickUpRequest.pickUpAddressIds,
      transportMode: command.transportMode !== undefined
        ? command.transportMode
        : pickUpRequest.transportMode,
      totalQuantityPallets: command.totalQuantityPallets !== undefined
        ? command.totalQuantityPallets
        : pickUpRequest.totalQuantityPallets,
      isReturnPackaging: command.isReturnPackaging !== undefined
        ? command.isReturnPackaging
        : pickUpRequest.isReturnPackaging,
      packagingRemark: command.packagingRemark !== undefined
        ? command.packagingRemark
        : pickUpRequest.packagingRemark,
      remarks: command.remarks !== undefined
        ? command.remarks
        : pickUpRequest.remarks,
      startDate: command.startDate !== undefined
        ? command.startDate
        : pickUpRequest.startDate,
      endDate: command.endDate !== undefined
        ? command.endDate
        : pickUpRequest.endDate,
      startTime: command.startTime !== undefined
        ? command.startTime
        : pickUpRequest.startTime,
      sendCopyToContacts: command.sendCopyToContacts !== undefined
        ? command.sendCopyToContacts
        : pickUpRequest.sendCopyToContacts,
      weeklyPlanningRequestUuid: pickUpRequest.weeklyPlanningRequestUuid,
      materials: command.materials !== undefined
        ? command.materials
        : pickUpRequest.materials,
      packagingRequestMaterial: command.packagingRequestMaterials !== undefined
        ? command.packagingRequestMaterials
        : pickUpRequest.packagingRequestMaterials,
      isWicConfirmed: command.isWicConfirmed ?? null
    })

    return mergedPickUpRequest
  }
}
