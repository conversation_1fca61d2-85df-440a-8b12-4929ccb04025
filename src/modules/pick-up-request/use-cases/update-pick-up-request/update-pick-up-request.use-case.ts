import { Injectable } from '@nestjs/common'
import { DataSource, Repository } from 'typeorm'
import { InjectRepository, transaction } from '@wisemen/nestjs-typeorm'
import { PickUpRequest } from '../../entities/pick-up-request.entity.js'
import { FileLinkService } from '../../../files/services/file-link.service.js'
import { EntityPart } from '../../../files/enums/entity-part.enum.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { DomainEventEmitter } from '../../../domain-events/domain-event-emitter.js'
import { PickUpRequestNotFoundError } from '../../errors/pick-up-request-sap-not-found.error.js'
import { UpdatePickUpRequestValidator } from '../../validators/update-pick-up-request.validator.js'
import { PickUpRequestUpdatedEvent } from './pick-up-request-updated.event.js'
import { UpdatePickUpRequestCommand } from './update-pick-up-request.command.js'
import { UpdatePickUpRequestResponse } from './update-pick-up-request.response.js'
import { UpdatePickUpRequestMapper } from './mappers/update-pick-up-request.mapper.js'

@Injectable()
export class UpdatePickUpRequestUseCase {
  constructor (
    private readonly dataSource: DataSource,
    private readonly authContext: AuthContext,
    private readonly validator: UpdatePickUpRequestValidator,
    @InjectRepository(PickUpRequest)
    private readonly pickUpRequestRepository: Repository<PickUpRequest>,
    private readonly fileLinkService: FileLinkService,
    private readonly eventEmitter: DomainEventEmitter
  ) {}

  async execute (
    pickUpRequestUuid: string,
    command: UpdatePickUpRequestCommand
  ): Promise<UpdatePickUpRequestResponse> {
    const authUserUuid = this.authContext.getUserUuidOrFail()

    const pickUpRequest = await this.loadPickUpRequest(pickUpRequestUuid, authUserUuid)

    await this.validator.validate(pickUpRequest, command)

    const updatePickUpRequest = UpdatePickUpRequestMapper.mapToPickUpRequest(command)

    await transaction(this.dataSource, async () => {
      const updateResult = await this.pickUpRequestRepository.update({
        uuid: pickUpRequestUuid,
        createdByUserUuid: authUserUuid
      }, updatePickUpRequest)

      if (updateResult.affected === 0) {
        throw new PickUpRequestNotFoundError({ uuid: pickUpRequestUuid })
      }

      if (command.additionalFiles !== undefined) {
        await this.fileLinkService.sync(
          command.additionalFiles,
          pickUpRequestUuid,
          PickUpRequest.name,
          EntityPart.ADDITIONAL
        )
      }

      await this.eventEmitter.emit([new PickUpRequestUpdatedEvent(pickUpRequestUuid)])
    })

    const updatedPickUpRequest = await this.pickUpRequestRepository.findOneOrFail({
      select: {
        uuid: true,
        createdAt: true,
        updatedAt: true,
        isWicConfirmed: true,
        materials: true
      },
      where: {
        uuid: pickUpRequestUuid
      }
    })

    return new UpdatePickUpRequestResponse(updatedPickUpRequest)
  }

  private loadPickUpRequest (
    pickUpRequestUuid: string,
    authUserUuid: string
  ): Promise<PickUpRequest> {
    return this.pickUpRequestRepository.findOneOrFail({
      where: {
        uuid: pickUpRequestUuid,
        createdByUserUuid: authUserUuid
      }
    })
  }
}
