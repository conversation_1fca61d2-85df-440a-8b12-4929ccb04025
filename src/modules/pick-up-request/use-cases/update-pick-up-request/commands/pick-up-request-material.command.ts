import { ApiProperty } from '@nestjs/swagger'
import { IsNullable } from '@wisemen/validators'
import { IsBoolean, IsEnum, IsInt, IsNotEmpty, IsN<PERSON>ber, IsO<PERSON>al, IsString, <PERSON>, <PERSON>, <PERSON> } from 'class-validator'
import { WasteMeasurementUnit } from '../../../../waste-inquiry/enums/waste-measurement-unit.enum.js'
import { PickUpRequestMaterial } from '../../../types/pick-up-request-material.type.js'
import { PackingGroup } from '../../../../waste-inquiry/enums/packaging-group.enum.js'

export class PickUpRequestMaterialCommand implements PickUpRequestMaterial {
  @ApiProperty({ type: String })
  @IsString()
  @IsNotEmpty()
  contractLineId: string

  @ApiProperty({ type: String })
  @IsString()
  @IsNotEmpty()
  contractNumber: string

  @ApiProperty({ type: String })
  @IsString()
  @IsNotEmpty()
  contractItem: string

  @ApiProperty({ type: String, nullable: true, required: false })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  processCode?: string | null

  @ApiProperty({ type: String, required: false, nullable: true })
  @IsOptional()
  @IsNullable()
  @IsString()
  tcNumber?: string | null

  @ApiProperty({ type: String, required: false, nullable: true })
  @IsOptional()
  @IsNullable()
  @IsString()
  pickUpAddressId?: string | null

  @ApiProperty({ type: String, required: false, nullable: true })
  @IsOptional()
  @IsNullable()
  @IsString()
  materialNumber?: string | null

  @ApiProperty({ type: String, required: false, nullable: true })
  @IsOptional()
  @IsNullable()
  @IsString()
  customerReference?: string | null

  @ApiProperty({ type: Boolean, required: false, nullable: true })
  @IsOptional()
  @IsNullable()
  @IsBoolean()
  isHazardous?: boolean | null

  @ApiProperty({ type: String, required: false, nullable: true })
  @IsOptional()
  @IsNullable()
  @IsString()
  wasteMaterial?: string | null

  @ApiProperty({ type: String, required: false, nullable: true })
  @IsOptional()
  @IsNullable()
  @IsString()
  ewcCode?: string | null

  @ApiProperty({ type: String, required: false, nullable: true })
  @IsOptional()
  @IsNullable()
  @IsString()
  asn?: string | null

  @ApiProperty({ type: Boolean, required: false, nullable: true })
  @IsOptional()
  @IsBoolean()
  @IsNullable()
  tfs?: boolean | null

  @ApiProperty({ type: Boolean, required: false, nullable: true })
  @IsOptional()
  @IsBoolean()
  @IsNullable()
  unNumberHazardous?: boolean | null

  @ApiProperty({
    type: Number,
    required: false,
    nullable: true,
    minimum: 0.01,
    maximum: 99999999999.99,
    multipleOf: 0.01
  })
  @IsOptional()
  @IsNullable()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0.01)
  @Max(99999999999.99)
  estimatedWeightOrVolumeValue?: number | null

  @ApiProperty({ type: String, enum: WasteMeasurementUnit, enumName: 'WasteMeasurementUnit', required: false, nullable: true })
  @IsOptional()
  @IsNullable()
  @IsNotEmpty()
  @IsEnum(WasteMeasurementUnit)
  estimatedWeightOrVolumeUnit?: WasteMeasurementUnit | null

  @ApiProperty({ type: String, required: false, nullable: true, maxLength: 25 })
  @IsNullable()
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  @MaxLength(25)
  costCenter?: string | null

  @ApiProperty({ type: String, required: false, nullable: true, maxLength: 25 })
  @IsNullable()
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  @MaxLength(25)
  poNumber?: string | null

  @ApiProperty({ type: String, required: false, nullable: true })
  @IsNullable()
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  unNumber?: string | null

  @ApiProperty({ type: String, required: false, nullable: true })
  @IsNullable()
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  unNumberDescription?: string | null

  @ApiProperty({ type: String, required: false, nullable: true })
  @IsNullable()
  @IsOptional()
  @IsString()
  adrClass?: string | null

  @ApiProperty({ type: String, enum: PackingGroup, enumName: 'PackingGroup', required: false, nullable: true })
  @IsNullable()
  @IsOptional()
  @IsEnum(PackingGroup)
  packingGroup?: PackingGroup | null

  @ApiProperty({ type: String, required: false, nullable: true })
  @IsNullable()
  @IsOptional()
  @IsString()
  dangerLabel1?: string | null

  @ApiProperty({ type: String, required: false, nullable: true })
  @IsNullable()
  @IsOptional()
  @IsString()
  dangerLabel2?: string | null

  @ApiProperty({ type: String, required: false, nullable: true })
  @IsNullable()
  @IsOptional()
  @IsString()
  dangerLabel3?: string | null

  @ApiProperty({ type: String, required: false, nullable: true })
  @IsNullable()
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  packagingType?: string | null

  @ApiProperty({ type: Number, required: false, nullable: true, minimum: 0, maximum: 999 })
  @IsOptional()
  @IsNullable()
  @IsInt()
  @Min(0)
  @Max(999)
  quantityPackages?: number | null

  @ApiProperty({ type: Number, required: false, nullable: true, minimum: 0, maximum: 999 })
  @IsOptional()
  @IsNullable()
  @IsInt()
  @Min(0)
  @Max(999)
  quantityLabels?: number | null

  @ApiProperty({ type: Number, required: false, nullable: true, minimum: 0, maximum: 999 })
  @IsOptional()
  @IsNullable()
  @IsInt()
  @Min(0)
  @Max(999)
  quantityPallets?: number | null

  @ApiProperty({ type: String, required: false, nullable: true })
  @IsNullable()
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  containerType?: string | null

  @ApiProperty({
    type: String,
    required: false,
    nullable: true,
    maxLength: 13
  })
  @IsOptional()
  @IsNullable()
  @IsString()
  @MaxLength(13)
  containerVolumeSize?: string | null

  @ApiProperty({ type: String, required: false, nullable: true, maxLength: 40 })
  @IsNullable()
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  @MaxLength(40)
  containerNumber?: string | null

  @ApiProperty({ type: String, required: false, nullable: true, description: 'Transport type id' })
  @IsOptional()
  @IsNullable()
  @IsString()
  @IsNotEmpty()
  @MaxLength(2)
  containerTransportType?: string | null

  @ApiProperty({ type: String, required: false, nullable: true, description: 'Tanker type id' })
  @IsOptional()
  @IsNullable()
  @IsString()
  @IsNotEmpty()
  @MaxLength(2)
  tankerType?: string | null

  @ApiProperty({ type: Boolean, required: false, nullable: true })
  @IsOptional()
  @IsBoolean()
  @IsNullable()
  isContainerCovered?: boolean | null

  @ApiProperty({ type: String, required: false, nullable: true, maxLength: 6 })
  @IsOptional()
  @IsNullable()
  @IsString()
  @IsNotEmpty()
  @MaxLength(6)
  reconciliationNumber?: string | null

  @ApiProperty({ type: String, required: false, nullable: true, maxLength: 100 })
  @IsOptional()
  @IsNullable()
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  hazardInducers?: string | null

  @ApiProperty({ type: Number, required: false, nullable: true, minimum: 0, maximum: 9 })
  @IsOptional()
  @IsNullable()
  @IsInt()
  @Min(0)
  @Max(9)
  quantityContainers?: number | null

  @ApiProperty({ type: String, required: false, nullable: true, maxLength: 15 })
  @IsOptional()
  @IsNullable()
  @IsString()
  @IsNotEmpty()
  @MaxLength(15)
  tfsNumber?: string | null

  @ApiProperty({ type: String, required: false, nullable: true, maxLength: 4 })
  @IsOptional()
  @IsNullable()
  @IsString()
  @IsNotEmpty()
  @MaxLength(4)
  serialNumber?: string | null
}
