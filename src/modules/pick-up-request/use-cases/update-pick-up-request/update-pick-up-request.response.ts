import { ApiProperty } from '@nestjs/swagger'
import { PickUpRequest } from '../../entities/pick-up-request.entity.js'

export class UpdatePickUpRequestResponse {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({ type: String, format: 'date-time' })
  createdAt: string

  @ApiProperty({ type: String, format: 'date-time' })
  updatedAt: string

  @ApiProperty({ type: Boolean, nullable: true })
  isWicConfirmed: boolean | null

  @ApiProperty({ type: Boolean })
  needsWicConfirmation: boolean

  constructor (pickUpRequest: PickUpRequest) {
    this.uuid = pickUpRequest.uuid
    this.createdAt = pickUpRequest.createdAt.toISOString()
    this.updatedAt = pickUpRequest.updatedAt.toISOString()
    this.isWicConfirmed = pickUpRequest.isWicConfirmed
    this.needsWicConfirmation = pickUpRequest.needsWicConfirmation
  }
}
