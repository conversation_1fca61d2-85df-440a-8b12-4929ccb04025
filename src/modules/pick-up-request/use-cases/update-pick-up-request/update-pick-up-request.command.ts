import { ApiProperty } from '@nestjs/swagger'
import { <PERSON><PERSON><PERSON>y, IsBoolean, IsEnum, IsInt, IsNotEmpty, IsO<PERSON>al, IsString, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, ValidateNested } from 'class-validator'
import { IsAfterTodayString, IsDateWithoutTimeString, IsNullable, IsTimeString } from '@wisemen/validators'
import { Type } from 'class-transformer'
import { PickUpTransportMode } from '../../enums/pick-up-transport-mode.enum.js'
import { CreateFileLinkCommand } from '../../../files/commands/create-file-link.command.js'
import { Contact } from '../../../contact/types/contact.type.js'
import { PackagingRequestMaterialCommand } from '../../../packaging-request/use-cases/update-packaging-request/commands/packaging-request-material.command.js'
import { PickUpRequestMaterialCommand } from './commands/pick-up-request-material.command.js'

export class UpdatePickUpRequestCommand {
  @ApiProperty({ type: String, required: false, nullable: true })
  @IsOptional()
  @IsNullable()
  @IsString()
  @IsNotEmpty()
  customerId?: string | null

  @ApiProperty({ type: String, required: false, nullable: true })
  @IsOptional()
  @IsNullable()
  @IsString()
  @IsNotEmpty()
  customerName?: string | null

  @ApiProperty({ type: String, required: false, nullable: true })
  @IsOptional()
  @IsNullable()
  @IsString()
  @IsNotEmpty()
  wasteProducerId?: string | null

  @ApiProperty({ type: String, required: false, nullable: true })
  @IsOptional()
  @IsNullable()
  @IsString()
  @IsNotEmpty()
  wasteProducerName?: string | null

  @ApiProperty({ type: String, required: false, isArray: true })
  @IsOptional()
  @IsString({ each: true })
  @IsArray()
  pickUpAddressIds?: string[]

  @ApiProperty({ type: String, required: false, isArray: true })
  @IsOptional()
  @IsString({ each: true })
  @IsArray()
  pickUpAddressNames?: string[]

  @ApiProperty({ type: String, enum: PickUpTransportMode, enumName: 'PickUpTransportMode', required: false, nullable: true })
  @IsOptional()
  @IsNullable()
  @IsNotEmpty()
  @IsEnum(PickUpTransportMode)
  transportMode?: PickUpTransportMode | null

  @ApiProperty({ type: Boolean, required: false, nullable: true })
  @IsOptional()
  @IsNullable()
  @IsBoolean()
  isTransportByIndaver?: boolean | null

  @ApiProperty({ type: Number, required: false, nullable: true, minimum: 0, maximum: 999 })
  @IsOptional()
  @IsNullable()
  @IsInt()
  @Min(0)
  @Max(999)
  totalQuantityPallets?: number | null

  @ApiProperty({ type: Boolean, required: false, nullable: true })
  @IsOptional()
  @IsNullable()
  @IsBoolean()
  isReturnPackaging?: boolean | null

  @ApiProperty({ type: String, required: false, nullable: true, format: 'date' })
  @IsOptional()
  @IsNullable()
  @IsDateWithoutTimeString()
  @IsNotEmpty()
  @IsAfterTodayString()
  startDate?: string | null

  @ApiProperty({ type: String, required: false, nullable: true, format: 'date' })
  @IsOptional()
  @IsNullable()
  @IsDateWithoutTimeString()
  @IsNotEmpty()
  @IsAfterTodayString()
  endDate?: string | null

  @ApiProperty({ type: String, format: 'time', required: false, nullable: true })
  @IsOptional()
  @IsNullable()
  @IsNotEmpty()
  @IsTimeString()
  startTime?: string | null

  @ApiProperty({ type: String, required: false, nullable: true, maxLength: 200 })
  @IsOptional()
  @IsNullable()
  @IsString()
  @IsNotEmpty()
  @MaxLength(200)
  packagingRemark?: string | null

  @ApiProperty({ type: String, required: false, nullable: true })
  @IsOptional()
  @IsNullable()
  @IsString()
  @IsNotEmpty()
  remarks?: string | null

  @ApiProperty({ type: CreateFileLinkCommand, required: false, isArray: true })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateFileLinkCommand)
  @IsArray()
  additionalFiles?: CreateFileLinkCommand[]

  @ApiProperty({ type: Contact, required: false, isArray: true })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => Contact)
  @IsArray()
  sendCopyToContacts?: Contact[]

  @ApiProperty({ type: PickUpRequestMaterialCommand, required: false, isArray: true })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => PickUpRequestMaterialCommand)
  @IsArray()
  materials?: PickUpRequestMaterialCommand[]

  @ApiProperty({ type: PackagingRequestMaterialCommand, required: false, isArray: true })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => PackagingRequestMaterialCommand)
  @IsArray()
  packagingRequestMaterials?: PackagingRequestMaterialCommand[]

  @ApiProperty({ type: Boolean, required: false, nullable: true })
  @IsOptional()
  @IsBoolean()
  isWicConfirmed?: boolean | null
}
