import { Contact } from '../../../../contact/types/contact.type.js'
import { CreateFileLinkCommand } from '../../../../files/commands/create-file-link.command.js'
import { PackagingRequestMaterialCommand } from '../../../../packaging-request/use-cases/update-packaging-request/commands/packaging-request-material.command.js'
import { PickUpRequestMaterialCommand } from '../commands/pick-up-request-material.command.js'
import { UpdatePickUpRequestCommand } from '../update-pick-up-request.command.js'

export class UpdatePickUpRequestCommandBuilder {
  private command: UpdatePickUpRequestCommand

  constructor () {
    this.reset()
  }

  reset (): this {
    this.command = new UpdatePickUpRequestCommand()

    return this
  }

  withCustomerId (customerId: string): this {
    this.command.customerId = customerId

    return this
  }

  withWasteProducerId (wasteProducerId: string): this {
    this.command.wasteProducerId = wasteProducerId

    return this
  }

  withPickUpAddressIds (pickUpAddressIds: string[]): this {
    this.command.pickUpAddressIds = pickUpAddressIds

    return this
  }

  addMaterial (material: PickUpRequestMaterialCommand): this {
    if (this.command.materials === undefined) {
      this.command.materials = []
    }

    this.command.materials.push(material)

    return this
  }

  addPackaging (packaging: PackagingRequestMaterialCommand): this {
    if (this.command.packagingRequestMaterials === undefined) {
      this.command.packagingRequestMaterials = []
    }

    this.command.packagingRequestMaterials.push(packaging)

    return this
  }

  withTotalQuantityPallets (totalQuantityPallets: number): this {
    this.command.totalQuantityPallets = totalQuantityPallets

    return this
  }

  addAdditionalFile (file: CreateFileLinkCommand): this {
    if (this.command.additionalFiles === undefined) {
      this.command.additionalFiles = []
    }

    this.command.additionalFiles.push(file)

    return this
  }

  withIsReturnPackaging (isReturnPackaging: boolean): this {
    this.command.isReturnPackaging = isReturnPackaging

    return this
  }

  withStartDate (date: string): this {
    this.command.startDate = date

    return this
  }

  withPackagingRemark (packagingRemark: string): this {
    this.command.packagingRemark = packagingRemark

    return this
  }

  withRemark (remarks: string): this {
    this.command.remarks = remarks

    return this
  }

  addSendCopyToContacts (contact: Contact): this {
    if (this.command.sendCopyToContacts === undefined) {
      this.command.sendCopyToContacts = []
    }

    this.command.sendCopyToContacts.push(contact)

    return this
  }

  withStartTime (startTime: string): this {
    this.command.startTime = startTime

    return this
  }

  withEndDate (date: string): this {
    this.command.endDate = date

    return this
  }

  withMaterials (materials: PickUpRequestMaterialCommand[]): this {
    this.command.materials = materials
    return this
  }

  withIsWicConfirmed (isConfirmed: boolean | null): this {
    this.command.isWicConfirmed = isConfirmed
    return this
  }

  build (): UpdatePickUpRequestCommand {
    const result = this.command

    this.reset()

    return result
  }
}
