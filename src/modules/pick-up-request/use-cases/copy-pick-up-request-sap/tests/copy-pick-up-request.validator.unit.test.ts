import { afterEach, before, describe, it } from 'node:test'
import { randomUUID } from 'node:crypto'
import { expect } from 'expect'
import { createStubInstance, SinonStubbedInstance } from 'sinon'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { SapGetPickUpRequestDetailResponseBuilder } from '../../../../sap/use-cases/get-pick-up-request-detail/tests/get-pick-up-request-detail.response.builder.js'
import { CopyNonSubmittedPickUpRequestError } from '../../../errors/copy-non-submitted-pick-up-request.error.js'
import { CopyPickUpRequestValidator } from '../copy-pick-up-request.validator.js'
import { PickUpRequestNotFoundError } from '../../../errors/pick-up-request-sap-not-found.error.js'
import { InvalidPickUpRequestCopyError } from '../../../errors/invalid-pick-up-request-copy.error.js'
import { AuthContext } from '../../../../auth/auth.context.js'
import { UserWasteProducerAuthService } from '../../../../auth/services/user-waste-producer-auth.service.js'

describe('CopyPickUpRequestValidator - Unit Tests', () => {
  let validator: CopyPickUpRequestValidator

  let authContext: SinonStubbedInstance<AuthContext>
  let userWasteProducerAuthService: SinonStubbedInstance<UserWasteProducerAuthService>

  before(() => {
    TestBench.setupUnitTest()

    authContext = createStubInstance(AuthContext)
    userWasteProducerAuthService = createStubInstance(UserWasteProducerAuthService)

    validator = new CopyPickUpRequestValidator(
      authContext,
      userWasteProducerAuthService
    )

    mockMethods()
  })

  afterEach(() => {
    mockMethods()
  })

  function mockMethods (): void {
    authContext.getSelectedCustomerId.returns(null)
    authContext.getAzureEntraUpn.returns(randomUUID())
    userWasteProducerAuthService.canUserAccessWasteProducer.resolves(true)
  }

  it('throws PickUpRequestNotFoundError when response is empty', async () => {
    await expect(validator.validate(randomUUID(), []))
      .rejects
      .toThrow(PickUpRequestNotFoundError)
  })

  it('throws CopyNonSubmittedPickUpRequestError when pick up request is not submitted', async () => {
    const details = [
      new SapGetPickUpRequestDetailResponseBuilder()
        .withStatus('004')
        .withWasteType('01')
        .build()
    ]

    await expect(validator.validate(randomUUID(), details))
      .rejects
      .toThrow(CopyNonSubmittedPickUpRequestError)
  })

  it('throws InvalidPickUpRequestCopyError when response gives back non pick up requests', async () => {
    const details = [
      new SapGetPickUpRequestDetailResponseBuilder()
        .withStatus('004')
        .withWasteType('06')
        .build()
    ]

    await expect(validator.validate(randomUUID(), details))
      .rejects
      .toThrow(InvalidPickUpRequestCopyError)
  })

  it('throws PickUpRequestNotFoundError when customer is not accessible', async () => {
    authContext.getSelectedCustomerId.returns(randomUUID())

    const details = [
      new SapGetPickUpRequestDetailResponseBuilder()
        .withStatus('000')
        .withWasteType('01')
        .withKunnr(randomUUID())
        .build()
    ]

    await expect(validator.validate(randomUUID(), details))
      .rejects
      .toThrow(PickUpRequestNotFoundError)
  })

  it('throws PickUpRequestNotFoundError when waste producer is not accessible', async () => {
    const customerId = randomUUID()
    authContext.getSelectedCustomerId.returns(customerId)
    userWasteProducerAuthService.canUserAccessWasteProducer.resolves(false)

    const details = [
      new SapGetPickUpRequestDetailResponseBuilder()
        .withStatus('000')
        .withWasteType('01')
        .withKunnr(customerId)
        .withKunnrY2(randomUUID())
        .build()
    ]

    await expect(validator.validate(randomUUID(), details))
      .rejects
      .toThrow(PickUpRequestNotFoundError)
  })

  it('throws no error when the pick up request is submitted', async () => {
    const details = [
      new SapGetPickUpRequestDetailResponseBuilder()
        .withStatus('000')
        .withWasteType('01')
        .build()
    ]

    await expect(validator.validate(randomUUID(), details))
      .resolves
      .not
      .toThrow()
  })
})
