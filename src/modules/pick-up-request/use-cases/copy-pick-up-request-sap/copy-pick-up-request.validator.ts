import { Injectable } from '@nestjs/common'
import { SapGetPickUpRequestDetailResponse } from '../../../sap/use-cases/get-pick-up-request-detail/get-pick-up-request-detail.response.js'
import { CopyNonSubmittedPickUpRequestError } from '../../errors/copy-non-submitted-pick-up-request.error.js'
import { PickUpRequestNotFoundError } from '../../errors/pick-up-request-sap-not-found.error.js'
import { SAP_PICK_UP_WASTE_TYPES } from '../../../sap/types/waste.type.js'
import { InvalidPickUpRequestCopyError } from '../../errors/invalid-pick-up-request-copy.error.js'
import { SAP_SUBMITTED_STATUS } from '../../enums/pick-up-request-status.enum.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { UserWasteProducerAuthService } from '../../../auth/services/user-waste-producer-auth.service.js'

@Injectable()
export class CopyPickUpRequestValidator {
  constructor (
    private readonly authContext: AuthContext,
    private readonly userWasteProducerAuthService: UserWasteProducerAuthService
  ) {}

  async validate (
    requestNumber: string,
    responses: SapGetPickUpRequestDetailResponse[]
  ): Promise<void> {
    this.validateResponse(requestNumber, responses)
    this.validateIsPickUpRequest(requestNumber, responses)
    await this.validatePickUpRequestAccessible(responses)

    const hasNonSubmittedPickUpRequest = responses.some((r) => {
      return r.Status === undefined || !SAP_SUBMITTED_STATUS.includes(r.Status)
    })

    if (hasNonSubmittedPickUpRequest) {
      throw new CopyNonSubmittedPickUpRequestError()
    }
  }

  private validateResponse (
    requestNumber: string,
    responses: SapGetPickUpRequestDetailResponse[]
  ): void {
    if (responses.length === 0) {
      throw new PickUpRequestNotFoundError({ requestNumber })
    }
  }

  private validateIsPickUpRequest (
    requestNumber: string,
    responses: SapGetPickUpRequestDetailResponse[]
  ): void {
    const hasNonPickUpRequestStatus = responses
      .some((response) => {
        return (
          response.WasteType === undefined
          || response.WasteType === ''
          || !SAP_PICK_UP_WASTE_TYPES.includes(response.WasteType))
      })

    if (hasNonPickUpRequestStatus) {
      throw new InvalidPickUpRequestCopyError({ requestNumber })
    }
  }

  private async validatePickUpRequestAccessible (
    responses: SapGetPickUpRequestDetailResponse[]
  ): Promise<void> {
    for (const packagingRequest of responses) {
      if (packagingRequest.Kunnr === undefined || packagingRequest.Kunnr === '') {
        continue
      }

      const selectedCustomerId = this.authContext.getSelectedCustomerId()
      if (selectedCustomerId != null && selectedCustomerId !== packagingRequest.Kunnr) {
        throw new PickUpRequestNotFoundError({ requestNumber: packagingRequest.Reqno ?? 'Reqno not given' })
      }

      if (packagingRequest.KunnrY2 === undefined || packagingRequest.KunnrY2 === '') {
        continue
      }

      const userId = this.authContext.getAzureEntraUpn()
      const canUserAccessWasteProducer = await this.userWasteProducerAuthService
        .canUserAccessWasteProducer(
          userId,
          packagingRequest.Kunnr,
          packagingRequest.KunnrY2
        )
      if (!canUserAccessWasteProducer) {
        throw new PickUpRequestNotFoundError({ requestNumber: packagingRequest.Reqno ?? 'Reqno not given' })
      }
    }
  }
}
