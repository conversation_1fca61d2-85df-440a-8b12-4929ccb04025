import { Module } from '@nestjs/common'
import { FileModule } from '../../../files/file.module.js'
import { SharepointModule } from '../../../sharepoint/sharepoint.module.js'
import { UploadPickUpRequestDocumentUseCase } from './upload-pick-up-request-document.use-case.js'

@Module({
  imports: [
    FileModule,
    SharepointModule
  ],
  providers: [
    UploadPickUpRequestDocumentUseCase
  ],
  exports: [
    UploadPickUpRequestDocumentUseCase
  ]
})
export class UploadPickUpRequestDocumentModule {}
