import { before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import { createStubInstance } from 'sinon'
import { expect } from 'expect'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { AzureBlobService } from '../../../../files/services/azure-blob.service.js'
import { SharepointUploadDocumentUseCase } from '../../../../sharepoint/use-cases/upload-document/upload-document.use-case.js'
import { UploadPickUpRequestDocumentUseCase } from '../upload-pick-up-request-document.use-case.js'
import { FileEntityBuilder } from '../../../../files/entities/file-entity.builder.js'
import { SharepointCommandBuilder } from '../../../../sharepoint/builders/sharepoint-command.builder.js'
import { DEFAULT_SHAREPOINT_SITE_TITLE } from '../../../../sharepoint/constants/sharepoint-site-title.constant.js'
import { SHAREPOINT_LIBRARY } from '../../../../sharepoint/constants/sharepoint-library.constant.js'
import { SharepointLibraryNameEnvMapper } from '../../../../sharepoint/mappers/sharepoint-library.mapper.js'

describe('UploadPickUpRequestDocumentUseCase - Unit Tests', () => {
  before(() => TestBench.setupUnitTest())

  it('downloads the buffer and uploads to sharepoint', async () => {
    const requestNumber = randomUUID()
    const file = new FileEntityBuilder().build()

    const azureBlobService = createStubInstance(AzureBlobService)
    azureBlobService.downloadFileBuffer.resolves(Buffer.from('123'))
    const uploadUseCase = createStubInstance(SharepointUploadDocumentUseCase)

    const useCase = new UploadPickUpRequestDocumentUseCase(azureBlobService, uploadUseCase)

    await useCase.execute(requestNumber, [file])

    expect(azureBlobService.downloadFileBuffer.calledOnce).toBe(true)
    expect(uploadUseCase.execute.calledOnce).toBe(true)
  })

  it('maps the buffer to the correct command', async () => {
    const requestNumber = randomUUID()
    const file = new FileEntityBuilder().build()

    const azureBlobService = createStubInstance(AzureBlobService)
    const buffer = Buffer.from('123')
    azureBlobService.downloadFileBuffer.resolves(buffer)
    const uploadUseCase = createStubInstance(SharepointUploadDocumentUseCase)

    const useCase = new UploadPickUpRequestDocumentUseCase(azureBlobService, uploadUseCase)

    await useCase.execute(requestNumber, [file])

    const usedCommand = uploadUseCase.execute.args[0][0]

    const libraryName = SharepointLibraryNameEnvMapper
      .toSharepointLibraryName(SHAREPOINT_LIBRARY.PICK_UP_REQUEST)
    const expectedCommand = new SharepointCommandBuilder()
      .addSiteTitle(DEFAULT_SHAREPOINT_SITE_TITLE)
      .addLibraryName(libraryName)
      .addFileName(`${requestNumber}_${file.name}`)
      .withSource(buffer.toString('base64'))
      .build()

    expect(usedCommand).toEqual(expectedCommand)
  })
})
