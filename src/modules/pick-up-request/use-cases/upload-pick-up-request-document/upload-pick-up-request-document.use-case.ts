import { Injectable } from '@nestjs/common'
import { SharepointUploadDocumentUseCase } from '../../../sharepoint/use-cases/upload-document/upload-document.use-case.js'
import { File } from '../../../files/entities/file.entity.js'
import { SharepointCommandBuilder } from '../../../sharepoint/builders/sharepoint-command.builder.js'
import { SHAREPOINT_LIBRARY } from '../../../sharepoint/constants/sharepoint-library.constant.js'
import { DEFAULT_SHAREPOINT_SITE_TITLE } from '../../../sharepoint/constants/sharepoint-site-title.constant.js'
import { SharepointLibraryNameEnvMapper } from '../../../sharepoint/mappers/sharepoint-library.mapper.js'
import { AzureBlobService } from '../../../files/services/azure-blob.service.js'

@Injectable()
export class UploadPickUpRequestDocumentUseCase {
  constructor (
    private readonly azureBlobService: AzureBlobService,
    private readonly uploadSharepointUseCase: SharepointUploadDocumentUseCase
  ) {}

  async execute (requestNumber: string, files: File[]): Promise<void> {
    if (files.length === 0) {
      return
    }

    await Promise.all(
      files.map(async (file) => {
        const buffer = await this.azureBlobService.downloadFileBuffer(file)
        const base64Content = buffer.toString('base64')

        const libraryName = SharepointLibraryNameEnvMapper
          .toSharepointLibraryName(SHAREPOINT_LIBRARY.PICK_UP_REQUEST)

        const fileName = `${requestNumber}_${file.name}`

        const command = new SharepointCommandBuilder()
          .addSiteTitle(DEFAULT_SHAREPOINT_SITE_TITLE)
          .addLibraryName(libraryName)
          .addFileName(fileName)
          .withSource(base64Content)
          .build()

        return this.uploadSharepointUseCase.execute(command)
      })
    )
  }
}
