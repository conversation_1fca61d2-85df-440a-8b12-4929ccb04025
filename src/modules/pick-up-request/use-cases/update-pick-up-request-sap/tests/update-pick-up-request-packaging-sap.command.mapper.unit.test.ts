import { describe, it, before } from 'node:test'
import { randomUUID } from 'crypto'
import { expect } from 'expect'
import { UpdatePickUpRequestPackagingSapCommandMapper } from '../mappers/update-pick-up-request-packaging-sap.command.mapper.js'
import { UserEntityBuilder } from '../../../../../app/users/tests/user-entity.builder.js'
import { mapLocaleToSapLocale } from '../../../../localization/enums/locale.enum.js'
import { getCurrentLanguage } from '../../../../localization/helpers/translate.helper.js'
import { mapWasteMeasurementUnitToSapValue, WasteMeasurementUnit } from '../../../../waste-inquiry/enums/waste-measurement-unit.enum.js'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { PickUpRequestContractLineNotFoundError } from '../../../errors/pick-up-request-contract-line-not-found.error.js'
import { SapGetIndascanDetailResponseBuilder } from '../../../../sap/use-cases/get-indascan-detail/tests/builders/get-indascan-detail.response.builder.js'
import { SapDateFormatterService } from '../../../../sap/services/sap-date-formatter.service.js'
import { UpdatePickUpRequestSapPackagingRequestCommandBuilder } from './builders/update-pick-up-request-packaging-request.command-sap.builder.js'
import { UpdatePickUpRequestSapCommandBuilder } from './builders/update-pick-up-request-sap.command.builder.js'
import { UpdatePickUpRequestPackagingMaterialSapCommandBuilder } from './builders/update-pick-up-request-packaging-material-sap.command.builder.js'

describe('UpdatePickUpRequestPackagingSapCommandMapper - Unit Tests', () => {
  before(() => {
    TestBench.setupUnitTest()
  })

  describe('empty', () => {
    it('returns all packaging requests when command is undefined', () => {
      const requestNumber = randomUUID()
      const currentUser = new UserEntityBuilder().build()

      const command = new UpdatePickUpRequestSapCommandBuilder()
        .withPackagingRequest(undefined)
        .build()

      const indascanRequestItem1 = new SapGetIndascanDetailResponseBuilder()
        .withWasteType('06')
        .build()

      const indascanRequestItem2 = new SapGetIndascanDetailResponseBuilder()
        .withWasteType('01')
        .build()

      const result = UpdatePickUpRequestPackagingSapCommandMapper.mapPackagingCommandToSapCommands(
        requestNumber,
        currentUser,
        command,
        [indascanRequestItem1, indascanRequestItem2]
      )
      expect(result).toHaveLength(1)
      expect(result).toEqual(expect.arrayContaining([indascanRequestItem1]))
    })

    it('returns empty array when packaging is an empty array', () => {
      const requestNumber = randomUUID()
      const currentUser = new UserEntityBuilder().build()
      const packagingRequest = new UpdatePickUpRequestSapPackagingRequestCommandBuilder()
        .withPackagingRequestMaterials([])
        .build()
      const command = new UpdatePickUpRequestSapCommandBuilder()
        .withPackagingRequest(packagingRequest)
        .build()
      const indascanItem = new SapGetIndascanDetailResponseBuilder()
        .withWasteType('06')
        .build()

      const result = UpdatePickUpRequestPackagingSapCommandMapper.mapPackagingCommandToSapCommands(
        requestNumber,
        currentUser,
        command,
        [indascanItem]
      )

      expect(result).toHaveLength(0)
    })
  })

  describe('create', () => {
    it('maps correctly to a create sap command', () => {
      const requestNumber = randomUUID()
      const currentUser = new UserEntityBuilder().build()
      const packagingMaterial = new UpdatePickUpRequestPackagingMaterialSapCommandBuilder()
        .withPosition(null)
        .build()
      const packagingRequest = new UpdatePickUpRequestSapPackagingRequestCommandBuilder()
        .withPackagingRequestMaterials([packagingMaterial])
        .build()
      const command = new UpdatePickUpRequestSapCommandBuilder()
        .withPackagingRequest(packagingRequest)
        .build()
      const indascanItem = new SapGetIndascanDetailResponseBuilder()
        .withRequestNumber(requestNumber)
        .withWasteType('06')
        .build()

      const result = UpdatePickUpRequestPackagingSapCommandMapper.mapPackagingCommandToSapCommands(
        requestNumber,
        currentUser,
        command,
        [indascanItem]
      )

      const mappedSapCommand = result.at(0)

      expect(mappedSapCommand).toBeDefined()
      expect(mappedSapCommand).toEqual(expect.objectContaining({
        Reqno: requestNumber,
        NameApplicant: currentUser.fullName,
        EmailApplicant: currentUser.email,
        Kunnr: packagingRequest.customerId,
        KunnrY2: packagingRequest.wasteProducerId,
        KunnrWe: packagingRequest.deliveryAddressId,
        Vbeln: packagingMaterial.contractNumber,
        Posnr: packagingMaterial.contractItem,
        Matnr: packagingMaterial.materialNumber,
        Arktx: packagingMaterial.wasteMaterial,
        WeightUom: mapWasteMeasurementUnitToSapValue(WasteMeasurementUnit.PC),
        CostCenter: packagingMaterial.costCenter ?? undefined,
        OrderNumber: packagingMaterial.poNumber ?? undefined,
        WeightVolume: packagingMaterial.quantity.toString(),
        RequestedDate: SapDateFormatterService.toMutationDate(command.startDate),
        ReqDateTo: SapDateFormatterService.toMutationDate(command.endDate),
        CreateLangu: mapLocaleToSapLocale(getCurrentLanguage()),
        Vkorg: indascanItem.Vkorg
      }))
    })

    describe('update', () => {
      it('throws PickUpRequestItemNotFoundError when no matching waste material is found', () => {
        const requestNumber = randomUUID()
        const currentUser = new UserEntityBuilder().build()
        const packagingMaterial = new UpdatePickUpRequestPackagingMaterialSapCommandBuilder()
          .withPosition(randomUUID())
          .build()
        const packagingRequest = new UpdatePickUpRequestSapPackagingRequestCommandBuilder()
          .withPackagingRequestMaterials([packagingMaterial])
          .build()
        const command = new UpdatePickUpRequestSapCommandBuilder()
          .withPackagingRequest(packagingRequest)
          .build()
        const indascanItem = new SapGetIndascanDetailResponseBuilder()
          .withWasteType('06')
          .build()

        expect(() => UpdatePickUpRequestPackagingSapCommandMapper.mapPackagingCommandToSapCommands(
          requestNumber,
          currentUser,
          command,
          [indascanItem]
        )).toThrow(PickUpRequestContractLineNotFoundError)
      })

      it('maps correctly to an update sap command', () => {
        const requestNumber = randomUUID()
        const position = randomUUID()
        const currentUser = new UserEntityBuilder().build()
        const packagingMaterial = new UpdatePickUpRequestPackagingMaterialSapCommandBuilder()
          .withPosition(position)
          .build()
        const packagingRequest = new UpdatePickUpRequestSapPackagingRequestCommandBuilder()
          .withPackagingRequestMaterials([packagingMaterial])
          .build()
        const command = new UpdatePickUpRequestSapCommandBuilder()
          .withPackagingRequest(packagingRequest)
          .build()
        const indascanItem = new SapGetIndascanDetailResponseBuilder()
          .withPosition(position)
          .withWasteType('06')
          .build()

        const result = UpdatePickUpRequestPackagingSapCommandMapper
          .mapPackagingCommandToSapCommands(
            requestNumber,
            currentUser,
            command,
            [indascanItem]
          )

        const mappedSapCommand = result.at(0)

        expect(mappedSapCommand).toBeDefined()
        expect(mappedSapCommand).toEqual(expect.objectContaining({
          Reqno: indascanItem.Reqno,
          NameApplicant: currentUser.fullName,
          EmailApplicant: currentUser.email,
          Kunnr: packagingRequest.customerId,
          KunnrY2: packagingRequest.wasteProducerId,
          KunnrWe: packagingRequest.deliveryAddressId,
          Vbeln: packagingMaterial.contractNumber,
          Posnr: packagingMaterial.contractItem,
          Matnr: packagingMaterial.materialNumber,
          Arktx: packagingMaterial.wasteMaterial,
          WeightUom: mapWasteMeasurementUnitToSapValue(WasteMeasurementUnit.PC),
          CostCenter: packagingMaterial.costCenter ?? undefined,
          OrderNumber: packagingMaterial.poNumber ?? undefined,
          WeightVolume: packagingMaterial.quantity.toString(),
          RequestedDate: SapDateFormatterService.toMutationDate(command.startDate),
          ReqDateTo: SapDateFormatterService.toMutationDate(command.endDate),
          CreateLangu: mapLocaleToSapLocale(getCurrentLanguage()),
          Vkorg: indascanItem.Vkorg
        }))
      })
    })
  })
})
