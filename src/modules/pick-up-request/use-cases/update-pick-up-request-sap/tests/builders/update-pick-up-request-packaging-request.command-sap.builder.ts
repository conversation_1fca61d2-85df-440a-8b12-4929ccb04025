import { randomUUID } from 'crypto'
import { UpdatePickUpRequestPackagingMaterialSapCommand, UpdatePickUpRequestPackagingSapCommand } from '../../update-pick-up-request-sap.command.js'
import { UpdatePickUpRequestPackagingMaterialSapCommandBuilder } from './update-pick-up-request-packaging-material-sap.command.builder.js'

export class UpdatePickUpRequestSapPackagingRequestCommandBuilder {
  private command: UpdatePickUpRequestPackagingSapCommand

  constructor () {
    this.reset()
  }

  private reset (): this {
    this.command = new UpdatePickUpRequestPackagingSapCommand()

    this.command.customerId = randomUUID()
    this.command.wasteProducerId = randomUUID()
    this.command.deliveryAddressId = randomUUID()
    this.command.packagingRequestMaterials = [
      new UpdatePickUpRequestPackagingMaterialSapCommandBuilder().build()
    ]

    return this
  }

  public withCustomerId (customerId: string | null): this {
    this.command.customerId = customerId

    return this
  }

  public withWasteProducerId (wasteProducerId: string | null): this {
    this.command.wasteProducerId = wasteProducerId

    return this
  }

  public withDeliveryAddressId (deliveryAddressId: string | null): this {
    this.command.deliveryAddressId = deliveryAddressId

    return this
  }

  public withPackagingRequestMaterials (
    packagingRequestMaterials?: UpdatePickUpRequestPackagingMaterialSapCommand[]
  ): this {
    this.command.packagingRequestMaterials = packagingRequestMaterials

    return this
  }

  public build (): UpdatePickUpRequestPackagingSapCommand {
    const result = this.command

    this.reset()

    return result
  }
}
