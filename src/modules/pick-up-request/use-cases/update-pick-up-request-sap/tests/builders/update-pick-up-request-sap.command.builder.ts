import { randomUUID } from 'crypto'
import dayjs from 'dayjs'
import { UpdatePickUpRequestMaterialSapCommand, UpdatePickUpRequestPackagingSapCommand, UpdatePickUpRequestSapCommand } from '../../update-pick-up-request-sap.command.js'
import { Contact } from '../../../../../contact/types/contact.type.js'
import { CreateFileLinkCommand } from '../../../../../files/commands/create-file-link.command.js'
import { UpdatePickUpRequestMaterialSapCommandBuilder } from './update-pick-up-request-material-sap.command.builder.js'
import { UpdatePickUpRequestSapPackagingRequestCommandBuilder } from './update-pick-up-request-packaging-request.command-sap.builder.js'

export class UpdatePickUpRequestSapCommandBuilder {
  private command: UpdatePickUpRequestSapCommand

  constructor () {
    this.reset()
  }

  private reset (): this {
    this.command = new UpdatePickUpRequestSapCommand()

    this.command.contacts = [{
      email: '<EMAIL>',
      firstName: 'John',
      lastName: 'Doe'
    }]
    this.command.additionalFiles = []
    this.command.materials = [new UpdatePickUpRequestMaterialSapCommandBuilder().build()]
    this.command.remarks = randomUUID()
    this.command.packagingRequest = new UpdatePickUpRequestSapPackagingRequestCommandBuilder()
      .build()
    this.command.startDate = dayjs().format('YYYY-MM-DD')
    this.command.endDate = dayjs().add(1, 'week').format('YYYY-MM-DD')
    this.command.packagingRemark = randomUUID()
    this.command.isReturnPackaging = true

    return this
  }

  withContacts (contacts: Contact[]): this {
    this.command.contacts = contacts
    return this
  }

  withAdditionalFiles (fileLinks: CreateFileLinkCommand[]): this {
    this.command.additionalFiles = fileLinks
    return this
  }

  withMaterials (materials?: UpdatePickUpRequestMaterialSapCommand[]): this {
    this.command.materials = materials
    return this
  }

  withRemarks (remarks: string | null): this {
    this.command.remarks = remarks
    return this
  }

  withPackagingRequest (
    packagingRequest?: UpdatePickUpRequestPackagingSapCommand
  ): this {
    this.command.packagingRequest = packagingRequest
    return this
  }

  withStartDate (startDate: string | Date): this {
    this.command.startDate = dayjs(startDate).format('YYYY-MM-DD')

    return this
  }

  withEndDate (endDate: string | Date): this {
    this.command.endDate = dayjs(endDate).format('YYYY-MM-DD')

    return this
  }

  withPackagingRemark (remark: string | null): this {
    this.command.remarks = remark
    return this
  }

  withIsReturnPackaging (isReturn: boolean | null): this {
    this.command.isReturnPackaging = isReturn
    return this
  }

  build (): UpdatePickUpRequestSapCommand {
    const result = this.command

    this.reset()

    return result
  }
}
