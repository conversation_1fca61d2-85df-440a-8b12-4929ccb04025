import { describe, it, before } from 'node:test'
import { randomUUID } from 'crypto'
import { expect } from 'expect'
import { UpdatePickUpRequestMaterialSapCommandMapper } from '../mappers/update-pick-up-request-material-sap.command.mapper.js'
import { UserEntityBuilder } from '../../../../../app/users/tests/user-entity.builder.js'
import { PickUpRequestContractLineNotFoundError } from '../../../errors/pick-up-request-contract-line-not-found.error.js'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { InvalidUpdateSapPickUpRequestError } from '../../../errors/invalid-update-sap-pick-up-request-error.js'
import { SapGetIndascanDetailResponseBuilder } from '../../../../sap/use-cases/get-indascan-detail/tests/builders/get-indascan-detail.response.builder.js'
import { SapDateFormatterService } from '../../../../sap/services/sap-date-formatter.service.js'
import { UpdatePickUpRequestMaterialSapCommandBuilder } from './builders/update-pick-up-request-material-sap.command.builder.js'
import { UpdatePickUpRequestSapCommandBuilder } from './builders/update-pick-up-request-sap.command.builder.js'

describe('UpdatePickUpRequestMaterialSapCommandMapper - Unit Tests', () => {
  before (() => {
    TestBench.setupUnitTest()
  })

  it('maps the command correctly to the sap command', () => {
    const requestNumber = randomUUID()
    const currentUser = new UserEntityBuilder().build()
    const position = '1'
    const materialCommand = new UpdatePickUpRequestMaterialSapCommandBuilder()
      .withPosition(position)
      .withCostCenter(randomUUID())
      .withPoNumber(randomUUID())
      .build()
    const command = new UpdatePickUpRequestSapCommandBuilder()
      .withMaterials([materialCommand])
      .build()

    const indascanItem = new SapGetIndascanDetailResponseBuilder()
      .withRequestNumber(requestNumber)
      .withWasteType('03')
      .withPosition(position)
      .build()

    const result = UpdatePickUpRequestMaterialSapCommandMapper.mapWasteMaterialCommandToSapCommands(
      requestNumber,
      currentUser,
      command,
      [indascanItem]
    )

    const mappedSapCommand = result.at(0)

    expect(mappedSapCommand).toBeDefined()
    expect(mappedSapCommand).toEqual(expect.objectContaining({
      ...indascanItem,
      QuantityLabels: '0',
      Reqpos: materialCommand.position,
      CostCenter: materialCommand.costCenter,
      OrderNumber: materialCommand.poNumber,
      NameApplicant: currentUser.fullName,
      EmailApplicant: currentUser.email,
      ReturnPackaging: command.isReturnPackaging,
      ReturnPackagingRemark: command.packagingRemark,
      RequestedDate: SapDateFormatterService.toMutationDate(command.startDate),
      ReqDateTo: SapDateFormatterService.toMutationDate(command.endDate)
    }))
  })

  it('returns all pick up request item responses that are not packaging requests when command is undefined', () => {
    const requestNumber = randomUUID()
    const currentUser = new UserEntityBuilder().build()
    const command = new UpdatePickUpRequestSapCommandBuilder()
      .withMaterials(undefined)
      .build()

    const indascanItem1 = new SapGetIndascanDetailResponseBuilder()
      .withRequestNumber(requestNumber)
      .withWasteType('01')
      .build()

    const indascanItem2 = new SapGetIndascanDetailResponseBuilder()
      .withRequestNumber(requestNumber)
      .withWasteType('06')
      .build()

    const result = UpdatePickUpRequestMaterialSapCommandMapper.mapWasteMaterialCommandToSapCommands(
      requestNumber,
      currentUser,
      command,
      [indascanItem1, indascanItem2]
    )

    const mappedSapCommand = result.at(0)

    expect(mappedSapCommand).toBeDefined()
    expect(mappedSapCommand).toEqual(expect.objectContaining({
      ...indascanItem1
    }))
  })

  it('throws PickUpRequestContractLineNotFoundError when material to update does not exists', () => {
    const requestNumber = randomUUID()
    const currentUser = new UserEntityBuilder().build()
    const position = '1'
    const materialCommand = new UpdatePickUpRequestMaterialSapCommandBuilder()
      .withPosition(randomUUID())
      .withCostCenter(randomUUID())
      .withPoNumber(randomUUID())
      .build()
    const command = new UpdatePickUpRequestSapCommandBuilder()
      .withMaterials([materialCommand])
      .build()

    const indascanItems = new SapGetIndascanDetailResponseBuilder()
      .withRequestNumber(requestNumber)
      .withWasteType('02')
      .withPosition(position)
      .build()

    expect(() => UpdatePickUpRequestMaterialSapCommandMapper.mapWasteMaterialCommandToSapCommands(
      requestNumber,
      currentUser,
      command,
      [indascanItems]
    )).toThrow(PickUpRequestContractLineNotFoundError)
  })

  it('throws InvalidUpdateSapPickUpRequestError when the amount materials do not match the amount of SAP materials', () => {
    const requestNumber = randomUUID()
    const currentUser = new UserEntityBuilder().build()
    const position = '1'
    const materialCommand = new UpdatePickUpRequestMaterialSapCommandBuilder()
      .withPosition(randomUUID())
      .withCostCenter(randomUUID())
      .withPoNumber(randomUUID())
      .build()
    const command = new UpdatePickUpRequestSapCommandBuilder()
      .withMaterials([materialCommand])
      .build()

    const indascanItems = Array.from({ length: 3 }, () => {
      return new SapGetIndascanDetailResponseBuilder()
        .withRequestNumber(requestNumber)
        .withPosition(position)
        .withWasteType('03')
        .build()
    })

    expect(() => UpdatePickUpRequestMaterialSapCommandMapper.mapWasteMaterialCommandToSapCommands(
      requestNumber,
      currentUser,
      command,
      indascanItems
    )).toThrow(InvalidUpdateSapPickUpRequestError)
  })
})
