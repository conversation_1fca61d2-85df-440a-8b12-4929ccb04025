import { after, before, describe, it } from 'node:test'
import request from 'supertest'
import { expect } from 'expect'
import { HttpStatus } from '@nestjs/common'
import dayjs from 'dayjs'
import { EndToEndTestSetup } from '../../../../../../test/setup/end-to-end-test-setup.js'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { Permission } from '../../../../permission/permission.enum.js'
import { UpdatePickUpRequestSapCommandBuilder } from './builders/update-pick-up-request-sap.command.builder.js'

describe('UpdatePickUpRequestSap - E2E tests', () => {
  let setup: EndToEndTestSetup

  before(async () => {
    setup = await TestBench.setupEndToEndTest()
  })

  after(async () => await setup.teardown())

  it('updates the sap pickup request', async () => {
    const user = await setup.authContext.getUser([Permission.PICK_UP_REQUEST_MANAGE])
    const command = new UpdatePickUpRequestSapCommandBuilder()
      .withMaterials(undefined)
      .withPackagingRequest(undefined)
      .withIsReturnPackaging(true)
      .withPackagingRemark('something')
      .withStartDate(dayjs().add(1, 'day').format('YYYY-MM-DD'))
      .withEndDate(dayjs().add(10, 'day').format('YYYY-MM-DD'))
      .build()

    const response = await request(setup.httpServer)
      .patch(`/pick-up-requests/sap/123`)
      .set('Authorization', `Bearer ${user.token}`)
      .send(command)

    expect(response).toHaveStatus(HttpStatus.OK)
  })
})
