import assert from 'assert'
import { User } from '../../../../../app/users/entities/user.entity.js'
import { SapUpdatePickUpRequestItemCommand } from '../../../../sap/use-cases/update-pick-up-request/update-pick-up-request.command.js'
import { PickUpRequestContractLineNotFoundError } from '../../../errors/pick-up-request-contract-line-not-found.error.js'
import { UpdatePickUpRequestMaterialSapCommand, UpdatePickUpRequestSapCommand } from '../update-pick-up-request-sap.command.js'
import { mapTransportModeToSapValue, PickUpTransportMode } from '../../../enums/pick-up-transport-mode.enum.js'
import { InvalidUpdateSapPickUpRequestError } from '../../../errors/invalid-update-sap-pick-up-request-error.js'
import { SapGetIndascanDetailResponse } from '../../../../sap/use-cases/get-indascan-detail/get-indascan-detail.response.js'
import { SapDateFormatterService } from '../../../../sap/services/sap-date-formatter.service.js'

export class UpdatePickUpRequestMaterialSapCommandMapper {
  public static mapWasteMaterialCommandToSapCommands (
    requestNumber: string,
    currentUser: User,
    command: UpdatePickUpRequestSapCommand,
    pickUpRequestItems: SapGetIndascanDetailResponse[]
  ): SapUpdatePickUpRequestItemCommand[] {
    const pickUpTransportModes = Object.values(PickUpTransportMode).map(mode =>
      mapTransportModeToSapValue(mode)
    )

    const wasteMaterials = pickUpRequestItems.filter(item =>
      item.WasteType !== undefined
      && item.WasteType !== ''
      && pickUpTransportModes.includes(item.WasteType)
    )

    if (command.materials === undefined) {
      return wasteMaterials
    }

    if (wasteMaterials.length !== command.materials.length) {
      throw new InvalidUpdateSapPickUpRequestError()
    }

    assert(command.materials !== undefined)

    const commands: SapUpdatePickUpRequestItemCommand[] = []

    for (const materialCommand of command.materials) {
      const matchingPickUpRequestItem = pickUpRequestItems
        .find(item => item.Reqpos === materialCommand.position && item.WasteType !== '06')

      if (matchingPickUpRequestItem === undefined) {
        throw new PickUpRequestContractLineNotFoundError(materialCommand.position, requestNumber)
      }

      const sapCommand = this.mapWasteMaterialCommandToSapCommand(
        currentUser,
        command,
        materialCommand,
        matchingPickUpRequestItem
      )

      commands.push(sapCommand)
    }

    return commands
  }

  private static mapWasteMaterialCommandToSapCommand (
    currentUser: User,
    pickUpRequestCommand: UpdatePickUpRequestSapCommand,
    materialCommand: UpdatePickUpRequestMaterialSapCommand,
    indascanItem: SapGetIndascanDetailResponse
  ): SapUpdatePickUpRequestItemCommand {
    const start = pickUpRequestCommand.startDate != null
      ? SapDateFormatterService.toMutationDate(pickUpRequestCommand.startDate)
      : indascanItem.RequestedDate

    assert(start !== null)

    return {
      ...indascanItem,
      CostCenter: materialCommand.costCenter ?? indascanItem.CostCenter,
      OrderNumber: materialCommand.poNumber ?? indascanItem.OrderNumber,
      NameApplicant: currentUser.fullName,
      EmailApplicant: currentUser.email,
      QuantityLabels: '0',
      ReturnPackaging: pickUpRequestCommand.isReturnPackaging ?? undefined,
      ReturnPackagingRemark: pickUpRequestCommand.packagingRemark ?? undefined,
      RequestedDate: start,
      ReqDateTo: SapDateFormatterService.toMutationDate(pickUpRequestCommand.endDate)
    }
  }
}
