import assert from 'assert'
import { User } from '../../../../../app/users/entities/user.entity.js'
import { SapUpdatePickUpRequestItemCommand } from '../../../../sap/use-cases/update-pick-up-request/update-pick-up-request.command.js'
import { UpdatePickUpRequestSapCommand } from '../update-pick-up-request-sap.command.js'
import { PickUpRequestContractLineNotFoundError } from '../../../errors/pick-up-request-contract-line-not-found.error.js'
import { PackagingRequestMaterialCommand } from '../../../../packaging-request/use-cases/update-packaging-request/commands/packaging-request-material.command.js'
import { mapWasteMeasurementUnitToSapValue, WasteMeasurementUnit } from '../../../../waste-inquiry/enums/waste-measurement-unit.enum.js'
import { mapLocaleToSapLocale } from '../../../../localization/enums/locale.enum.js'
import { getCurrentLanguage } from '../../../../localization/helpers/translate.helper.js'
import { SapGetIndascanDetailResponse } from '../../../../sap/use-cases/get-indascan-detail/get-indascan-detail.response.js'
import { SapDateFormatterService } from '../../../../sap/services/sap-date-formatter.service.js'

export class UpdatePickUpRequestPackagingSapCommandMapper {
  public static mapPackagingCommandToSapCommands (
    requestNumber: string,
    currentUser: User,
    command: UpdatePickUpRequestSapCommand,
    indascanItems: SapGetIndascanDetailResponse[]
  ): SapUpdatePickUpRequestItemCommand[] {
    if (command.packagingRequest === undefined) {
      return indascanItems.filter(item => item.WasteType === '06')
    }

    if (command.packagingRequest.packagingRequestMaterials !== undefined
      && command.packagingRequest.packagingRequestMaterials.length === 0) {
      return []
    }

    assert(command.packagingRequest.packagingRequestMaterials !== undefined)

    const commands: SapUpdatePickUpRequestItemCommand[] = []

    for (const packagingCommand of command.packagingRequest.packagingRequestMaterials) {
      if (packagingCommand.position !== null) {
        const matchingPackagingItem = indascanItems.find(item =>
          item.Reqpos === packagingCommand.position && item.WasteType === '06'
        )

        if (matchingPackagingItem === undefined) {
          throw new PickUpRequestContractLineNotFoundError(packagingCommand.position, requestNumber)
        }

        commands.push(this.mapPackagingCommandToSapCommand(
          currentUser,
          command,
          packagingCommand,
          matchingPackagingItem,
          false
        ))
      } else {
        commands.push(this.mapPackagingCommandToSapCommand(
          currentUser,
          command,
          packagingCommand,
          indascanItems[0]
        ))
      }
    }

    return commands
  }

  private static mapPackagingCommandToSapCommand (
    currentUser: User,
    pickUpRequestCommand: UpdatePickUpRequestSapCommand,
    packagingCommand: PackagingRequestMaterialCommand,
    indascanItem: SapGetIndascanDetailResponse,
    isCreate: boolean = true
  ): SapUpdatePickUpRequestItemCommand {
    assert(pickUpRequestCommand.packagingRequest !== undefined)

    const start = pickUpRequestCommand.startDate != null
      ? SapDateFormatterService.toMutationDate(pickUpRequestCommand.startDate)
      : indascanItem.RequestedDate

    assert(start !== null)

    const baseCommand: SapUpdatePickUpRequestItemCommand = {
      NameApplicant: currentUser.fullName,
      EmailApplicant: currentUser.email,
      Kunnr: pickUpRequestCommand.packagingRequest.customerId ?? undefined,
      KunnrY2: pickUpRequestCommand.packagingRequest.wasteProducerId ?? undefined,
      KunnrWe: pickUpRequestCommand.packagingRequest.deliveryAddressId ?? undefined,
      Vbeln: packagingCommand.contractNumber,
      Posnr: packagingCommand.contractItem,
      Matnr: packagingCommand.materialNumber ?? undefined,
      Arktx: packagingCommand.wasteMaterial ?? undefined,
      WeightUom: mapWasteMeasurementUnitToSapValue(WasteMeasurementUnit.PC),
      CostCenter: packagingCommand.costCenter ?? undefined,
      OrderNumber: packagingCommand.poNumber ?? undefined,
      WeightVolume: packagingCommand.quantity.toString(),
      CreateLangu: mapLocaleToSapLocale(getCurrentLanguage()),
      RequestedDate: start,
      ReqDateTo: SapDateFormatterService.toMutationDate(pickUpRequestCommand.endDate)
    }

    return isCreate
      ? {
          ...baseCommand,
          Vkorg: indascanItem.Vkorg,
          Reqno: indascanItem.Reqno
        }
      : {
          ...indascanItem,
          ...baseCommand
        }
  }
}
