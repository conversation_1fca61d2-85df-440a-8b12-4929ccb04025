import { ApiProperty } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { ValidateNested, IsArray, IsNotEmpty, IsString, IsOptional, <PERSON>Length, ArrayMinSize, IsBoolean } from 'class-validator'
import { IsDateWithoutTimeString, IsNullable, IsSameOrAfterDateString, IsUndefinable } from '@wisemen/validators'
import { Contact } from '../../../contact/types/contact.type.js'
import { CreateFileLinkCommand } from '../../../files/commands/create-file-link.command.js'
import { PackagingRequestMaterialCommand } from '../../../packaging-request/use-cases/update-packaging-request/commands/packaging-request-material.command.js'

export class UpdatePickUpRequestMaterialSapCommand {
  @ApiProperty({ type: String, required: false, nullable: true, maxLength: 25 })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  @MaxLength(25)
  costCenter?: string | null

  @ApiProperty({ type: String, required: false, nullable: true, maxLength: 25 })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  @MaxLength(25)
  poNumber?: string | null

  @ApiProperty({ type: String, maxLength: 6 })
  @IsString()
  @IsNotEmpty()
  @MaxLength(6)
  position: string
}

export class UpdatePickUpRequestPackagingMaterialSapCommand
  extends PackagingRequestMaterialCommand {
  @ApiProperty({ type: String, maxLength: 6, nullable: true })
  @IsNullable()
  @IsString()
  @IsNotEmpty()
  @MaxLength(6)
  position: string | null
}

export class UpdatePickUpRequestPackagingSapCommand {
  @ApiProperty({ type: String, required: false, nullable: true })
  @IsOptional()
  @IsNullable()
  @IsString()
  @IsNotEmpty()
  customerId?: string | null

  @ApiProperty({ type: String, required: false, nullable: true })
  @IsOptional()
  @IsNullable()
  @IsString()
  @IsNotEmpty()
  wasteProducerId?: string | null

  @ApiProperty({ type: String, required: false, nullable: true })
  @IsOptional()
  @IsNullable()
  @IsString()
  @IsNotEmpty()
  deliveryAddressId?: string | null

  @ApiProperty({
    type: UpdatePickUpRequestPackagingMaterialSapCommand,
    required: false,
    isArray: true
  })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => UpdatePickUpRequestPackagingMaterialSapCommand)
  @IsArray()
  packagingRequestMaterials?: UpdatePickUpRequestPackagingMaterialSapCommand[]
}

export class UpdatePickUpRequestSapCommand {
  @ApiProperty({ type: Contact, required: false, isArray: true })
  @IsUndefinable()
  @ValidateNested({ each: true })
  @Type(() => Contact)
  @IsArray()
  contacts?: Contact[]

  @ApiProperty({ type: UpdatePickUpRequestMaterialSapCommand, required: false, isArray: true })
  @IsUndefinable()
  @ValidateNested({ each: true })
  @Type(() => UpdatePickUpRequestMaterialSapCommand)
  @IsArray()
  @ArrayMinSize(1)
  materials?: UpdatePickUpRequestMaterialSapCommand[]

  @ApiProperty({ type: String, required: false, nullable: true, format: 'date' })
  @IsOptional()
  @IsDateWithoutTimeString()
  startDate?: string | null

  @ApiProperty({ type: String, required: false, nullable: true, format: 'date' })
  @IsOptional()
  @IsDateWithoutTimeString()
  @IsSameOrAfterDateString((c: UpdatePickUpRequestSapCommand) => c.startDate ?? undefined)
  endDate?: string | null

  @ApiProperty({ type: Boolean, required: false, nullable: true })
  @IsOptional()
  @IsNullable()
  @IsBoolean()
  isReturnPackaging?: boolean | null

  @ApiProperty({ type: String, required: false, nullable: true, maxLength: 200 })
  @IsOptional()
  @IsNullable()
  @IsString()
  @IsNotEmpty()
  @MaxLength(200)
  packagingRemark?: string | null

  @ApiProperty({ type: UpdatePickUpRequestPackagingSapCommand, required: false })
  @IsUndefinable()
  @ValidateNested()
  @Type(() => UpdatePickUpRequestPackagingSapCommand)
  packagingRequest?: UpdatePickUpRequestPackagingSapCommand

  @ApiProperty({ type: String, required: false, nullable: true })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  remarks?: string | null

  @ApiProperty({ type: CreateFileLinkCommand, required: false, isArray: true })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateFileLinkCommand)
  @IsArray()
  additionalFiles?: CreateFileLinkCommand[]
}
