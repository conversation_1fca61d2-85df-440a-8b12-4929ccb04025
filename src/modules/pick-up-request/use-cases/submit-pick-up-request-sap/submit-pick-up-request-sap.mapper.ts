import { SapGetIndascanDetailResponse } from '../../../sap/use-cases/get-indascan-detail/get-indascan-detail.response.js'
import { SapUpdatePickUpRequestCommand } from '../../../sap/use-cases/update-pick-up-request/update-pick-up-request.command.js'
import { mapPickUpRequestStatusToSapValue, PickUpRequestStatus } from '../../enums/pick-up-request-status.enum.js'

export class SubmitPickUpRequestSapMapper {
  public static mapSubmitToSapCommand (
    requestNumber: string,
    pickUpRequestDetails: SapGetIndascanDetailResponse[]
  ): SapUpdatePickUpRequestCommand {
    const submittedStatus = mapPickUpRequestStatusToSapValue(PickUpRequestStatus.PENDING)[0]

    const submittedPickUpRequests = pickUpRequestDetails.map(detail => ({
      ...detail,
      QuantityLabels: '0',
      Status: submittedStatus
    }))

    return {
      Reqno: requestNumber,
      ToDisposalRequest: submittedPickUpRequests
    }
  }
}
