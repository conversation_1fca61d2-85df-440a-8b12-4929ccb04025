import { Injectable } from '@nestjs/common'
import dayjs from 'dayjs'
import { SapGetPickUpRequestDetailResponse } from '../../../sap/use-cases/get-pick-up-request-detail/get-pick-up-request-detail.response.js'
import { mapPickUpRequestStatusToSapValue, PickUpRequestStatus } from '../../enums/pick-up-request-status.enum.js'
import { InvalidIndascanSubmitStatusError } from '../../errors/invalid-indascan-submit-status.error.js'
import { PickUpRequestNotFoundError } from '../../errors/pick-up-request-sap-not-found.error.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { UserWasteProducerAuthService } from '../../../auth/services/user-waste-producer-auth.service.js'
import { DateMustBeAfterError } from '../../../exceptions/generic/date-must-be-after.js'
import { MissingRequiredFieldError } from '../../../exceptions/generic/missing-required-field.error.js'
import { SapDateFormatterService } from '../../../sap/services/sap-date-formatter.service.js'

@Injectable()
export class SubmitPickUpRequestSapValidator {
  constructor (
    private readonly authContext: AuthContext,
    private readonly userWasteProducerAuthService: UserWasteProducerAuthService
  ) {}

  async validate (details: SapGetPickUpRequestDetailResponse[]): Promise<void> {
    const indascanStatus = mapPickUpRequestStatusToSapValue(PickUpRequestStatus.INDASCAN_DRAFT)

    const isNotAllIndascanDraft = details.some((detail) => {
      return detail.Status === undefined || !indascanStatus.includes(detail.Status)
    })

    if (isNotAllIndascanDraft) {
      throw new InvalidIndascanSubmitStatusError()
    }

    await this.validatePickUpRequestAccessible(details)
    this.validateDates(details)
  }

  private async validatePickUpRequestAccessible (
    details: SapGetPickUpRequestDetailResponse[]
  ): Promise<void> {
    for (const packagingRequest of details) {
      if (packagingRequest.Kunnr === undefined || packagingRequest.Kunnr === '') {
        continue
      }

      const selectedCustomerId = this.authContext.getSelectedCustomerId()
      if (selectedCustomerId != null && selectedCustomerId !== packagingRequest.Kunnr) {
        throw new PickUpRequestNotFoundError({ requestNumber: packagingRequest.Reqno ?? 'Reqno not given' })
      }

      if (packagingRequest.KunnrY2 === undefined || packagingRequest.KunnrY2 === '') {
        continue
      }

      const userId = this.authContext.getAzureEntraUpn()
      const canUserAccessWasteProducer = await this.userWasteProducerAuthService
        .canUserAccessWasteProducer(
          userId,
          packagingRequest.Kunnr,
          packagingRequest.KunnrY2
        )
      if (!canUserAccessWasteProducer) {
        throw new PickUpRequestNotFoundError({ requestNumber: packagingRequest.Reqno ?? 'Reqno not given' })
      }
    }
  }

  private validateDates (
    details: SapGetPickUpRequestDetailResponse[]
  ): void {
    const tomorrow = dayjs().add(1, 'day')

    details.forEach((detail) => {
      const start = SapDateFormatterService.parseDate(detail.RequestedDate ?? null)
      const end = SapDateFormatterService.parseDate(detail.ReqDateTo ?? null)

      if (start === null) {
        throw new MissingRequiredFieldError({ pointer: `$.startDate` })
      }

      if (end === null) {
        throw new MissingRequiredFieldError({ pointer: `$.endDate` })
      }

      if (!dayjs(start).isSameOrAfter(tomorrow, 'date')) {
        throw new DateMustBeAfterError(dayjs(start).format('YYYY-MM-DD'), { pointer: `$.startDate` })
      }

      if (!dayjs(end).isSameOrAfter(start, 'date')) {
        throw new DateMustBeAfterError(dayjs(end).format('YYYY-MM-DD'), { pointer: `$.endDate` })
      }
    })
  }
}
