import { before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import { expect } from 'expect'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { SubmitPickUpRequestSapMapper } from '../submit-pick-up-request-sap.mapper.js'
import { SapGetIndascanDetailResponseBuilder } from '../../../../sap/use-cases/get-indascan-detail/tests/builders/get-indascan-detail.response.builder.js'

describe('SubmitPickUpRequestSapMapper - Unit Tests', () => {
  before(() => TestBench.setupUnitTest())

  it('maps the details correctly to the sap command where every detail has sap status pending', () => {
    const details = Array.from({ length: 3 }, () =>
      new SapGetIndascanDetailResponseBuilder().build()
    )

    const requestNumber = randomUUID()

    const sapCommand = SubmitPickUpRequestSapMapper.mapSubmitToSapCommand(requestNumber, details)

    expect(sapCommand.Reqno).toBe(requestNumber)
    expect(sapCommand.ToDisposalRequest?.every(i => i.Status === '000'))
  })
})
