import { after, before, describe, it, mock } from 'node:test'
import request from 'supertest'
import { HttpStatus } from '@nestjs/common'
import { expect } from 'expect'
import dayjs from 'dayjs'
import { EndToEndTestSetup } from '../../../../../../test/setup/end-to-end-test-setup.js'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { Permission } from '../../../../permission/permission.enum.js'
import { SapGetIndascanDetailResponseBuilder } from '../../../../sap/use-cases/get-indascan-detail/tests/builders/get-indascan-detail.response.builder.js'
import { SapGetIndascanDetailUseCase } from '../../../../sap/use-cases/get-indascan-detail/get-indascan-detail.use-case.js'

describe('SubmitPickUpRequestSap - E2E tests', () => {
  let setup: EndToEndTestSetup

  before(async () => {
    setup = await TestBench.setupEndToEndTest()
  })

  after(async () => await setup.teardown())

  it('submits the sap pickup request', async () => {
    const user = await setup.authContext.getUser([Permission.PICK_UP_REQUEST_MANAGE])

    const pickUpRequestDetailMock = mock.method(SapGetIndascanDetailUseCase.prototype, 'execute', () => {
      return [new SapGetIndascanDetailResponseBuilder()
        .withStatus('004')
        .withRequestedDate(dayjs().add(1, 'day').toDate())
        .withReqDateTo((dayjs().add(1, 'week').toDate()))
        .build()]
    })

    const response = await request(setup.httpServer)
      .post(`/pick-up-requests/sap/:requestNumber/submit`)
      .set('Authorization', `Bearer ${user.token}`)

    pickUpRequestDetailMock.mock.restore()

    expect(response).toHaveStatus(HttpStatus.OK)
  })
})
