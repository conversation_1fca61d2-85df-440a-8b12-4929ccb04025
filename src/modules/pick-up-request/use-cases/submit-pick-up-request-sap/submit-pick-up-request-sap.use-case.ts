import { Injectable } from '@nestjs/common'
import { SapUpdatePickUpRequestUseCase } from '../../../sap/use-cases/update-pick-up-request/update-pick-up-request.use-case.js'
import { SapQuery } from '../../../sap/query/sap-query.js'
import { SapGetIndascanDetailUseCase } from '../../../sap/use-cases/get-indascan-detail/get-indascan-detail.use-case.js'
import { SapGetIndascanDetailResponse } from '../../../sap/use-cases/get-indascan-detail/get-indascan-detail.response.js'
import { SubmitPickUpRequestSapMapper } from './submit-pick-up-request-sap.mapper.js'
import { SubmitPickUpRequestSapValidator } from './submit-pick-up-request-sap.validator.js'

@Injectable()
export class SubmitPickUpRequestSapUseCase {
  constructor (
    private readonly sapGetIndascanDetailUseCase: SapGetIndascanDetailUseCase,
    private readonly sapUpdatePickUpRequestUseCase: SapUpdatePickUpRequestUseCase,
    private readonly validator: SubmitPickUpRequestSapValidator
  ) {}

  async execute (
    requestNumber: string
  ): Promise<void> {
    const details = await this.getPickUpRequestDetails(requestNumber)

    await this.validator.validate(details)

    const sapCommand = SubmitPickUpRequestSapMapper.mapSubmitToSapCommand(requestNumber, details)

    await this.sapUpdatePickUpRequestUseCase.execute(sapCommand)
  }

  private async getPickUpRequestDetails (
    requestNumber: string
  ): Promise<SapGetIndascanDetailResponse[]> {
    const sapQuery = new SapQuery<SapGetIndascanDetailResponse>()
      .where('Reqno', requestNumber)

    return this.sapGetIndascanDetailUseCase.execute(sapQuery)
  }
}
