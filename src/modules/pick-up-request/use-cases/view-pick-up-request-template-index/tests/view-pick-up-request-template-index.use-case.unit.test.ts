import { afterEach, before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import { SinonStubbedInstance, createStubInstance } from 'sinon'
import { expect } from 'expect'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { AuthContext } from '../../../../auth/auth.context.js'
import { ViewPickUpRequestTemplateIndexUseCase } from '../view-pick-up-request-template-index.use-case.js'
import { ViewPickUpRequestTemplateIndexRepository } from '../view-pick-up-request-template-index.repository.js'
import { UserWasteProducerAuthService } from '../../../../auth/services/user-waste-producer-auth.service.js'
import { ViewPickUpRequestTemplateIndexQueryBuilder } from './view-pick-up-request-template-index-query.builder.js'

describe('ViewPickUpRequestTemplateIndexUseCase - unit test', () => {
  let useCase: ViewPickUpRequestTemplateIndexUseCase

  let userUuid: string

  let authContext: SinonStubbedInstance<AuthContext>
  let repository: SinonStubbedInstance<ViewPickUpRequestTemplateIndexRepository>
  let userWasteProducerAuthService: SinonStubbedInstance<UserWasteProducerAuthService>

  before(() => {
    TestBench.setupUnitTest()

    userUuid = randomUUID()

    authContext = createStubInstance(AuthContext)
    repository = createStubInstance(ViewPickUpRequestTemplateIndexRepository)
    userWasteProducerAuthService = createStubInstance(UserWasteProducerAuthService)

    useCase = new ViewPickUpRequestTemplateIndexUseCase(
      authContext,
      repository,
      userWasteProducerAuthService
    )

    mockMethods()
  })

  afterEach(() => {
    mockMethods()
  })

  function mockMethods () {
    repository.findPaginated.resetHistory()

    authContext.isInternalUser.returns(true)
    authContext.getSelectedCustomerId.returns(null)
    authContext.getUserUuidOrFail.returns(userUuid)
    authContext.getAzureEntraUpn.returns(randomUUID())
    repository.findPaginated.resolves([[], 0])
    userWasteProducerAuthService.getRestrictedWasteProducerIds.resolves(undefined)
  }

  it('should call the repository without filters when no customer given', async () => {
    const query = new ViewPickUpRequestTemplateIndexQueryBuilder().build()

    await useCase.execute(query)

    expect(repository.findPaginated.calledWith(query)).toBe(true)
    expect(userWasteProducerAuthService.getRestrictedWasteProducerIds.notCalled).toBe(true)
  })

  it('should call the repository with filters when customer given', async () => {
    authContext.isInternalUser.returns(false)
    const customerId = randomUUID()
    authContext.getSelectedCustomerId.returns(customerId)

    const query = new ViewPickUpRequestTemplateIndexQueryBuilder().build()

    await useCase.execute(query)

    expect(repository.findPaginated.calledWith(
      query, userUuid, customerId, undefined
    )).toBe(true)
    expect(userWasteProducerAuthService.getRestrictedWasteProducerIds.calledOnce).toBe(true)
  })
})
