import { Body, Controller, HttpCode, Param, Post, Res } from '@nestjs/common'
import { ApiOAuth2, ApiOkResponse, ApiTags } from '@nestjs/swagger'
import { Response } from 'express'
import { Permissions } from '../../../permission/permission.decorator.js'
import { Permission } from '../../../permission/permission.enum.js'
import { ApiNotFoundErrorResponse } from '../../../exceptions/api-errors/api-error-response.decorator.js'
import { GlobalCustomerRequired } from '../../../auth/decorators/no-customer-required.decorator.js'
import { FileNotFoundError } from '../../../files/errors/file.not-found.error.js'
import { DownloadDocumentSubmittedPickUpRequestUseCase } from './download-document-submitted-pick-up-request.use-case.js'
import { DownloadDocumentSubmittedPickUpRequestCommand } from './download-document-submitted-pick-up-request.command.js'

@ApiTags('Pick-up request')
@Controller('pick-up-requests/sap/:requestNumber/documents/download')
@ApiOAuth2([])
export class DownloadDocumentSubmittedPickUpRequestController {
  constructor (
    private readonly useCase: DownloadDocumentSubmittedPickUpRequestUseCase
  ) {}

  @Post()
  @GlobalCustomerRequired()
  @HttpCode(200)
  @Permissions(Permission.PICK_UP_REQUEST_MANAGE, Permission.PICK_UP_REQUEST_READ)
  @ApiOkResponse()
  @ApiNotFoundErrorResponse(FileNotFoundError)
  async downloadDocumentSubmittedPickUpRequest (
    @Res() response: Response,
    @Param('requestNumber') requestNumber: string,
    @Body() command: DownloadDocumentSubmittedPickUpRequestCommand
  ): Promise<void> {
    return this.useCase.execute(response, requestNumber, command)
  }
}
