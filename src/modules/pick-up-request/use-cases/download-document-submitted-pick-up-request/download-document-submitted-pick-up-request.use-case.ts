import { HttpStatus, Injectable } from '@nestjs/common'
import { AxiosError } from 'axios'
import { Response } from 'express'
import { SharepointDownloadDocumentUseCase } from '../../../sharepoint/use-cases/download-document/download-document.use-case.js'
import { streamFileResponse } from '../../../../utils/streams/stream-file-response.js'
import { SharepointCommandBuilder } from '../../../sharepoint/builders/sharepoint-command.builder.js'
import { SHAREPOINT_LIBRARY } from '../../../sharepoint/constants/sharepoint-library.constant.js'
import { DEFAULT_SHAREPOINT_SITE_TITLE } from '../../../sharepoint/constants/sharepoint-site-title.constant.js'
import { SharepointLibraryNameEnvMapper } from '../../../sharepoint/mappers/sharepoint-library.mapper.js'
import { FileNotFoundError } from '../../../files/errors/file.not-found.error.js'
import { DownloadDocumentSubmittedPickUpRequestCommand } from './download-document-submitted-pick-up-request.command.js'

@Injectable()
export class DownloadDocumentSubmittedPickUpRequestUseCase {
  constructor (
    private sharepointDownloadUseCase: SharepointDownloadDocumentUseCase
  ) {}

  async execute (
    response: Response,
    requestNumber: string,
    command: DownloadDocumentSubmittedPickUpRequestCommand
  ): Promise<void> {
    const libraryName = SharepointLibraryNameEnvMapper
      .toSharepointLibraryName(SHAREPOINT_LIBRARY.PICK_UP_REQUEST)

    const fileName = `${requestNumber.padStart(10, '0')}_${command.fileName}`

    const sharepointCommand = new SharepointCommandBuilder()
      .addFileName(fileName)
      .addLibraryName(libraryName)
      .addSiteTitle(DEFAULT_SHAREPOINT_SITE_TITLE)
      .build()

    try {
      const result = await this.sharepointDownloadUseCase.execute(sharepointCommand)
      return streamFileResponse(
        result.buffer,
        result.mimeType,
        result.fileName,
        response
      )
    } catch (error) {
      const axiosError = error as AxiosError

      if (axiosError.status === HttpStatus.NOT_FOUND) {
        throw new FileNotFoundError(fileName)
      }

      throw error
    }
  }
}
