import { randomUUID } from 'crypto'
import { DownloadDocumentSubmittedPickUpRequestCommand } from '../../download-document-submitted-pick-up-request.command.js'

export class DownloadDocumentSubmittedPickUpRequestCommandBuilder {
  private command: DownloadDocumentSubmittedPickUpRequestCommand

  constructor () {
    this.command = new DownloadDocumentSubmittedPickUpRequestCommand()
    this.command.fileName = randomUUID()
  }

  withFileName (fileName: string): this {
    this.command.fileName = fileName
    return this
  }

  build (): DownloadDocumentSubmittedPickUpRequestCommand {
    return this.command
  }
}
