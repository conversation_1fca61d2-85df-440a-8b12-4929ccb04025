import { after, before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import request from 'supertest'
import { expect } from 'expect'
import { HttpStatus } from '@nestjs/common'
import { EndToEndTestSetup } from '../../../../../../test/setup/end-to-end-test-setup.js'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { TestUser } from '../../../../../app/users/tests/setup-user.type.js'
import { Permission } from '../../../../permission/permission.enum.js'
import { DownloadDocumentSubmittedPickUpRequestCommandBuilder } from './builders/download-document-submitted-pick-up-request.command.builder.js'

describe('DownloadDocumentSubmittedPickUpRequest - E2E Tests', () => {
  let setup: EndToEndTestSetup
  let user: TestUser

  before(async () => {
    setup = await TestBench.setupEndToEndTest()
    user = await setup.authContext.getUser([Permission.PICK_UP_REQUEST_MANAGE])
  })

  after(async () => await setup.teardown())

  it('returns 200', async () => {
    const command = new DownloadDocumentSubmittedPickUpRequestCommandBuilder().build()
    const requestNumber = randomUUID()
    const response = await request(setup.httpServer)
      .post(`/pick-up-requests/sap/${requestNumber}/documents/download`)
      .set('Authorization', `Bearer ${user.token}`)
      .send(command)

    expect(response).toHaveStatus(HttpStatus.OK)
  })
})
