import { Module } from '@nestjs/common'
import { SharepointModule } from '../../../sharepoint/sharepoint.module.js'
import { DownloadDocumentSubmittedPickUpRequestController } from './download-document-submitted-pick-up-request.controller.js'
import { DownloadDocumentSubmittedPickUpRequestUseCase } from './download-document-submitted-pick-up-request.use-case.js'

@Module({
  imports: [
    SharepointModule
  ],
  controllers: [
    DownloadDocumentSubmittedPickUpRequestController
  ],
  providers: [
    DownloadDocumentSubmittedPickUpRequestUseCase
  ]
})
export class DownloadDocumentSubmittedPickUpRequestModule {}
