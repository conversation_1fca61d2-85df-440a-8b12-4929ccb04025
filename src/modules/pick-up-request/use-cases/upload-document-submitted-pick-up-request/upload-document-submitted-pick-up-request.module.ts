import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { File } from '../../../files/entities/file.entity.js'
import { SapModule } from '../../../sap/sap.module.js'
import { UploadDocumentSubmittedPickUpRequestController } from './upload-document-submitted-pick-up-request.controller.js'
import { UploadDocumentSubmittedPickUpRequestUseCase } from './upload-document-submitted-pick-up-request.use-case.js'
import { UploadDocumentSubmittedPickUpRequestValidator } from './upload-document-submitted-pick-up-request.validator.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([File]),
    SapModule
  ],
  controllers: [UploadDocumentSubmittedPickUpRequestController],
  providers: [
    UploadDocumentSubmittedPickUpRequestUseCase,
    UploadDocumentSubmittedPickUpRequestValidator
  ],
  exports: [UploadDocumentSubmittedPickUpRequestUseCase]
})
export class UploadDocumentSubmittedPickUpRequestModule {}
