import { ApiProperty } from '@nestjs/swagger'
import { IsArray, ArrayUnique, ArrayMinSize, IsUUID, IsEnum, ValidateNested } from 'class-validator'
import { Type } from 'class-transformer'
import { EntityPart } from '../../../files/enums/entity-part.enum.js'

export class UploadDocumentCommand {
  @ApiProperty({ type: String, format: 'uuid' })
  @IsUUID()
  fileUuid: string

  @ApiProperty({ type: String, enum: EntityPart, enumName: 'EntityPart' })
  @IsEnum(EntityPart)
  entityPart: EntityPart
}

export class UploadDocumentSubmittedPickUpRequestCommand {
  @ApiProperty({ type: UploadDocumentCommand, isArray: true })
  @ValidateNested({ each: true })
  @IsArray()
  @Type(() => UploadDocumentCommand)
  @ArrayUnique()
  @ArrayMinSize(1)
  documents: UploadDocumentCommand[]
}
