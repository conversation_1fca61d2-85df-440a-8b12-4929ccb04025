import { Body, Controller, HttpCode, Param, Post } from '@nestjs/common'
import { ApiOAuth2, ApiOkResponse, ApiTags } from '@nestjs/swagger'
import { Permissions } from '../../../permission/permission.decorator.js'
import { Permission } from '../../../permission/permission.enum.js'
import { NotFoundError } from '../../../exceptions/generic/not-found.error.js'
import { ApiNotFoundErrorResponse } from '../../../exceptions/api-errors/api-error-response.decorator.js'
import { GlobalCustomerRequired } from '../../../auth/decorators/no-customer-required.decorator.js'
import { FileNotFoundError } from '../../../files/errors/file.not-found.error.js'
import { UploadDocumentSubmittedPickUpRequestCommand } from './upload-document-submitted-pick-up-request.command.js'
import { UploadDocumentSubmittedPickUpRequestUseCase } from './upload-document-submitted-pick-up-request.use-case.js'

@ApiTags('Pick-up request')
@Controller('pick-up-requests/:requestNumber/upload-documents')
@ApiOAuth2([])
export class UploadDocumentSubmittedPickUpRequestController {
  constructor (
    private readonly useCase: UploadDocumentSubmittedPickUpRequestUseCase
  ) {}

  @Post()
  @GlobalCustomerRequired()
  @HttpCode(200)
  @Permissions(Permission.PICK_UP_REQUEST_MANAGE)
  @ApiOkResponse({
    description: 'Documents uploaded to the submitted pick-up request'
  })
  @ApiNotFoundErrorResponse(NotFoundError, FileNotFoundError)
  async uploadDocumentSubmittedPickUpRequest (
    @Param('requestNumber') requestNumber: string,
    @Body() command: UploadDocumentSubmittedPickUpRequestCommand
  ): Promise<void> {
    return await this.useCase.execute(requestNumber, command)
  }
}
