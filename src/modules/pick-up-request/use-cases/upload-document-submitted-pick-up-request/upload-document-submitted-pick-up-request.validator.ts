import { Injectable } from '@nestjs/common'
import { AuthContext } from '../../../auth/auth.context.js'
import { UserWasteProducerAuthService } from '../../../auth/services/user-waste-producer-auth.service.js'
import { InvalidEntityPartError } from '../../../files/errors/invalid-entity-part.error.js'
import { EntityPart } from '../../../files/enums/entity-part.enum.js'
import { WasteProducerNotAccessibleError } from '../../../waste-producer/errors/waste-producer-not-accessible.error.js'
import { CustomerNotProvidedError } from '../../../customer/errors/customer-not-provided.error.js'
import { UploadDocumentCommand } from './upload-document-submitted-pick-up-request.command.js'

interface PickUpRequestOptions {
  requestNumber?: string
  customerId?: string
  wasteProducerId?: string
}

@Injectable()
export class UploadDocumentSubmittedPickUpRequestValidator {
  constructor (
    private readonly authContext: AuthContext,
    private readonly userWasteProducerAuthService: UserWasteProducerAuthService
  ) {}

  async validate (
    commands: UploadDocumentCommand[],
    options: PickUpRequestOptions
  ): Promise<void> {
    this.validateDocuments(commands)
    await this.validatePickUpRequestAccessible(options)
  }

  private validateDocuments (
    commands: UploadDocumentCommand[]
  ): void {
    commands.map((command, index) => {
      if (command.entityPart !== EntityPart.ADDITIONAL) {
        throw new InvalidEntityPartError({ pointer: `$.command[${index}]` })
      }
    })
  }

  private async validatePickUpRequestAccessible (
    options: PickUpRequestOptions
  ): Promise<void> {
    const { customerId, wasteProducerId } = options

    if (customerId === undefined || customerId === '') {
      return
    }

    const selectedCustomerId = this.authContext.getSelectedCustomerId()
    if (selectedCustomerId != null && selectedCustomerId !== customerId) {
      throw new CustomerNotProvidedError()
    }
    if (wasteProducerId === undefined || wasteProducerId === '') {
      return
    }

    const userId = this.authContext.getAzureEntraUpn()

    const canUserAccessWasteProducer = await this.userWasteProducerAuthService
      .canUserAccessWasteProducer(
        userId,
        customerId,
        wasteProducerId
      )

    if (!canUserAccessWasteProducer) {
      throw new WasteProducerNotAccessibleError()
    }
  }
}
