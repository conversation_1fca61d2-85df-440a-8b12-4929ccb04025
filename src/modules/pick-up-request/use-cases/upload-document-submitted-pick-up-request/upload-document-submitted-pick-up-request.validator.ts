import { Injectable } from '@nestjs/common'
import { AuthContext } from '../../../auth/auth.context.js'
import { UserWasteProducerAuthService } from '../../../auth/services/user-waste-producer-auth.service.js'
import { WasteProducerNotAccessibleError } from '../../../waste-producer/errors/waste-producer-not-accessible.error.js'
import { CustomerNotProvidedError } from '../../../customer/errors/customer-not-provided.error.js'

interface PickUpRequestOptions {
  requestNumber?: string
  customerId?: string
  wasteProducerId?: string
}

@Injectable()
export class UploadDocumentSubmittedPickUpRequestValidator {
  constructor (
    private readonly authContext: AuthContext,
    private readonly userWasteProducerAuthService: UserWasteProducerAuthService
  ) {}

  public async validatePickUpRequestAccessible (
    options: PickUpRequestOptions
  ): Promise<void> {
    const { customerId, wasteProducerId } = options

    if (customerId === undefined || customerId === '') {
      return
    }

    const selectedCustomerId = this.authContext.getSelectedCustomerId()
    if (selectedCustomerId != null && selectedCustomerId !== customerId) {
      throw new CustomerNotProvidedError()
    }
    if (wasteProducerId === undefined || wasteProducerId === '') {
      return
    }

    const userId = this.authContext.getAzureEntraUpn()

    const canUserAccessWasteProducer = await this.userWasteProducerAuthService
      .canUserAccessWasteProducer(
        userId,
        customerId,
        wasteProducerId
      )

    if (!canUserAccessWasteProducer) {
      throw new WasteProducerNotAccessibleError()
    }
  }
}
