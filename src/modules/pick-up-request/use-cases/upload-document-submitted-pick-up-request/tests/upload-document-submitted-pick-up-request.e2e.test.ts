import { after, before, describe, it, mock } from 'node:test'
import { randomUUID } from 'crypto'
import request from 'supertest'
import { Repository } from 'typeorm'
import { expect } from 'expect'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { EndToEndTestSetup } from '../../../../../../test/setup/end-to-end-test-setup.js'
import { Permission } from '../../../../permission/permission.enum.js'
import { TestUser } from '../../../../../app/users/tests/setup-user.type.js'
import { File } from '../../../../files/entities/file.entity.js'
import { SapGetPickUpRequestDetailUseCase } from '../../../../sap/use-cases/get-pick-up-request-detail/get-pick-up-request-detail.use-case.js'
import { FileEntityBuilder } from '../../../../files/entities/file-entity.builder.js'
import { SapGetPickUpRequestDetailResponseBuilder } from '../../../../sap/use-cases/get-pick-up-request-detail/tests/get-pick-up-request-detail.response.builder.js'
import { UploadPickUpRequestDocumentUseCase } from '../../upload-pick-up-request-document/upload-pick-up-request-document.use-case.js'
import { UploadDocumentSubmittedPickUpRequestCommandBuilder } from './upload-document-submitted-pick-up-request.command.builder.js'

describe('Upload document submitted pick-up request e2e test', () => {
  let setup: EndToEndTestSetup

  let fileRepository: Repository<File>

  let unauthorizedUser: TestUser
  let authorizedUser: TestUser

  before(async () => {
    setup = await TestBench.setupEndToEndTest()

    fileRepository = setup.dataSource.getRepository(File)

    const [_unauthorizedUser, _authorizedUser] = await Promise.all([
      await setup.authContext.getUser([]),
      await setup.authContext.getUser([Permission.PICK_UP_REQUEST_MANAGE])
    ])

    unauthorizedUser = _unauthorizedUser
    authorizedUser = _authorizedUser
  })

  after(async () => await setup.teardown())

  it('returns 401 when not authenticated', async () => {
    const response = await request(setup.httpServer)
      .post(`/pick-up-requests/sap/${randomUUID()}/documents/upload`)

    expect(response).toHaveStatus(401)
  })

  it('returns 403 when unauthorized', async () => {
    const response = await request(setup.httpServer)
      .post(`/pick-up-requests/sap/${randomUUID()}/documents/upload`)
      .set('Authorization', `Bearer ${unauthorizedUser.token}`)

    expect(response).toHaveStatus(403)
  })

  it('returns 200 when authorized and file exists', async () => {
    const file = new FileEntityBuilder().build()
    await fileRepository.insert(file)

    const requestNumber = randomUUID()

    mock.method(SapGetPickUpRequestDetailUseCase.prototype, 'execute', () => {
      return [new SapGetPickUpRequestDetailResponseBuilder()
        .withRequestNumber(requestNumber).build()]
    })

    const uploadMock = mock.method(UploadPickUpRequestDocumentUseCase.prototype, 'execute', () => { })

    const command = new UploadDocumentSubmittedPickUpRequestCommandBuilder()
      .withFileUuids([file.uuid])
      .build()

    const response = await request(setup.httpServer)
      .post(`/pick-up-requests/sap/${requestNumber}/documents/upload`)
      .set('Authorization', `Bearer ${authorizedUser.token}`)
      .send(command)

    uploadMock.mock.restore()

    expect(response).toHaveStatus(200)
  })
})
