import { randomUUID } from 'crypto'
import { UploadDocumentSubmittedPickUpRequestCommand } from '../upload-document-submitted-pick-up-request.command.js'

export class UploadDocumentSubmittedPickUpRequestCommandBuilder {
  private command: UploadDocumentSubmittedPickUpRequestCommand

  constructor () {
    this.reset()
  }

  reset (): this {
    this.command = new UploadDocumentSubmittedPickUpRequestCommand()
    this.command.fileUuids = [randomUUID()]

    return this
  }

  withFileUuids (fileUuids: string[]): this {
    this.command.fileUuids = fileUuids
    return this
  }

  build (): UploadDocumentSubmittedPickUpRequestCommand {
    const command = this.command
    this.reset()
    return command
  }
}
