import { UploadDocumentCommand, UploadDocumentSubmittedPickUpRequestCommand } from '../upload-document-submitted-pick-up-request.command.js'
import { EntityPart } from '../../../../files/enums/entity-part.enum.js'

export class UploadDocumentSubmittedPickUpRequestCommandBuilder {
  private command: UploadDocumentSubmittedPickUpRequestCommand

  constructor () {
    this.reset()
  }

  reset (): this {
    this.command = new UploadDocumentSubmittedPickUpRequestCommand()
    this.command.documents = []

    return this
  }

  addUploadDocumentCommand (fileUuid: string, entityPart: EntityPart): this {
    const uploadDocumentCommand = new UploadDocumentCommand()
    uploadDocumentCommand.fileUuid = fileUuid
    uploadDocumentCommand.entityPart = entityPart

    this.command.documents.push(uploadDocumentCommand)

    return this
  }

  build (): UploadDocumentSubmittedPickUpRequestCommand {
    const command = this.command
    this.reset()
    return command
  }
}
