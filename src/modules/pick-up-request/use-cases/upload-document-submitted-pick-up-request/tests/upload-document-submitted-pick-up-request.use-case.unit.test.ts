import { before, describe, it, afterEach } from 'node:test'
import { randomUUID } from 'crypto'
import { Repository } from 'typeorm'
import { expect } from 'expect'
import { assert, createStubInstance, SinonStubbedInstance } from 'sinon'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { UploadDocumentSubmittedPickUpRequestUseCase } from '../upload-document-submitted-pick-up-request.use-case.js'
import { UploadDocumentSubmittedPickUpRequestValidator } from '../upload-document-submitted-pick-up-request.validator.js'
import { SapGetPickUpRequestDetailUseCase } from '../../../../../modules/sap/use-cases/get-pick-up-request-detail/get-pick-up-request-detail.use-case.js'
import { FileEntityBuilder } from '../../../../files/entities/file-entity.builder.js'
import { File } from '../../../../files/entities/file.entity.js'
import { SapGetPickUpRequestDetailResponseBuilder } from '../../../../sap/use-cases/get-pick-up-request-detail/tests/get-pick-up-request-detail.response.builder.js'
import { FileNotFoundError } from '../../../../files/errors/file.not-found.error.js'
import { PickUpRequestNotFoundError } from '../../../errors/pick-up-request-sap-not-found.error.js'
import { UploadPickUpRequestDocumentUseCase } from '../../upload-pick-up-request-document/upload-pick-up-request-document.use-case.js'
import { UploadDocumentSubmittedPickUpRequestCommandBuilder } from './upload-document-submitted-pick-up-request.command.builder.js'

describe('Upload document submitted pick-up request use-case unit test', () => {
  let useCase: UploadDocumentSubmittedPickUpRequestUseCase

  let validator: SinonStubbedInstance<UploadDocumentSubmittedPickUpRequestValidator>
  let uploadDocumentUseCase: SinonStubbedInstance<UploadPickUpRequestDocumentUseCase>
  let sapGetPickUpRequestDetail: SinonStubbedInstance<SapGetPickUpRequestDetailUseCase>
  let fileRepository: SinonStubbedInstance<Repository<File>>

  let file: File

  before(() => {
    TestBench.setupUnitTest()

    file = new FileEntityBuilder().build()

    validator = createStubInstance(UploadDocumentSubmittedPickUpRequestValidator, {
      validatePickUpRequestAccessible: Promise.resolve()
    })

    uploadDocumentUseCase = createStubInstance(UploadPickUpRequestDocumentUseCase, {
      execute: Promise.resolve()
    })

    sapGetPickUpRequestDetail = createStubInstance(SapGetPickUpRequestDetailUseCase)
    fileRepository = createStubInstance<Repository<File>>(Repository<File>)

    useCase = new UploadDocumentSubmittedPickUpRequestUseCase(
      fileRepository,
      validator,
      sapGetPickUpRequestDetail,
      uploadDocumentUseCase
    )

    mockMethods()
  })

  afterEach(() => {
    mockMethods()
  })

  function mockMethods () {
    sapGetPickUpRequestDetail.execute.resolves([
      new SapGetPickUpRequestDetailResponseBuilder().build()
    ])
    fileRepository.findBy.resolves([file])
  }

  it('Calls all methods', async () => {
    const command = new UploadDocumentSubmittedPickUpRequestCommandBuilder()
      .withFileUuids([file.uuid])
      .build()

    const requestNumber = randomUUID()

    sapGetPickUpRequestDetail.execute.resolves([
      new SapGetPickUpRequestDetailResponseBuilder()
        .withRequestNumber(requestNumber)
        .build()
    ])

    await useCase.execute(requestNumber, command)

    assert.calledOnce(sapGetPickUpRequestDetail.execute)
    assert.calledOnce(uploadDocumentUseCase.execute)
    assert.calledOnce(fileRepository.findBy)
    assert.calledOnce(validator.validatePickUpRequestAccessible)
  })

  it('Throws error when pick-up request is not found', async () => {
    const command = new UploadDocumentSubmittedPickUpRequestCommandBuilder()
      .withFileUuids([file.uuid])
      .build()

    const requestNumber = randomUUID()

    sapGetPickUpRequestDetail.execute.resolves([])

    await expect(useCase.execute(requestNumber, command))
      .rejects
      .toThrow(PickUpRequestNotFoundError)
  })

  it('Throws error when file is not found', async () => {
    const command = new UploadDocumentSubmittedPickUpRequestCommandBuilder()
      .withFileUuids([file.uuid])
      .build()

    const requestNumber = randomUUID()

    sapGetPickUpRequestDetail.execute.resolves([
      new SapGetPickUpRequestDetailResponseBuilder()
        .withRequestNumber(requestNumber)
        .build()
    ])

    fileRepository.findBy.resolves([])

    await expect(useCase.execute(requestNumber, command))
      .rejects
      .toThrow(FileNotFoundError)
  })
})
