import assert from 'assert'
import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { Any, Repository } from 'typeorm'
import { SapUploadAndCreateDocumentUseCase } from '../../../sap/use-cases/upload-and-create-document/upload-and-create-document.use-case.js'
import { File } from '../../../files/entities/file.entity.js'
import { FileNotFoundError } from '../../../files/errors/file.not-found.error.js'
import { SapGetPickUpRequestDetailUseCase } from '../../../sap/use-cases/get-pick-up-request-detail/get-pick-up-request-detail.use-case.js'
import { SapQuery } from '../../../sap/query/sap-query.js'
import { SapGetPickUpRequestDetailResponse } from '../../../sap/use-cases/get-pick-up-request-detail/get-pick-up-request-detail.response.js'
import { PickUpRequestNotFoundError } from '../../errors/pick-up-request-sap-not-found.error.js'
import { UploadDocumentSubmittedPickUpRequestCommand } from './upload-document-submitted-pick-up-request.command.js'
import { UploadDocumentSubmittedPickUpRequestValidator } from './upload-document-submitted-pick-up-request.validator.js'

@Injectable()
export class UploadDocumentSubmittedPickUpRequestUseCase {
  constructor (
    @InjectRepository(File)
    private readonly fileRepository: Repository<File>,
    private readonly validator: UploadDocumentSubmittedPickUpRequestValidator,
    private readonly sapUploadAndCreateDocument: SapUploadAndCreateDocumentUseCase,
    private readonly sapGetPickUpRequestDetail: SapGetPickUpRequestDetailUseCase
  ) {}

  async execute (
    requestNumber: string,
    command: UploadDocumentSubmittedPickUpRequestCommand
  ): Promise<void> {
    const sapDetailResponse = await this.sapGetPickUpRequestDetail.execute(
      this.getPickUpRequestDetailSapQuery(requestNumber)
    )

    if (sapDetailResponse.length === 0) {
      throw new PickUpRequestNotFoundError({ requestNumber })
    }

    const options = {
      requestNumber: sapDetailResponse[0].Reqno,
      customerId: sapDetailResponse[0].Kunnr,
      wasteProducerId: sapDetailResponse[0].KunnrY2
    }

    await this.validator.validate(
      command.documents,
      options
    )

    const fileUuids = command.documents.map(document => document.fileUuid)

    const files = await this.validateAndLoadFiles(fileUuids)

    await Promise.all(
      command.documents.map(async (document) => {
        const file = files.find(file => file.uuid === document.fileUuid)

        assert(file !== undefined)

        await this.sapUploadAndCreateDocument.execute(
          requestNumber,
          file,
          document.entityPart
        )
      })
    )
  }

  private async validateAndLoadFiles (
    fileUuids: string[]
  ): Promise<File[]> {
    const files = await this.fileRepository.find({
      where: {
        uuid: Any(fileUuids)
      }
    })

    if (files.length !== fileUuids.length) {
      const foundFileUuids = files.map(file => file.uuid)
      const missingFileUuids = fileUuids.filter(
        uuid => !foundFileUuids.includes(uuid)
      )

      throw new FileNotFoundError(missingFileUuids)
    }

    return files
  }

  private getPickUpRequestDetailSapQuery (
    requestNumber: string
  ): SapQuery<SapGetPickUpRequestDetailResponse> {
    const sapQuery = new SapQuery<SapGetPickUpRequestDetailResponse>()
      .where('Reqno', requestNumber)
      .setTop(1)

    return sapQuery
  }
}
