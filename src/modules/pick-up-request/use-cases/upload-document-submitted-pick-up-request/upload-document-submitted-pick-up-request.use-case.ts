import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { Any, Repository } from 'typeorm'
import { File } from '../../../files/entities/file.entity.js'
import { FileNotFoundError } from '../../../files/errors/file.not-found.error.js'
import { SapGetPickUpRequestDetailUseCase } from '../../../sap/use-cases/get-pick-up-request-detail/get-pick-up-request-detail.use-case.js'
import { SapQuery } from '../../../sap/query/sap-query.js'
import { SapGetPickUpRequestDetailResponse } from '../../../sap/use-cases/get-pick-up-request-detail/get-pick-up-request-detail.response.js'
import { PickUpRequestNotFoundError } from '../../errors/pick-up-request-sap-not-found.error.js'
import { UploadPickUpRequestDocumentUseCase } from '../upload-pick-up-request-document/upload-pick-up-request-document.use-case.js'
import { UploadDocumentSubmittedPickUpRequestCommand } from './upload-document-submitted-pick-up-request.command.js'
import { UploadDocumentSubmittedPickUpRequestValidator } from './upload-document-submitted-pick-up-request.validator.js'

@Injectable()
export class UploadDocumentSubmittedPickUpRequestUseCase {
  constructor (
    @InjectRepository(File)
    private readonly fileRepository: Repository<File>,
    private readonly validator: UploadDocumentSubmittedPickUpRequestValidator,
    private readonly sapGetPickUpRequestDetail: SapGetPickUpRequestDetailUseCase,
    private readonly uploadUseCase: UploadPickUpRequestDocumentUseCase
  ) {}

  async execute (
    requestNumber: string,
    command: UploadDocumentSubmittedPickUpRequestCommand
  ): Promise<void> {
    const sapDetailResponse = await this.sapGetPickUpRequestDetail.execute(
      this.getPickUpRequestDetailSapQuery(requestNumber)
    )

    if (sapDetailResponse.length === 0) {
      throw new PickUpRequestNotFoundError({ requestNumber })
    }

    await this.validator.validatePickUpRequestAccessible({
      requestNumber: sapDetailResponse[0].Reqno,
      customerId: sapDetailResponse[0].Kunnr,
      wasteProducerId: sapDetailResponse[0].KunnrY2
    })

    const files = await this.validateAndLoadFiles(command.fileUuids)

    await this.uploadUseCase.execute(requestNumber, files)
  }

  private async validateAndLoadFiles (
    fileUuids: string[]
  ): Promise<File[]> {
    const files = await this.fileRepository.findBy({ uuid: Any(fileUuids) })

    if (files.length !== fileUuids.length) {
      const foundFileUuids = files.map(file => file.uuid)
      const missingFileUuids = fileUuids.filter(
        uuid => !foundFileUuids.includes(uuid)
      )

      throw new FileNotFoundError(missingFileUuids)
    }

    return files
  }

  private getPickUpRequestDetailSapQuery (
    requestNumber: string
  ): SapQuery<SapGetPickUpRequestDetailResponse> {
    const sapQuery = new SapQuery<SapGetPickUpRequestDetailResponse>()
      .where('Reqno', requestNumber)
      .setTop(1)

    return sapQuery
  }
}
