import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { Repository } from 'typeorm'
import { PickUpRequest } from '../../entities/pick-up-request.entity.js'
import { SubmitPickUpRequestService } from '../../services/submit-pick-up-request.service.js'
import { EntityPart } from '../../../files/enums/entity-part.enum.js'
import { FileLinkService } from '../../../files/services/file-link.service.js'

@Injectable()
export class CreatePickUpRequestSapUseCase {
  constructor (
    @InjectRepository(PickUpRequest)
    private readonly pickUpRequestRepository: Repository<PickUpRequest>,
    private readonly fileLinkService: FileLinkService,
    private readonly submitPickUpRequestService: SubmitPickUpRequestService
  ) {}

  async execute (
    pickUpRequestUuid: string
  ): Promise<string> {
    const pickUpRequest = await this.pickUpRequestRepository.findOneOrFail({
      where: {
        uuid: pickUpRequestUuid
      },
      relations: {
        createdByUser: true
      }
    })

    await this.loadFiles(pickUpRequest)

    return this.submitPickUpRequestService.execute(pickUpRequest)
  }

  private async loadFiles (pickUpRequest: PickUpRequest): Promise<void> {
    pickUpRequest.additionalFiles = await this.fileLinkService.loadFileLinks(
      pickUpRequest.uuid,
      PickUpRequest.name,
      EntityPart.ADDITIONAL
    )
  }
}
