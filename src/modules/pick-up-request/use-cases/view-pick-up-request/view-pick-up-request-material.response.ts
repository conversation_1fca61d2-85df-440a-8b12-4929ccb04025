import { ApiProperty } from '@nestjs/swagger'
import { PickUpRequestMaterialWithContractLine } from '../../types/pick-up-request-material-with-contract-line.type.js'
import { WasteMeasurementUnit } from '../../../waste-inquiry/enums/waste-measurement-unit.enum.js'
import { ContractLinePackagingTypeApiProperty, ContractLinePackagingType } from '../../../contract-line/enums/contract-line-packaging-type.enum.js'

export class MaterialResponse {
  @ApiProperty({ type: String })
  contractLineId: string

  @ApiProperty({ type: String })
  contractNumber: string

  @ApiProperty({ type: String })
  contractItem: string

  @ApiProperty({ type: String, nullable: true })
  tcNumber: string | null

  @ApiProperty({ type: String, nullable: true })
  customerReference: string | null

  @ApiProperty({ type: String, nullable: true })
  wasteMaterial: string | null

  @ApiProperty({ type: String, nullable: true })
  materialNumber: string | null

  @ApiProperty({ type: String, nullable: true })
  treatmentCenterName: string | null

  @ApiProperty({ type: String, nullable: true })
  installationName: string | null

  @ApiProperty({ type: String, nullable: true })
  customerId: string | null

  @ApiProperty({ type: String, nullable: true })
  customerName: string | null

  @ApiProperty({ type: String, nullable: true })
  wasteProducerId: string | null

  @ApiProperty({ type: String, nullable: true })
  wasteProducerName: string | null

  @ApiProperty({ type: String, nullable: true })
  pickUpAddressId: string | null

  @ApiProperty({ type: String, nullable: true })
  pickUpAddressName: string | null

  @ApiProperty({ type: String, nullable: true })
  asn: string | null

  @ApiProperty({ type: Boolean, nullable: true })
  tfs: boolean | null

  @ApiProperty({ type: Boolean, nullable: true })
  isHazardous: boolean | null

  @ContractLinePackagingTypeApiProperty({ nullable: true })
  packaged: ContractLinePackagingType | null

  @ApiProperty({ type: String, nullable: true })
  materialAnalysis: string | null

  @ApiProperty({ type: String, nullable: true })
  ewcCode: string | null

  @ApiProperty({ type: String, nullable: true })
  endTreatmentCenterId: string | null

  @ApiProperty({ type: String, nullable: true })
  endTreatmentCenterName: string | null

  @ApiProperty({ type: String, nullable: true })
  remarks: string | null

  @ApiProperty({ type: String, nullable: true })
  processCode: string | null

  @ApiProperty({ type: String, nullable: true })
  esnNumber: string | null

  @ApiProperty({ type: String, nullable: true })
  deliveryInfo: string | null

  @ApiProperty({ type: String, nullable: true })
  materialType: string | null

  @ApiProperty({ type: String, nullable: true })
  packagingIndicator: string | null

  @ApiProperty({ type: Number, nullable: true })
  estimatedWeightOrVolumeValue: number | null

  @ApiProperty({ type: String, nullable: true, enum: WasteMeasurementUnit, enumName: 'WasteMeasurementUnit' })
  estimatedWeightOrVolumeUnit: WasteMeasurementUnit | null

  @ApiProperty({ type: String, nullable: true })
  costCenter: string | null

  @ApiProperty({ type: String, nullable: true })
  poNumber: string | null

  @ApiProperty({ type: String, nullable: true })
  unNumber: string | null

  @ApiProperty({ type: String, nullable: true })
  unNumberDescription: string | null

  @ApiProperty({ type: String, nullable: true })
  adrClass: string | null

  @ApiProperty({ type: String, nullable: true })
  packingGroup: string | null

  @ApiProperty({ type: String, nullable: true })
  dangerLabel1: string | null

  @ApiProperty({ type: String, nullable: true })
  dangerLabel2: string | null

  @ApiProperty({ type: String, nullable: true })
  dangerLabel3: string | null

  @ApiProperty({ type: String, nullable: true })
  packagingType: string | null

  @ApiProperty({ type: Number, nullable: true })
  quantityPackages: number | null

  @ApiProperty({ type: Number, nullable: true })
  quantityLabels: number | null

  @ApiProperty({ type: Number, nullable: true })
  quantityPallets: number | null

  @ApiProperty({ type: String, nullable: true })
  containerType: string | null

  @ApiProperty({ type: String, nullable: true })
  containerVolumeSize: string | null

  @ApiProperty({ type: String, nullable: true })
  containerNumber: string | null

  @ApiProperty({ type: String, nullable: true })
  containerTransportType: string | null

  @ApiProperty({ type: Boolean, nullable: true })
  isContainerCovered: boolean | null

  @ApiProperty({ type: String, nullable: true })
  tankerType: string | null

  @ApiProperty({ type: String, nullable: true })
  position: string | null

  @ApiProperty({
    type: Boolean,
    nullable: true,
    description: 'When false, the contract line could not be retrieved from SAP. This occurs when the contract line is expired or rejected.'
  })
  contractLineAccessible: boolean | null

  @ApiProperty({ type: Boolean, nullable: true })
  unNumberHazardous: boolean | null

  @ApiProperty({ type: String, nullable: true })
  reconciliationNumber: string | null

  @ApiProperty({ type: String, nullable: true })
  hazardInducers: string | null

  @ApiProperty({ type: Number, nullable: true })
  quantityContainers: number | null

  @ApiProperty({ type: String, nullable: true })
  tfsNumber: string | null

  @ApiProperty({ type: String, nullable: true })
  serialNumber: string | null

  constructor (material: PickUpRequestMaterialWithContractLine) {
    const { contractLine } = material

    this.contractLineId = material.contractLineId
    this.contractNumber = material.contractNumber
    this.contractItem = material.contractItem
    this.tcNumber = material.tcNumber ?? null
    this.customerReference = contractLine?.customerReference ?? null
    this.wasteMaterial = contractLine?.wasteMaterial ?? null
    this.materialNumber = contractLine?.materialNumber ?? null
    this.treatmentCenterName = contractLine?.treatmentCenterName ?? null
    this.installationName = contractLine?.installationName ?? null
    this.customerId = contractLine?.customerId ?? null
    this.customerName = contractLine?.customerName ?? null
    this.wasteProducerId = contractLine?.wasteProducerId ?? null
    this.wasteProducerName = contractLine?.wasteProducerName ?? null
    this.pickUpAddressId = contractLine?.pickUpAddressId ?? null
    this.pickUpAddressName = contractLine?.pickUpAddressName ?? null
    this.asn = contractLine?.asn ?? null
    this.tfs = contractLine?.tfs ?? null
    this.isHazardous = contractLine?.isHazardous ?? null
    this.packaged = contractLine?.packaged ?? null
    this.materialAnalysis = contractLine?.materialAnalysis ?? null
    this.ewcCode = contractLine?.ewcCode ?? null
    this.endTreatmentCenterId = contractLine?.endTreatmentCenterId ?? null
    this.endTreatmentCenterName = contractLine?.endTreatmentCenterName ?? null
    this.remarks = contractLine?.remarks ?? null
    this.processCode = contractLine?.processCode ?? null
    this.esnNumber = contractLine?.esnNumber ?? null
    this.deliveryInfo = contractLine?.deliveryInfo ?? null
    this.materialType = contractLine?.materialType ?? null
    this.packagingIndicator = contractLine?.packagingIndicator ?? null
    this.estimatedWeightOrVolumeValue = material.estimatedWeightOrVolumeValue ?? null
    this.estimatedWeightOrVolumeUnit = material.estimatedWeightOrVolumeUnit ?? null
    this.costCenter = material.costCenter ?? null
    this.poNumber = material.poNumber ?? null
    this.unNumber = material.unNumber ?? null
    this.unNumberDescription = material.unNumberDescription ?? null
    this.adrClass = material.adrClass ?? null
    this.packingGroup = material.packingGroup ?? null
    this.dangerLabel1 = material.dangerLabel1 ?? null
    this.dangerLabel2 = material.dangerLabel2 ?? null
    this.dangerLabel3 = material.dangerLabel3 ?? null
    this.packagingType = material.packagingType ?? null
    this.quantityPackages = material.quantityPackages ?? null
    this.quantityLabels = material.quantityLabels ?? null
    this.quantityPallets = material.quantityPallets ?? null
    this.containerType = material.containerType ?? null
    this.containerVolumeSize = material.containerVolumeSize ?? null
    this.containerNumber = material.containerNumber ?? null
    this.containerTransportType = material.containerTransportType ?? null
    this.isContainerCovered = material.isContainerCovered ?? null
    this.tankerType = material.tankerType ?? null
    this.position = material.position ?? null
    this.contractLineAccessible = material.contractLineAccessible ?? null
    this.unNumberHazardous = material.unNumberHazardous ?? null
    this.reconciliationNumber = material.reconciliationNumber ?? null
    this.hazardInducers = material.hazardInducers ?? null
    this.quantityContainers = material.quantityContainers ?? null
    this.tfsNumber = material.tfsNumber ?? null
    this.serialNumber = material.serialNumber ?? null
  }
}
