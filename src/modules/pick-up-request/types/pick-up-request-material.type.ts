import { PackingGroup } from '../../waste-inquiry/enums/packaging-group.enum.js'
import { WasteMeasurementUnit } from '../../waste-inquiry/enums/waste-measurement-unit.enum.js'

export interface PickUpRequestMaterial {
  contractLineId: string
  contractNumber: string
  contractItem: string
  processCode?: string | null
  tcNumber?: string | null
  pickUpAddressId?: string | null
  materialNumber?: string | null
  customerReference?: string | null
  isHazardous?: boolean | null
  wasteMaterial?: string | null
  ewcCode?: string | null
  asn?: string | null
  tfs?: boolean | null
  unNumberHazardous?: boolean | null

  estimatedWeightOrVolumeValue?: number | null
  estimatedWeightOrVolumeUnit?: WasteMeasurementUnit | null
  costCenter?: string | null
  poNumber?: string | null
  unNumber?: string | null
  unNumberDescription?: string | null
  adrClass?: string | null
  packingGroup?: PackingGroup | null
  dangerLabel1?: string | null
  dangerLabel2?: string | null
  dangerLabel3?: string | null
  packagingType?: string | null
  quantityPackages?: number | null
  quantityLabels?: number | null
  quantityPallets?: number | null
  containerType?: string | null
  containerVolumeSize?: string | null
  containerNumber?: string | null
  containerTransportType?: string | null
  tankerType?: string | null
  isContainerCovered?: boolean | null
  reconciliationNumber?: string | null
  hazardInducers?: string | null
  quantityContainers?: number | null
  tfsNumber?: string | null
  serialNumber?: string | null
  position?: string | null
  contractLineAccessible?: boolean
}
