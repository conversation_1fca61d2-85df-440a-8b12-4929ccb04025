import { PickUpRequestStatus } from '../enums/pick-up-request-status.enum.js'
import { TransportMode } from '../enums/pick-up-transport-mode.enum.js'

export interface PickUpRequestDynamicTableFields {
  requestNumber: string | null
  createdAt: string | null
  status: PickUpRequestStatus
  wasteMaterial: string[]
  customerId: string | null
  customerName: string | null
  wasteProducerId: string | null
  wasteProducerName: string | null
  pickUpAddressId: string[]
  pickUpAddressName: string[]
  customerReference: string | null
  contractNumber: string[]
  contractItem: string[]
  transportMode: TransportMode | null
  dateOfRequest: string | null
  treatmentCenterName: string | null
  accountManager: string | null
  costCenter: string[]
  isTransportByIndaver: boolean | null
  requestedStartDate: string | null
  requestedEndDate: string | null
  confirmedTransportDate: string | null
  salesOrder: string | null
  isHazardous: boolean[]
  nameOfApplicant: string | null
  orderNumber: string | null
  containerNumber: string[]
  materialAnalysis: string | null
  deliveryInfo: string | null
  nameInstallation: string | null
  disposalCertificateNumber: string | null
  ewc: string | null
  tfsNumber: string | null
}
