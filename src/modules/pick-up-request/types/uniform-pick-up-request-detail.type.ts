import { Contact } from '../../contact/types/contact.type.js'
import { SapCustomer } from '../../customer/types/sap-customer.type.js'
import { FileLink } from '../../files/entities/file-link.entity.js'
import { ExternalFile } from '../../files/types/external-file.type.js'
import { SapPickUpAddress } from '../../pick-up-address/types/sap-pick-up-address.type.js'
import { SapWasteProducer } from '../../waste-producer/types/sap-waste-producer.type.js'
import { PickUpRequestStatus } from '../enums/pick-up-request-status.enum.js'
import { PickUpTransportMode } from '../enums/pick-up-transport-mode.enum.js'
import { PackagingRequestMaterial } from './packaging-request-material.type.js'
import { PickUpRequestMaterialWithContractLine } from './pick-up-request-material-with-contract-line.type.js'

export class UniformDetailPickUpRequest {
  uuid: string | null
  requestNumber: string | null
  status: PickUpRequestStatus
  customer: SapCustomer | null
  wasteProducer: SapWasteProducer | null
  pickUpAddresses: SapPickUpAddress[]
  transportMode: PickUpTransportMode | null
  isTransportByIndaver: boolean | null
  startDate: string | null
  endDate: string | null
  totalQuantityPallets: number | null
  isReturnPackaging: boolean | null
  packagingRemark: string | null
  materialsWithContractLine: PickUpRequestMaterialWithContractLine[]
  remarks: string | null
  sendCopyToContacts: Contact[]
  submittedOn: Date | null
  additionalFiles: FileLink[] | ExternalFile[]
  createdBy: string | null
  confirmedDate: string | null
  packagingRequestMaterials: PackagingRequestMaterial[] = []
  isWicConfirmed: boolean | null

  get needsWicConfirmation (): boolean {
    return this.isWicConfirmed === true
  }
}
