import assert from 'assert'
import { PickUpRequest } from '../entities/pick-up-request.entity.js'
import { UniformPickUpRequest } from '../types/uniform-pick-up-request.type.js'
import { PickUpRequestStatus } from '../enums/pick-up-request-status.enum.js'
import { PackagingRequestMode } from '../enums/pick-up-transport-mode.enum.js'

export class MapUniformPickUpRequestDbService {
  static mapResultToUniformPickUpRequest (
    pickUpRequest: PickUpRequest
  ): UniformPickUpRequest {
    assert(pickUpRequest.createdByUser !== undefined)

    return {
      uuid: pickUpRequest.uuid,
      createdAt: pickUpRequest.createdAt,
      requestNumber: pickUpRequest.requestNumber ?? null,
      status: pickUpRequest.submittedOn === null
        ? PickUpRequestStatus.DRAFT
        : PickUpRequestStatus.PENDING,
      wasteMaterial: this.toNonNullableArray(pickUpRequest.materials, 'wasteMaterial'),
      customerId: pickUpRequest.customerId,
      customerName: pickUpRequest.customer?.name ?? null,
      wasteProducerId: pickUpRequest.wasteProducerId,
      wasteProducerName: pickUpRequest.wasteProducer?.name ?? null,
      pickUpAddressId: this.toNonNullableArray(pickUpRequest.materials, 'pickUpAddressId'),
      pickUpAddressName: null,
      customerReference: null,
      contractNumber: this.toNonNullableArray(pickUpRequest.materials, 'contractNumber'),
      contractItem: this.toNonNullableArray(pickUpRequest.materials, 'contractItem'),
      transportMode: !pickUpRequest.isOnlyPackagingRequest
        ? pickUpRequest.transportMode
        : PackagingRequestMode.PACKAGING_REQUEST_ORDER,
      dateOfRequest: null,
      treatmentCenterName: null,
      accountManager: null,
      costCenter: this.toNonNullableArray(pickUpRequest.materials, 'costCenter'),
      isTransportByIndaver: pickUpRequest.isTransportByIndaver,
      requestedStartDate: pickUpRequest.startDate,
      requestedEndDate: pickUpRequest.endDate,
      confirmedTransportDate: null,
      salesOrder: null,
      isHazardous: this.toNonNullableArray(pickUpRequest.materials, 'isHazardous'),
      nameOfApplicant: pickUpRequest.createdByUser?.fullName,
      orderNumber: null,
      containerNumber: this.toNonNullableArray(pickUpRequest.materials, 'containerNumber'),
      materialAnalysis: null,
      deliveryInfo: null,
      nameInstallation: null,
      disposalCertificateNumber: null,
      ewc: null,
      tfsNumber: null
    }
  }

  static mapResultsToUniformPickUpRequests (
    pickUpRequests: PickUpRequest[]
  ): UniformPickUpRequest[] {
    return pickUpRequests.map(pickUpRequest => this.mapResultToUniformPickUpRequest(pickUpRequest))
  }

  private static toNonNullableArray<T, K extends keyof T>(
    array: readonly T[],
    key: K
  ): NonNullable<T[K]>[] {
    return array
      .map(item => item[key])
      .filter(value => value != null)
  }
}
