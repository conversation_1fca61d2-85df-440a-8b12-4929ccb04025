import { before, beforeEach, describe, it } from 'node:test'
import { expect } from 'expect'
import { TestBench } from '../../../../../test/setup/test-bench.js'
import { MapUniformPickUpRequestDetailSapService } from '../map-uniform-pick-up-request-detail-sap.service.js'
import { PickUpRequestStatus, SAP_INDASCAN_STATUS } from '../../enums/pick-up-request-status.enum.js'
import { PickUpTransportMode } from '../../enums/pick-up-transport-mode.enum.js'
import { SapGetPickUpRequestDetailResponse } from '../../../sap/use-cases/get-pick-up-request-detail/get-pick-up-request-detail.response.js'
import { WasteMeasurementUnit } from '../../../waste-inquiry/enums/waste-measurement-unit.enum.js'
import { GetPickUpRequestCommentResponseBuilder } from '../../../sap/use-cases/get-pick-up-request-comment/tests/get-pick-up-request-comment.response.builder.js'
import { GetPickUpRequestContactsResponseBuilder } from '../../../sap/use-cases/get-pick-up-request-contacts/tests/get-pick-up-request-contacts.response.builder.js'
import { GetPickUpRequestAttachmentsResponseBuilder } from '../../../sap/use-cases/get-pick-up-request-attachments/tests/get-pick-up-request-attachments.response.builder.js'

describe('Map uniform pick-up request detail SAP unit test', () => {
  let basePickUpRequestResponse: SapGetPickUpRequestDetailResponse

  before(() => {
    TestBench.setupUnitTest()
  })

  beforeEach(() => {
    basePickUpRequestResponse = {
      Reqno: '123',
      Vbeln: '456',
      Posnr: '789',
      Status: '002',
      Kunnr: '50000000',
      Kunnrname: 'Customer name',
      Kunnrstreet: 'customer street',
      Kunnrcity: 'customer city',
      Kunnrpostcode: '1234AB',
      Kunnrland: 'BE',
      KunnrY2: '60000000',
      Kunnry2name: 'Waste producer name',
      Kunnry2street: 'waste producer street',
      Kunnry2city: 'waste producer city',
      Kunnry2postcode: '1234CD',
      Kunnry2land: 'FR',
      KunnrWe: '70000000',
      Kunnrwename: 'Pick-up address name',
      Kunnrwestreet: 'pick-up address street',
      Kunnrwecity: 'pick-up address city',
      Kunnrwepostcode: '1234EF',
      Kunnrweland: 'DE',
      WasteType: '01',
      TransportBy: 'X',
      ReqDateFrom: 'Date(1735689600000)',
      ReqDateTo: 'Date(1735689600000)',
      QuantityPallets: '10',
      ReturnPackaging: false,
      ReturnPackagingRemark: 'My packaging remarks',
      NrOPallets: '10',
      WeightVolume: '1000',
      WeightUom: 'KG',
      RequestedDate: 'Date(1735689600000)',
      DateApplication: 'Date(1735689600000)',
      NameApplicant: 'John Doe',
      CostCenter: 'Logistics',
      Yyun: '01',
      TypeRecipient: '02',
      QuantityBarrels: '5',
      QuantityLabels: '3',
      ContainerType: '03',
      OrderNumber: '666',
      Cont1Nr: '123456789',
      Cont1Transport: '01',
      Cont1Weight: '1500',
      BulkType: '03',
      Cont1Cover: '01',
      ConfirmedDate: 'Date(1735689600000)'
    }
  })

  it('Maps a SAP pick-up request response to a uniform pick-up request detail', () => {
    const pickUpRequestComment = new GetPickUpRequestCommentResponseBuilder()
      .withRequestNumber('123')
      .withComment('Comment for the pick-up request.')
      .build()

    const pickUpRequestContacts = [
      new GetPickUpRequestContactsResponseBuilder()
        .withRequestNumber('123')
        .withEmail('<EMAIL>')
        .withName1('John')
        .withName2('Doe')
        .build()
    ]

    const pickUpRequestAttachments = [
      new GetPickUpRequestAttachmentsResponseBuilder()
        .withFilename('image.png')
        .withPosnr('0001')
        .build()
    ]

    const result = MapUniformPickUpRequestDetailSapService.mapResultToUniformPickUpRequest(
      [basePickUpRequestResponse],
      pickUpRequestComment,
      pickUpRequestContacts,
      pickUpRequestAttachments
    )

    expect(result).toMatchObject({
      uuid: null,
      requestNumber: '123',
      status: PickUpRequestStatus.CONFIRMED,
      customer: {
        id: '50000000',
        name: 'Customer name',
        address: MapUniformPickUpRequestDetailSapService.formatAddress(
          'customer street',
          'customer city',
          '1234AB',
          'BE'
        )
      },
      wasteProducer: {
        id: '60000000',
        name: 'Waste producer name',
        address: MapUniformPickUpRequestDetailSapService.formatAddress(
          'waste producer street',
          'waste producer city',
          '1234CD',
          'FR'
        )
      },
      pickUpAddresses: [{
        id: '70000000',
        name: 'Pick-up address name',
        address: MapUniformPickUpRequestDetailSapService.formatAddress(
          'pick-up address street',
          'pick-up address city',
          '1234EF',
          'DE'
        )
      }],
      transportMode: PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK,
      isTransportByIndaver: true,
      startDate: '2025-01-01',
      endDate: '2025-01-01',
      totalQuantityPallets: 10,
      isReturnPackaging: false,
      packagingRemark: 'My packaging remarks',
      materialsWithContractLine: [{
        contractLineId: '456:789',
        contractNumber: '456',
        contractItem: '789',
        tcNumber: undefined,
        quantityPallets: 10,
        estimatedWeightOrVolumeValue: 1000,
        estimatedWeightOrVolumeUnit: WasteMeasurementUnit.KG,
        costCenter: 'Logistics',
        unNumber: '01',
        packagingType: '02',
        quantityPackages: 5,
        quantityLabels: 3,
        containerType: '03',
        containerNumber: '123456789',
        containerTransportType: '01',
        containerVolumeSize: '1500',
        tankerType: '03',
        isContainerCovered: true
      }],
      remarks: 'Comment for the pick-up request.',
      sendCopyToContacts: [{
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe'
      }],
      submittedOn: new Date('2025-01-01'),
      additionalFiles: [{
        name: 'image.png',
        mimeType: null,
        url: null,
        order: 1
      }],
      createdBy: 'John Doe',
      confirmedDate: '2025-01-01'
    })
  })

  it('sets quantity labels to 0 when the pickup request is a draft Indascan', () => {
    basePickUpRequestResponse.Status = SAP_INDASCAN_STATUS[0]
    basePickUpRequestResponse.ExtId = 'FILLED IN'
    basePickUpRequestResponse.QuantityLabels = '9999999999'

    const pickUpRequestComment = new GetPickUpRequestCommentResponseBuilder()
      .withRequestNumber('123')
      .withComment('Comment for the pick-up request.')
      .build()

    const pickUpRequestContacts = [
      new GetPickUpRequestContactsResponseBuilder()
        .withRequestNumber('123')
        .withEmail('<EMAIL>')
        .withName1('John')
        .withName2('Doe')
        .build()
    ]

    const pickUpRequestAttachments = [
      new GetPickUpRequestAttachmentsResponseBuilder()
        .withFilename('image.png')
        .withPosnr('0001')
        .build()
    ]

    const result = MapUniformPickUpRequestDetailSapService.mapResultToUniformPickUpRequest(
      [basePickUpRequestResponse],
      pickUpRequestComment,
      pickUpRequestContacts,
      pickUpRequestAttachments
    )

    expect(result).toMatchObject({
      uuid: null,
      requestNumber: '123',
      status: PickUpRequestStatus.INDASCAN_DRAFT,
      customer: {
        id: '50000000',
        name: 'Customer name',
        address: MapUniformPickUpRequestDetailSapService.formatAddress(
          'customer street',
          'customer city',
          '1234AB',
          'BE'
        )
      },
      wasteProducer: {
        id: '60000000',
        name: 'Waste producer name',
        address: MapUniformPickUpRequestDetailSapService.formatAddress(
          'waste producer street',
          'waste producer city',
          '1234CD',
          'FR'
        )
      },
      pickUpAddresses: [{
        id: '70000000',
        name: 'Pick-up address name',
        address: MapUniformPickUpRequestDetailSapService.formatAddress(
          'pick-up address street',
          'pick-up address city',
          '1234EF',
          'DE'
        )
      }],
      transportMode: PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK,
      isTransportByIndaver: true,
      startDate: '2025-01-01',
      endDate: '2025-01-01',
      totalQuantityPallets: 10,
      isReturnPackaging: false,
      packagingRemark: 'My packaging remarks',
      materialsWithContractLine: [{
        contractLineId: '456:789',
        contractNumber: '456',
        contractItem: '789',
        tcNumber: undefined,
        quantityPallets: 10,
        estimatedWeightOrVolumeValue: 1000,
        estimatedWeightOrVolumeUnit: WasteMeasurementUnit.KG,
        costCenter: 'Logistics',
        unNumber: '01',
        packagingType: '02',
        quantityPackages: 5,
        quantityLabels: 0,
        containerType: '03',
        containerNumber: '123456789',
        containerTransportType: '01',
        containerVolumeSize: '1500',
        tankerType: '03',
        isContainerCovered: true
      }],
      remarks: 'Comment for the pick-up request.',
      sendCopyToContacts: [{
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe'
      }],
      submittedOn: new Date('2025-01-01'),
      additionalFiles: [{
        name: 'image.png',
        mimeType: null,
        url: null,
        order: 1
      }],
      createdBy: 'John Doe',
      confirmedDate: '2025-01-01'
    })
  })
})
