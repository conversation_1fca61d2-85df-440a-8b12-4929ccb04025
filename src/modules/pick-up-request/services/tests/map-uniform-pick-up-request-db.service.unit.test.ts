import { before, describe, it } from 'node:test'
import { expect } from 'expect'
import { TestBench } from '../../../../../test/setup/test-bench.js'
import { UserEntityBuilder } from '../../../../app/users/tests/user-entity.builder.js'
import { PickUpRequestEntityBuilder } from '../../tests/pick-up-request-entity.builder.js'
import { MapUniformPickUpRequestDbService } from '../map-uniform-pick-up-request-db.service.js'
import { PickUpRequestStatus } from '../../enums/pick-up-request-status.enum.js'
import { PickUpTransportMode } from '../../enums/pick-up-transport-mode.enum.js'

describe('Map uniform pick-up request db unit test', () => {
  before(() => {
    TestBench.setupUnitTest()
  })

  it('Maps a pick-up to a uniform waste inquiry', () => {
    const user = new UserEntityBuilder().withFirstName('John').withLastName('Doe').build()
    const pickUpRequest = new PickUpRequestEntityBuilder()
      .withCustomerId('customer-id')
      .withCustomerName('Customer name')
      .withWasteProducerId('waste-producer-id')
      .withWasteProducerName('Waste Producer name')
      .withTransportMode(PickUpTransportMode.BULK_ISO_TANK)
      .withIsTransportByIndaver(true)
      .withStartDate('2025-01-01')
      .withEndDate('2025-01-02')
      .build()

    pickUpRequest.customer = { id: 'customer-id', name: 'Customer name', address: null }
    pickUpRequest.wasteProducer = { id: 'waste-producer-id', name: 'Waste Producer name', address: null }
    pickUpRequest.createdByUser = user

    const result = MapUniformPickUpRequestDbService.mapResultToUniformPickUpRequest(
      pickUpRequest
    )

    expect(result).toMatchObject({
      uuid: pickUpRequest.uuid,
      requestNumber: null,
      status: PickUpRequestStatus.DRAFT,
      wasteMaterial: [],
      customerId: 'customer-id',
      customerName: 'Customer name',
      wasteProducerId: 'waste-producer-id',
      wasteProducerName: 'Waste Producer name',
      pickUpAddressId: [],
      pickUpAddressName: [],
      customerReference: null,
      contractNumber: [],
      contractItem: [],
      transportMode: PickUpTransportMode.BULK_ISO_TANK,
      dateOfRequest: null,
      treatmentCenterName: null,
      accountManager: null,
      costCenter: [],
      isTransportByIndaver: true,
      requestedStartDate: '2025-01-01',
      requestedEndDate: '2025-01-02',
      confirmedTransportDate: null,
      salesOrder: null,
      isHazardous: [],
      nameOfApplicant: 'John Doe',
      orderNumber: null,
      containerNumber: [],
      materialAnalysis: null,
      deliveryInfo: null,
      nameInstallation: null,
      disposalCertificateNumber: null,
      ewc: null,
      tfsNumber: null
    })
  })
})
