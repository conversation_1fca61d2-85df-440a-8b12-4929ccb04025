import { before, describe, it, afterEach } from 'node:test'
import { randomUUID } from 'crypto'
import Sinon, { assert, createStubInstance, SinonStubbedInstance } from 'sinon'
import { Any, FindOptionsWhere, Repository, IsNull, Not } from 'typeorm'
import { randEmail } from '@ngneat/falso'
import { TestBench } from '../../../../../test/setup/test-bench.js'
import { PickUpRequest } from '../../entities/pick-up-request.entity.js'
import { PickUpRequestEntityBuilder } from '../../tests/pick-up-request-entity.builder.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { UserWasteProducerAuthService } from '../../../auth/services/user-waste-producer-auth.service.js'
import { PickUpRequestTemplateRepository } from '../pick-up-request-template.repository.js'

describe('PickUpRequestTemplateService unit test', () => {
  let service: PickUpRequestTemplateRepository

  let userUuid: string
  let pickUpRequest: PickUpRequest
  let pickUpRequestUuid: string
  let customerIdSelected: string
  let restrictedWasteProducerIds: string[]

  let authContext: SinonStubbedInstance<AuthContext>
  let pickUpRequestRepository: SinonStubbedInstance<Repository<PickUpRequest>>
  let userWasteProducerAuthService: SinonStubbedInstance<UserWasteProducerAuthService>

  before(() => {
    TestBench.setupUnitTest()

    userUuid = randomUUID()
    pickUpRequestUuid = randomUUID()
    customerIdSelected = randomUUID()
    restrictedWasteProducerIds = [randomUUID(), randomUUID()]

    pickUpRequest = new PickUpRequestEntityBuilder()
      .withUuid(pickUpRequestUuid)
      .withCustomerId(customerIdSelected)
      .build()

    authContext = createStubInstance(AuthContext, {
      getUserUuidOrFail: userUuid,
      getSelectedCustomerId: customerIdSelected,
      getAzureEntraUpn: randEmail()
    })

    pickUpRequestRepository = createStubInstance<Repository<PickUpRequest>>(
      Repository<PickUpRequest>
    )
    pickUpRequestRepository.findOneOrFail.resolves(pickUpRequest)
    pickUpRequestRepository.find.resolves([pickUpRequest])

    userWasteProducerAuthService = createStubInstance(UserWasteProducerAuthService)
    userWasteProducerAuthService.getRestrictedWasteProducerIds.resolves(restrictedWasteProducerIds)

    service = new PickUpRequestTemplateRepository(
      authContext,
      pickUpRequestRepository,
      userWasteProducerAuthService
    )
  })

  afterEach(() => {
    Sinon.resetHistory()
  })

  it('Finds multiple pick-up request templates when created by the same user', async () => {
    const pickUpRequestUuids = [pickUpRequestUuid, randomUUID()]
    const pickUpRequests = [
      pickUpRequest,
      new PickUpRequestEntityBuilder().withUuid(pickUpRequestUuids[1]).build()
    ]

    authContext.getUserUuidOrFail.returns(userUuid)
    authContext.getSelectedCustomerId.returns(null)
    pickUpRequestRepository.find.resolves(pickUpRequests)

    const result = await service.findPickUpRequestTemplates(pickUpRequestUuids)

    assert.match(result, pickUpRequests)
    const whereConditions: FindOptionsWhere<PickUpRequest>[] = [
      {
        uuid: Any(pickUpRequestUuids),
        createdByUserUuid: userUuid,
        templateName: Not(IsNull())
      }
    ]
    assert.calledOnceWithExactly(pickUpRequestRepository.find, {
      select: undefined,
      where: whereConditions,
      relations: {
        templateUpdatedByUser: true
      }
    })
    assert.notCalled(userWasteProducerAuthService.getRestrictedWasteProducerIds)
  })

  it('Finds multiple pick-up request templates with query options', async () => {
    const pickUpRequestUuids = [pickUpRequestUuid, randomUUID()]
    const pickUpRequests = [
      pickUpRequest,
      new PickUpRequestEntityBuilder().withUuid(pickUpRequestUuids[1]).build()
    ]
    const queryOptions = { selects: ['uuid', 'templateName', 'customerId'] as (keyof PickUpRequest)[] }

    authContext.getUserUuidOrFail.returns(userUuid)
    authContext.getSelectedCustomerId.returns(null)
    pickUpRequestRepository.find.resolves(pickUpRequests)

    const result = await service.findPickUpRequestTemplates(pickUpRequestUuids, queryOptions)

    assert.match(result, pickUpRequests)
    const whereConditions: FindOptionsWhere<PickUpRequest>[] = [
      {
        uuid: Any(pickUpRequestUuids),
        createdByUserUuid: userUuid,
        templateName: Not(IsNull())
      }
    ]
    assert.calledOnceWithExactly(pickUpRequestRepository.find, {
      select: queryOptions.selects,
      where: whereConditions,
      relations: {
        templateUpdatedByUser: true
      }
    })
    assert.notCalled(userWasteProducerAuthService.getRestrictedWasteProducerIds)
  })

  it('Finds multiple pick-up request templates with customer access and restricted waste producer ids', async () => {
    const pickUpRequestUuids = [pickUpRequestUuid, randomUUID()]
    const pickUpRequests = [
      pickUpRequest,
      new PickUpRequestEntityBuilder().withUuid(pickUpRequestUuids[1]).build()
    ]

    authContext.getUserUuidOrFail.returns(userUuid)
    authContext.getSelectedCustomerId.returns(customerIdSelected)
    authContext.getAzureEntraUpn.returns(randEmail())
    userWasteProducerAuthService.getRestrictedWasteProducerIds.resolves(restrictedWasteProducerIds)
    pickUpRequestRepository.find.resolves(pickUpRequests)

    const result = await service.findPickUpRequestTemplates(pickUpRequestUuids)

    assert.match(result, pickUpRequests)
    const whereConditions: FindOptionsWhere<PickUpRequest>[] = [
      {
        uuid: Any(pickUpRequestUuids),
        createdByUserUuid: userUuid,
        templateName: Not(IsNull())
      },
      {
        uuid: Any(pickUpRequestUuids),
        customerId: customerIdSelected,
        wasteProducerId: Any(restrictedWasteProducerIds),
        templateName: Not(IsNull())
      }
    ]
    assert.calledOnceWithExactly(pickUpRequestRepository.find, {
      select: undefined,
      where: whereConditions,
      relations: {
        templateUpdatedByUser: true
      }
    })
    assert.calledOnce(userWasteProducerAuthService.getRestrictedWasteProducerIds)
  })

  it('Finds multiple pick-up request templates with customer access but no restricted waste producer ids', async () => {
    const pickUpRequestUuids = [pickUpRequestUuid, randomUUID()]
    const pickUpRequests = [
      pickUpRequest,
      new PickUpRequestEntityBuilder().withUuid(pickUpRequestUuids[1]).build()
    ]

    authContext.getUserUuidOrFail.returns(userUuid)
    authContext.getSelectedCustomerId.returns(customerIdSelected)
    authContext.getAzureEntraUpn.returns(randEmail())
    userWasteProducerAuthService.getRestrictedWasteProducerIds.resolves([])
    pickUpRequestRepository.find.resolves(pickUpRequests)

    const result = await service.findPickUpRequestTemplates(pickUpRequestUuids)

    assert.match(result, pickUpRequests)
    const whereConditions: FindOptionsWhere<PickUpRequest>[] = [
      {
        uuid: Any(pickUpRequestUuids),
        createdByUserUuid: userUuid,
        templateName: Not(IsNull())
      },
      {
        uuid: Any(pickUpRequestUuids),
        customerId: customerIdSelected,
        wasteProducerId: undefined,
        templateName: Not(IsNull())
      }
    ]
    assert.calledOnceWithExactly(pickUpRequestRepository.find, {
      select: undefined,
      where: whereConditions,
      relations: {
        templateUpdatedByUser: true
      }
    })
    assert.calledOnce(userWasteProducerAuthService.getRestrictedWasteProducerIds)
  })

  it('Finds a pick-up request template when created by the same user', async () => {
    authContext.getUserUuidOrFail.returns(userUuid)
    authContext.getSelectedCustomerId.returns(null)

    await service.findPickUpRequestTemplateOrFail(pickUpRequestUuid)

    const whereConditions: FindOptionsWhere<PickUpRequest>[] = [
      {
        uuid: pickUpRequestUuid,
        createdByUserUuid: userUuid,
        templateName: Not(IsNull())
      }
    ]
    assert.calledOnceWithExactly(pickUpRequestRepository.findOneOrFail, {
      where: whereConditions,
      relations: {
        templateUpdatedByUser: true
      }
    })
    assert.notCalled(userWasteProducerAuthService.getRestrictedWasteProducerIds)
  })

  it('Finds a pick-up request template with customer access and restricted waste producer ids', async () => {
    authContext.getUserUuidOrFail.returns(userUuid)
    authContext.getSelectedCustomerId.returns(customerIdSelected)
    userWasteProducerAuthService.getRestrictedWasteProducerIds.resolves(restrictedWasteProducerIds)

    await service.findPickUpRequestTemplateOrFail(pickUpRequestUuid)

    const whereConditions: FindOptionsWhere<PickUpRequest>[] = [
      {
        uuid: pickUpRequestUuid,
        createdByUserUuid: userUuid,
        templateName: Not(IsNull())
      },
      {
        uuid: pickUpRequestUuid,
        customerId: customerIdSelected,
        wasteProducerId: Any(restrictedWasteProducerIds),
        templateName: Not(IsNull())
      }
    ]
    assert.calledOnceWithExactly(pickUpRequestRepository.findOneOrFail, {
      where: whereConditions,
      relations: {
        templateUpdatedByUser: true
      }
    })
    assert.calledOnce(userWasteProducerAuthService.getRestrictedWasteProducerIds)
  })

  it('Finds a pick-up request template with customer access but no restricted waste producer ids', async () => {
    authContext.getUserUuidOrFail.returns(userUuid)
    authContext.getSelectedCustomerId.returns(customerIdSelected)
    userWasteProducerAuthService.getRestrictedWasteProducerIds.resolves([])

    await service.findPickUpRequestTemplateOrFail(pickUpRequestUuid)

    const whereConditions: FindOptionsWhere<PickUpRequest>[] = [
      {
        uuid: pickUpRequestUuid,
        createdByUserUuid: userUuid,
        templateName: Not(IsNull())
      },
      {
        uuid: pickUpRequestUuid,
        customerId: customerIdSelected,
        wasteProducerId: undefined,
        templateName: Not(IsNull())
      }
    ]
    assert.calledOnceWithExactly(pickUpRequestRepository.findOneOrFail, {
      where: whereConditions,
      relations: {
        templateUpdatedByUser: true
      }
    })
    assert.calledOnce(userWasteProducerAuthService.getRestrictedWasteProducerIds)
  })
})
