import { before, describe, it } from 'node:test'
import { expect } from 'expect'
import { TestBench } from '../../../../../test/setup/test-bench.js'
import { SapGetPickUpRequestIndexResponse } from '../../../sap/use-cases/get-pick-up-request-index/get-pick-up-request-index.response.js'
import { MapUniformPickUpRequestSapService } from '../map-uniform-pick-up-request-sap.service.js'
import { PickUpRequestStatus } from '../../enums/pick-up-request-status.enum.js'
import { PickUpTransportMode } from '../../enums/pick-up-transport-mode.enum.js'

describe('Map uniform pick-up request SAP unit test', () => {
  before(() => {
    TestBench.setupUnitTest()
  })

  it('Maps a SAP pick-up request response to a uniform waste inquiry', () => {
    const pickUpRequest: SapGetPickUpRequestIndexResponse = {
      Reqno: '123',
      Status: '001',
      Arktx: 'Waste',
      Kunnr: 'customer-id',
      Kunnry2: 'waste-producer-id',
      Kunnrwe: 'pick-up-address-id',
      Yyklantmat: 'customer-reference',
      Vbeln: 'contract-number',
      Posnr: 'position-number',
      Wastetype: '01',
      Dateapplication: '/Date(1719446400000)/',
      Costcenter: 'CC1',
      Transportby: 'X',
      Requesteddate: '/Date(1719446400000)/',
      Confirmeddate: '/Date(1719619200000)/',
      Salesdoc: 'SO1',
      Dangerous: 'X',
      Nameapplicant: 'John Doe',
      Ordernumber: 'ORD1',
      Cont1nr: 'CONT1',
      Eural: '01 01 01',
      Tfsnumber: 'TFS1'
    }

    const result = MapUniformPickUpRequestSapService.mapResultToUniformPickUpRequest(
      pickUpRequest
    )

    expect(result).toMatchObject({
      uuid: null,
      requestNumber: '123',
      status: PickUpRequestStatus.PENDING,
      wasteMaterial: ['Waste'],
      customerId: 'customer-id',
      customerName: null,
      wasteProducerId: 'waste-producer-id',
      wasteProducerName: null,
      pickUpAddressId: ['pick-up-address-id'],
      pickUpAddressName: null,
      customerReference: 'customer-reference',
      contractNumber: ['contract-number'],
      contractItem: ['position-number'],
      transportMode: PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK,
      dateOfRequest: '2024-06-27',
      treatmentCenterName: null,
      accountManager: null,
      costCenter: ['CC1'],
      isTransportByIndaver: true,
      requestedStartDate: '2024-06-27',
      requestedEndDate: null,
      confirmedTransportDate: '2024-06-29',
      salesOrder: 'SO1',
      isHazardous: [true],
      nameOfApplicant: 'John Doe',
      orderNumber: 'ORD1',
      containerNumber: ['CONT1'],
      materialAnalysis: null,
      deliveryInfo: null,
      nameInstallation: null,
      disposalCertificateNumber: null,
      ewc: '01 01 01',
      tfsNumber: 'TFS1'
    })
  })
})
