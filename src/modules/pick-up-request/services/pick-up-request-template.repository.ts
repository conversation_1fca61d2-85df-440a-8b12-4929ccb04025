import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { Any, FindOptionsWhere, IsNull, Not, Repository } from 'typeorm'
import { AuthContext } from '../../auth/auth.context.js'
import { UserWasteProducerAuthService } from '../../auth/services/user-waste-producer-auth.service.js'
import { PickUpRequest } from '../entities/pick-up-request.entity.js'

interface CustomerAccessOptions {
  customerId: string
  restrictedWasteProducerIds?: string[]
}

interface PickUpRequestTemplateQueryOptions {
  selects: (keyof PickUpRequest)[]
}

@Injectable()
export class PickUpRequestTemplateRepository {
  constructor (
    private readonly authContext: AuthContext,
    @InjectRepository(PickUpRequest)
    private readonly pickUpRequestRepository: Repository<PickUpRequest>,
    private readonly userWasteProducerAuthService: UserWasteProducerAuthService
  ) {}

  async findPickUpRequestTemplates (
    pickUpRequestUuids: string[],
    queryOptions?: PickUpRequestTemplateQueryOptions
  ): Promise<PickUpRequest[]> {
    const authUserUuid = this.authContext.getUserUuidOrFail()
    const whereOptions = await this.buildWhereConditions(pickUpRequestUuids, authUserUuid)

    return await this.pickUpRequestRepository.find({
      select: queryOptions !== undefined
        ? queryOptions.selects
        : undefined,
      where: whereOptions,
      relations: {
        templateUpdatedByUser: true
      }
    })
  }

  async findPickUpRequestTemplateOrFail (pickUpRequestUuid: string): Promise<PickUpRequest> {
    const authUserUuid = this.authContext.getUserUuidOrFail()
    const whereOptions = await this.buildWhereConditions(pickUpRequestUuid, authUserUuid)

    return await this.pickUpRequestRepository.findOneOrFail({
      where: whereOptions,
      relations: {
        templateUpdatedByUser: true
      }
    })
  }

  private async buildWhereConditions (
    pickUpRequestUuid: string | string[],
    authUserUuid: string
  ): Promise<FindOptionsWhere<PickUpRequest>[]> {
    const uuidCondition = Array.isArray(pickUpRequestUuid)
      ? Any(pickUpRequestUuid)
      : pickUpRequestUuid

    const baseCondition: FindOptionsWhere<PickUpRequest> = {
      uuid: uuidCondition,
      createdByUserUuid: authUserUuid,
      templateName: Not(IsNull())
    }

    const conditions: FindOptionsWhere<PickUpRequest>[] = [baseCondition]

    const customerAccess = await this.getCustomerAccess()

    if (customerAccess !== null) {
      const wasteProducerIds = customerAccess.restrictedWasteProducerIds
      conditions.push({
        uuid: uuidCondition,
        templateName: Not(IsNull()),
        customerId: customerAccess.customerId,
        wasteProducerId: wasteProducerIds !== undefined && wasteProducerIds.length > 0
          ? Any(wasteProducerIds)
          : undefined
      })
    }

    return conditions
  }

  private async getCustomerAccess (): Promise<CustomerAccessOptions | null> {
    const selectedCustomerId = this.authContext.getSelectedCustomerId()

    if (selectedCustomerId === null) {
      return null
    }

    const restrictedWasteProducerIds = await this.userWasteProducerAuthService
      .getRestrictedWasteProducerIds(
        this.authContext.getAzureEntraUpn(),
        selectedCustomerId
      )

    return {
      customerId: selectedCustomerId,
      restrictedWasteProducerIds
    }
  }
}
