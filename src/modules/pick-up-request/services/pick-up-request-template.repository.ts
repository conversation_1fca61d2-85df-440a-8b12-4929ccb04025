import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { Any, FindOptionsWhere, IsNull, Not, Repository } from 'typeorm'
import { AuthContext } from '../../auth/auth.context.js'
import { UserWasteProducerAuthService } from '../../auth/services/user-waste-producer-auth.service.js'
import { PickUpRequest } from '../entities/pick-up-request.entity.js'

interface CustomerAccessOptions {
  customerId: string
  restrictedWasteProducerIds?: string[]
}

interface PickUpRequestTemplateQueryOptions {
  selects: (keyof PickUpRequest)[]
}

@Injectable()
export class PickUpRequestTemplateRepository {
  constructor (
    private readonly authContext: AuthContext,
    @InjectRepository(PickUpRequest)
    private readonly pickUpRequestRepository: Repository<PickUpRequest>,
    private readonly userWasteProducerAuthService: UserWasteProducerAuthService
  ) {}

  async findPickUpRequestTemplates (
    pickUpRequestUuids: string[],
    queryOptions?: PickUpRequestTemplateQueryOptions
  ): Promise<PickUpRequest[]> {
    const whereOptions = await this.buildWhereConditions(pickUpRequestUuids)

    return await this.pickUpRequestRepository.find({
      select: queryOptions !== undefined
        ? queryOptions.selects
        : undefined,
      where: whereOptions,
      relations: {
        templateUpdatedByUser: true
      }
    })
  }

  async findPickUpRequestTemplateOrFail (pickUpRequestUuid: string): Promise<PickUpRequest> {
    const whereOptions = await this.buildWhereConditions(pickUpRequestUuid)

    return await this.pickUpRequestRepository.findOneOrFail({
      where: whereOptions,
      relations: {
        templateUpdatedByUser: true
      }
    })
  }

  private async getRestrictedWasteProducerIds (): Promise<string[] | undefined> {
    const selectedCustomerId = this.authContext.getSelectedCustomerId()

    if (selectedCustomerId === null) return undefined

    return await this.userWasteProducerAuthService.getRestrictedWasteProducerIds(
      this.authContext.getAzureEntraUpn(),
      selectedCustomerId
    )
  }

  private async buildWhereConditions (
    pickUpRequestUuid: string | string[]
  ): Promise<FindOptionsWhere<PickUpRequest>[]> {
    const customerAccess = await this.getCustomerAccess()
    const uuidCondition = Array.isArray(pickUpRequestUuid)
      ? Any(pickUpRequestUuid)
      : pickUpRequestUuid

    const conditions: FindOptionsWhere<PickUpRequest>[] = [
      {
        uuid: uuidCondition,
        templateName: Not(IsNull()),
        createdByUserUuid: this.authContext.getUserUuidOrFail()
      },
      {
        uuid: uuidCondition,
        templateName: Not(IsNull()),
        customerId: customerAccess?.customerId ?? undefined,
        wasteProducerId: customerAccess?.restrictedWasteProducerIds !== undefined
          && customerAccess.restrictedWasteProducerIds.length > 0
          ? Any(customerAccess.restrictedWasteProducerIds)
          : undefined
      }
    ]

    return conditions
  }

  private async getCustomerAccess (): Promise<CustomerAccessOptions | null> {
    const selectedCustomerId = this.authContext.getSelectedCustomerId()

    if (selectedCustomerId === null) {
      return null
    }

    const restrictedWasteProducerIds = await this.userWasteProducerAuthService
      .getRestrictedWasteProducerIds(
        this.authContext.getAzureEntraUpn(),
        selectedCustomerId
      )

    return {
      customerId: selectedCustomerId,
      restrictedWasteProducerIds
    }
  }
}
