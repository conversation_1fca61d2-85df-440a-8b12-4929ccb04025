import assert from 'assert'
import { Sap<PERSON>ust<PERSON> } from '../../customer/types/sap-customer.type.js'
import { SapPickUpAddress } from '../../pick-up-address/types/sap-pick-up-address.type.js'
import { SapWasteProducer } from '../../waste-producer/types/sap-waste-producer.type.js'
import { UniformDetailPickUpRequest } from '../types/uniform-pick-up-request-detail.type.js'
import { mapSapValueToPickUpRequestStatus, PickUpRequestStatus, SAP_INDASCAN_STATUS } from '../enums/pick-up-request-status.enum.js'
import { mapSapValueToPickUpTransportMode } from '../enums/pick-up-transport-mode.enum.js'
import { mapSapValueToWasteMeasurementUnit, WasteMeasurementUnit } from '../../waste-inquiry/enums/waste-measurement-unit.enum.js'
import { mapSapContractLineId } from '../../contract-line/helpers/map-contract-line-id.helper.js'
import { SapGetPickUpRequestDetailResponse } from '../../sap/use-cases/get-pick-up-request-detail/get-pick-up-request-detail.response.js'
import { SapGetPickUpRequestCommentResponse } from '../../sap/use-cases/get-pick-up-request-comment/get-pick-up-request-comment.response.js'
import { SapGetPickUpRequestContactsResponse } from '../../sap/use-cases/get-pick-up-request-contacts/get-pick-up-request-contacts.response.js'
import { Contact } from '../../contact/types/contact.type.js'
import { SapGetPickUpRequestAttachmentsResponse } from '../../sap/use-cases/get-pick-up-request-attachments/get-pick-up-request-attachments.response.js'
import { ExternalFile } from '../../files/types/external-file.type.js'
import { PickUpRequestMaterialWithContractLine } from '../types/pick-up-request-material-with-contract-line.type.js'
import { MapContractLineSapService } from '../../contract-line/services/map-contract-line.service.js'
import { PackagingRequestMaterial } from '../types/packaging-request-material.type.js'
import { SAP_PACKAGE_WASTE_TYPES, SAP_PICK_UP_WASTE_TYPES } from '../../sap/types/waste.type.js'
import { SapDateFormatterService } from '../../sap/services/sap-date-formatter.service.js'

export class MapUniformPickUpRequestDetailSapService {
  static mapResultToUniformPickUpRequest (
    details: SapGetPickUpRequestDetailResponse[],
    comment: SapGetPickUpRequestCommentResponse | null,
    contacts: SapGetPickUpRequestContactsResponse[],
    attachments: SapGetPickUpRequestAttachmentsResponse[]
  ): UniformDetailPickUpRequest {
    const pickUpRequestDetail = details.find((detail) => {
      return detail.WasteType !== undefined
        && detail.WasteType !== ''
        && SAP_PICK_UP_WASTE_TYPES.includes(detail.WasteType)
    }) ?? details[0]

    assert(pickUpRequestDetail.Vbeln !== undefined)
    assert(pickUpRequestDetail.Posnr !== undefined)

    const uniformPickUpRequest = new UniformDetailPickUpRequest()

    uniformPickUpRequest.uuid = null
    uniformPickUpRequest.requestNumber = pickUpRequestDetail.Reqno !== ''
      && pickUpRequestDetail.Reqno !== undefined
      ? pickUpRequestDetail.Reqno
      : null
    uniformPickUpRequest.status = pickUpRequestDetail.Status !== undefined
      ? mapSapValueToPickUpRequestStatus(pickUpRequestDetail.Status)
      : PickUpRequestStatus.PENDING
    uniformPickUpRequest.customer = this.mapCustomer(pickUpRequestDetail)
    uniformPickUpRequest.wasteProducer = this.mapWasteProducer(pickUpRequestDetail)
    uniformPickUpRequest.pickUpAddresses = this.mapPickUpAddresses(pickUpRequestDetail)
    uniformPickUpRequest.transportMode = pickUpRequestDetail.WasteType !== undefined
      ? mapSapValueToPickUpTransportMode(pickUpRequestDetail.WasteType)
      : null
    uniformPickUpRequest.isTransportByIndaver = pickUpRequestDetail.TransportBy === 'X'
    uniformPickUpRequest.startDate = pickUpRequestDetail.RequestedDate !== undefined
      ? SapDateFormatterService.parseDateToString(pickUpRequestDetail.RequestedDate)
      : null
    uniformPickUpRequest.endDate = pickUpRequestDetail.ReqDateTo !== undefined
      ? SapDateFormatterService.parseDateToString(pickUpRequestDetail.ReqDateTo)
      : null
    uniformPickUpRequest.totalQuantityPallets = pickUpRequestDetail.QuantityPallets !== undefined && pickUpRequestDetail.QuantityPallets !== ''
      ? Number(pickUpRequestDetail.QuantityPallets)
      : null
    uniformPickUpRequest.isReturnPackaging = pickUpRequestDetail.ReturnPackaging ?? null
    uniformPickUpRequest.packagingRemark = pickUpRequestDetail.ReturnPackagingRemark ?? null
    uniformPickUpRequest.remarks = comment !== null
      ? (
          comment.Comment !== undefined && comment.Comment !== ''
            ? comment.Comment
            : null
        )
      : null
    uniformPickUpRequest.sendCopyToContacts = this.mapContacts(contacts)
    uniformPickUpRequest.submittedOn = pickUpRequestDetail.DateApplication != null
      ? SapDateFormatterService.parseDate(pickUpRequestDetail.DateApplication)
      : null
    uniformPickUpRequest.additionalFiles = this.mapAdditionalFiles(attachments)
    uniformPickUpRequest.createdBy = pickUpRequestDetail.NameApplicant ?? null
    uniformPickUpRequest.confirmedDate = pickUpRequestDetail.ConfirmedDate != null
      ? SapDateFormatterService.parseDateToString(pickUpRequestDetail.ConfirmedDate)
      : null

    uniformPickUpRequest.materialsWithContractLine = this.mapWasteMaterials(details)
    uniformPickUpRequest.packagingRequestMaterials = this.mapPackagingRequestMaterial(details)
    uniformPickUpRequest.isWicConfirmed = pickUpRequestDetail.ConfirmWic ?? false

    return uniformPickUpRequest
  }

  static mapCustomer (
    response: SapGetPickUpRequestDetailResponse
  ): SapCustomer | null {
    if (response.Kunnr === undefined) return null
    if (response.Kunnrname === undefined) return null

    return {
      id: response.Kunnr,
      name: response.Kunnrname,
      address: this.formatAddress(
        response.Kunnrstreet ?? null,
        response.Kunnrcity ?? null,
        response.Kunnrpostcode ?? null,
        response.Kunnrland ?? null
      )
    }
  }

  static mapWasteProducer (
    response: SapGetPickUpRequestDetailResponse
  ): SapWasteProducer | null {
    if (response.KunnrY2 === undefined) return null
    if (response.Kunnry2name === undefined) return null

    return {
      id: response.KunnrY2,
      name: response.Kunnry2name,
      address: this.formatAddress(
        response.Kunnry2street ?? null,
        response.Kunnry2city ?? null,
        response.Kunnry2postcode ?? null,
        response.Kunnry2land ?? null
      )
    }
  }

  static mapPickUpAddresses (
    response: SapGetPickUpRequestDetailResponse
  ): SapPickUpAddress[] {
    if (response.KunnrWe === undefined) return []
    if (response.Kunnrwename === undefined) return []

    return [{
      id: response.KunnrWe,
      name: response.Kunnrwename,
      address: this.formatAddress(
        response.Kunnrwestreet ?? null,
        response.Kunnrwecity ?? null,
        response.Kunnrwepostcode ?? null,
        response.Kunnrweland ?? null
      )
    }]
  }

  static mapContacts (
    contacts: SapGetPickUpRequestContactsResponse[]
  ): Contact[] {
    const result: Contact[] = []

    for (const contact of contacts) {
      if (contact.Email === undefined || contact.Email === '') continue
      if (contact.Name1 === undefined || contact.Name1 === '') continue
      if (contact.Name2 === undefined || contact.Name2 === '') continue

      result.push({
        email: contact.Email,
        firstName: contact.Name1,
        lastName: contact.Name2
      })
    }

    return result
  }

  static formatAddress (
    street: string | null,
    city: string | null,
    postcode: string | null,
    country: string | null
  ): string {
    return `${street?.trim() ?? ''} - ${postcode?.trim() ?? ''} ${city?.trim() ?? ''} - ${country?.trim() ?? ''}`.trim()
  }

  static mapAdditionalFiles (
    attachments: SapGetPickUpRequestAttachmentsResponse[]
  ): ExternalFile[] {
    const result: ExternalFile[] = []

    for (const attachment of attachments) {
      if (attachment.Filename === undefined || attachment.Filename === '') continue

      result.push({
        name: attachment.Filename,
        mimeType: null,
        url: null,
        order: attachment.Posnr !== undefined && attachment.Posnr !== ''
          ? Number(attachment.Posnr)
          : null
      })
    }

    return result
  }

  static mapWasteMaterials (
    details: SapGetPickUpRequestDetailResponse[]
  ): PickUpRequestMaterialWithContractLine[] {
    const filteredDetails = details.filter((detail) => {
      return detail.WasteType !== undefined
        && detail.WasteType !== ''
        && SAP_PICK_UP_WASTE_TYPES.includes(detail.WasteType)
    })

    const materials: PickUpRequestMaterialWithContractLine[] = []

    if (filteredDetails.length === 0) {
      return []
    }

    const isIndascan
    = filteredDetails[0].ExtId !== ''
      && filteredDetails[0].Status !== undefined
      && SAP_INDASCAN_STATUS.includes(filteredDetails[0].Status)

    for (const sapMaterial of filteredDetails) {
      assert(sapMaterial.Vbeln !== undefined)
      assert(sapMaterial.Posnr !== undefined)

      const material: PickUpRequestMaterialWithContractLine = {
        contractLineId: mapSapContractLineId(
          sapMaterial.Vbeln,
          sapMaterial.Posnr,
          sapMaterial.ActionNr
        ),
        contractNumber: sapMaterial.Vbeln,
        contractItem: sapMaterial.Posnr,
        tcNumber: sapMaterial.ActionNr,
        quantityPallets: sapMaterial.NrOPallets !== undefined
          ? sapMaterial.NrOPallets !== ''
            ? Number(sapMaterial.NrOPallets)
            : null
          : undefined,
        estimatedWeightOrVolumeValue: this.mapWeightOrVolumeValue(sapMaterial),
        estimatedWeightOrVolumeUnit: this.mapWeightOrVolumeUnit(sapMaterial),
        costCenter: sapMaterial.CostCenter !== undefined
          ? sapMaterial.CostCenter !== ''
            ? sapMaterial.CostCenter
            : null
          : undefined,
        poNumber: sapMaterial.OrderNumber !== undefined
          ? sapMaterial.OrderNumber !== ''
            ? sapMaterial.OrderNumber
            : null
          : undefined,
        unNumber: sapMaterial.Yyun !== undefined
          ? sapMaterial.Yyun !== ''
            ? sapMaterial.Yyun
            : null
          : undefined,
        packagingType: sapMaterial.TypeRecipient !== undefined
          ? sapMaterial.TypeRecipient !== ''
            ? sapMaterial.TypeRecipient
            : null
          : undefined,
        quantityPackages: sapMaterial.QuantityBarrels !== undefined
          ? sapMaterial.QuantityBarrels !== ''
            ? Number(sapMaterial.QuantityBarrels)
            : null
          : undefined,
        quantityLabels: isIndascan
          ? 0
          : sapMaterial.QuantityLabels !== undefined
            ? sapMaterial.QuantityLabels !== ''
              ? Number(sapMaterial.QuantityLabels)
              : null
            : undefined,
        containerType: sapMaterial.ContainerType !== undefined
          ? sapMaterial.ContainerType !== ''
            ? sapMaterial.ContainerType
            : null
          : undefined,
        containerNumber: sapMaterial.Cont1Nr !== undefined
          ? sapMaterial.Cont1Nr !== ''
            ? sapMaterial.Cont1Nr
            : null
          : undefined,
        containerTransportType: sapMaterial.Cont1Transport !== undefined
          ? sapMaterial.Cont1Transport !== ''
            ? sapMaterial.Cont1Transport
            : null
          : undefined,
        containerVolumeSize: sapMaterial.Cont1Weight !== undefined
          ? sapMaterial.Cont1Weight !== ''
            ? sapMaterial.Cont1Weight
            : null
          : undefined,
        tankerType: sapMaterial.BulkType !== undefined
          ? sapMaterial.BulkType !== ''
            ? sapMaterial.BulkType
            : null
          : undefined,
        isContainerCovered: sapMaterial.Cont1Cover !== undefined
          ? sapMaterial.Cont1Cover !== ''
            ? sapMaterial.Cont1Cover === '01'
            : null
          : undefined,
        position: sapMaterial.Reqpos !== undefined
          ? sapMaterial.Reqpos !== ''
            ? sapMaterial.Reqpos
            : null
          : undefined,
        contractLine: MapContractLineSapService.mapPickUpRequestResultToContractLine(
          sapMaterial
        ),
        processCode: null
      }

      materials.push(material)
    }

    return materials
  }

  static mapPackagingRequestMaterial (
    details: SapGetPickUpRequestDetailResponse[]
  ): PackagingRequestMaterial[] {
    const filteredDetails = details.filter((detail) => {
      return detail.WasteType !== undefined
        && detail.WasteType !== ''
        && SAP_PACKAGE_WASTE_TYPES.includes(detail.WasteType)
    })

    if (filteredDetails.length === 0) {
      return []
    }

    const packagingRequestMaterials: PackagingRequestMaterial[] = []

    for (const sapMaterial of filteredDetails) {
      assert(sapMaterial.Posnr !== undefined)
      assert(sapMaterial.Vbeln !== undefined)

      const material: PackagingRequestMaterial = {
        contractNumber: sapMaterial.Vbeln,
        contractItem: sapMaterial.Posnr,
        contractLineId: mapSapContractLineId(
          sapMaterial.Vbeln,
          sapMaterial.Posnr,
          sapMaterial.ActionNr
        ),
        costCenter: sapMaterial.CostCenter ?? null,
        isSales: false, // How do I parse this back from SAP?
        materialNumber: sapMaterial.Matnr ?? null,
        poNumber: sapMaterial.OrderNumber ?? null,
        quantity: Number(sapMaterial.WeightVolume) || 0,
        wasteMaterial: sapMaterial.Arktx ?? null
      }

      packagingRequestMaterials.push(material)
    }

    return packagingRequestMaterials
  }

  static mapWeightOrVolumeValue (
    sapMaterial: SapGetPickUpRequestDetailResponse
  ): number | null {
    if (sapMaterial.WasteType !== '02') {
      return sapMaterial.WeightVolume !== undefined && sapMaterial.WeightVolume !== ''
        ? Number(sapMaterial.WeightVolume)
        : null
    } else {
      return sapMaterial.Cont1Weight !== undefined && sapMaterial.Cont1Weight !== ''
        ? Number(sapMaterial.Cont1Weight)
        : null
    }
  }

  static mapWeightOrVolumeUnit (
    sapMaterial: SapGetPickUpRequestDetailResponse
  ): WasteMeasurementUnit | null {
    if (sapMaterial.WasteType !== '02') {
      return sapMaterial.WeightUom !== undefined && sapMaterial.WeightUom !== ''
        ? mapSapValueToWasteMeasurementUnit(sapMaterial.WeightUom)
        : null
    } else {
      return sapMaterial.Cont1Uom !== undefined && sapMaterial.Cont1Uom !== ''
        ? mapSapValueToWasteMeasurementUnit(sapMaterial.Cont1Uom)
        : null
    }
  }
}
