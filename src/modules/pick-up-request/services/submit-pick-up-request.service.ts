import assert from 'assert'
import { Injectable } from '@nestjs/common'
import { PickUpRequest } from '../entities/pick-up-request.entity.js'
import { GetCustomerDefaultSalesOrganisationIdUseCase } from '../../customer/use-cases/get-customer-default-sales-organisation-id/get-customer-default-sales-organisation-id.use-case.js'
import { SapCreatePickUpRequestUseCase } from '../../sap/use-cases/create-pick-up-request/create-pick-up-request.use-case.js'
import { CreatePickUpRequestSapMapper } from '../use-cases/create-pick-up-request-sap/create-pick-up-request-sap.mapper.js'
import { UploadPickUpRequestDocumentUseCase } from '../use-cases/upload-pick-up-request-document/upload-pick-up-request-document.use-case.js'

@Injectable()
export class SubmitPickUpRequestService {
  constructor (
    private readonly getCustomerSalesOrganisationId: GetCustomerDefaultSalesOrganisationIdUseCase,
    private readonly sapCreatePickUpRequest: SapCreatePickUpRequestUseCase,
    private readonly pickUpRequestUploadUseCase: UploadPickUpRequestDocumentUseCase
  ) {}

  async execute (
    pickUpRequest: PickUpRequest,
    weeklyPlanningId?: string
  ): Promise<string> {
    assert(pickUpRequest.additionalFiles !== undefined)

    let customerSalesOrganization: string | null = null
    if (pickUpRequest.customerId !== null) {
      customerSalesOrganization = await this.getCustomerSalesOrganisationId.getOrganisationId(
        pickUpRequest.customerId
      )
    }

    const sapCommand = CreatePickUpRequestSapMapper.mapSubmittedPickUpRequestToSapCommand(
      pickUpRequest,
      customerSalesOrganization,
      weeklyPlanningId
    )

    const sapResponse = await this.sapCreatePickUpRequest.execute(sapCommand)

    const files = pickUpRequest.additionalFiles
      .map(file => file.file)
      .filter(file => file !== undefined)

    await this.pickUpRequestUploadUseCase.execute(sapResponse.d.Reqno, files)

    return sapResponse.d.Reqno
  }
}
