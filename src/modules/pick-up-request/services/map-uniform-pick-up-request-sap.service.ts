import { SapGetPickUpRequestIndexResponse } from '../../sap/use-cases/get-pick-up-request-index/get-pick-up-request-index.response.js'
import { mapSapValueToPickUpRequestStatus, PickUpRequestStatus } from '../enums/pick-up-request-status.enum.js'
import { UniformPickUpRequest } from '../types/uniform-pick-up-request.type.js'
import { mapSapValueToTransportMode } from '../enums/pick-up-transport-mode.enum.js'
import { toBoolean } from '../../../utils/transformers/to-boolean.js'
import { SapDateFormatterService } from '../../sap/services/sap-date-formatter.service.js'

export class MapUniformPickUpRequestSapService {
  static mapResultToUniformPickUpRequest (
    response: SapGetPickUpRequestIndexResponse
  ): UniformPickUpRequest {
    return {
      uuid: null,
      createdAt: null,
      requestNumber: response.Reqno !== '' && response.Reqno !== undefined
        ? response.Reqno
        : null,
      status: response.Status !== undefined
        ? mapSapValueToPickUpRequestStatus(response.Status)
        : PickUpRequestStatus.PENDING,
      wasteMaterial: response.Arktx !== '' && response.Arktx !== undefined
        ? [response.Arktx]
        : [],
      customerId: response.Kunnr !== '' && response.Kunnr !== undefined
        ? response.Kunnr
        : null,
      customerName: response.Kunnrname !== '' && response.Kunnrname !== undefined
        ? response.Kunnrname
        : null,
      wasteProducerId: response.Kunnry2 !== '' && response.Kunnry2 !== undefined
        ? response.Kunnry2
        : null,
      wasteProducerName: response.Kunnry2name !== '' && response.Kunnry2name !== undefined
        ? response.Kunnry2name
        : null,
      pickUpAddressId: response.Kunnrwe !== '' && response.Kunnrwe !== undefined
        ? [response.Kunnrwe]
        : [],
      pickUpAddressName: response.Kunnrwename !== '' && response.Kunnrwename !== undefined
        ? response.Kunnrwename
        : null,
      customerReference: response.Yyklantmat !== '' && response.Yyklantmat !== undefined
        ? response.Yyklantmat
        : null,
      contractNumber: response.Vbeln !== '' && response.Vbeln !== undefined
        ? [response.Vbeln]
        : [],
      contractItem: response.Posnr !== '' && response.Posnr !== undefined
        ? [response.Posnr]
        : [],
      transportMode: response.Wastetype !== undefined
        ? mapSapValueToTransportMode(response.Wastetype)
        : null,
      dateOfRequest: response.Dateapplication !== undefined
        ? SapDateFormatterService.parseDateToString(response.Dateapplication)
        : null,
      treatmentCenterName: response.Lifnry0name !== '' && response.Lifnry0name !== undefined
        ? response.Lifnry0name
        : null,
      accountManager: response.Accountmanager !== '' && response.Accountmanager !== undefined
        ? response.Accountmanager
        : null,
      costCenter: response.Costcenter !== '' && response.Costcenter !== undefined
        ? [response.Costcenter]
        : [],
      isTransportByIndaver: response.Transportby !== undefined
        ? toBoolean(response.Transportby)
        : null,
      requestedStartDate: response.Requesteddate !== undefined
        ? SapDateFormatterService.parseDateToString(response.Requesteddate)
        : null,
      requestedEndDate: response.ReqDateTo != null
        ? SapDateFormatterService.parseDateToString(response.ReqDateTo)
        : null,
      confirmedTransportDate: response.Confirmeddate !== undefined
        ? SapDateFormatterService.parseDateToString(response.Confirmeddate)
        : null,
      salesOrder: response.Salesdoc !== '' && response.Salesdoc !== undefined
        ? response.Salesdoc
        : null,
      isHazardous: response.Dangerous !== undefined
        ? [toBoolean(response.Dangerous)]
        : [],
      nameOfApplicant: response.Nameapplicant !== '' && response.Nameapplicant !== undefined
        ? response.Nameapplicant
        : null,
      orderNumber: response.Ordernumber !== '' && response.Ordernumber !== undefined
        ? response.Ordernumber
        : null,
      containerNumber: response.Cont1nr !== '' && response.Cont1nr !== undefined
        ? [response.Cont1nr]
        : [],
      materialAnalysis: response.Materialanalysis !== undefined && response.Materialanalysis !== ''
        ? response.Materialanalysis
        : null,
      deliveryInfo: response.Deliveryinfo !== undefined && response.Deliveryinfo !== ''
        ? response.Deliveryinfo
        : null,
      nameInstallation: response.Lifnry3name !== undefined && response.Lifnry3name !== ''
        ? response.Lifnry3name
        : null,
      disposalCertificateNumber: response.Yyesn !== undefined && response.Yyesn !== ''
        ? response.Yyesn
        : null,
      ewc: response.Eural !== '' && response.Eural !== undefined
        ? response.Eural
        : null,
      tfsNumber: response.Tfsnumber !== '' && response.Tfsnumber !== undefined
        ? response.Tfsnumber
        : null
    }
  }

  static mapResultsToUniformPickUpRequests (
    responses: SapGetPickUpRequestIndexResponse[]
  ): UniformPickUpRequest[] {
    return responses.map(response => this.mapResultToUniformPickUpRequest(response))
  }
}
