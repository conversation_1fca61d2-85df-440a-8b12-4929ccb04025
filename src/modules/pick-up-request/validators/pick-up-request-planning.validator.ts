import { Injectable } from '@nestjs/common'
import dayjs from 'dayjs'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { Repository, In } from 'typeorm'
import { PickUpRequest } from '../entities/pick-up-request.entity.js'
import { DateMustBeAfterError } from '../../exceptions/generic/date-must-be-after.js'
import { MissingRequiredFieldError } from '../../exceptions/generic/missing-required-field.error.js'
import { PickUpRequestPlanningValidationUtil } from '../utils/pick-up-request-planning-validation.util.js'
import { FieldMustBeNullError } from '../../exceptions/generic/field-must-be-null.error.js'
import { CreateFileLinkCommand } from '../../files/commands/create-file-link.command.js'
import { FileNotAccessibleError } from '../../files/errors/file-not-accessible.error.js'
import { UpdatePickUpRequestCommand } from '../use-cases/update-pick-up-request/update-pick-up-request.command.js'
import { File } from '../../files/entities/file.entity.js'
import { AuthContext } from '../../auth/auth.context.js'
import { Contact } from '../../contact/types/contact.type.js'

@Injectable()
export class PickUpRequestPlanningValidator {
  private validationUtil: PickUpRequestPlanningValidationUtil
  private pickUpRequest: Partial<PickUpRequest>
  private errorPointerPrefix: string = '$'

  constructor (
    @InjectRepository(File)
    private readonly fileRepository: Repository<File>,
    private readonly authContext: AuthContext
  ) {}

  async validateUpdate (
    command: UpdatePickUpRequestCommand,
    pickUpRequest: PickUpRequest
  ): Promise<void> {
    this.validationUtil = new PickUpRequestPlanningValidationUtil(pickUpRequest)
    this.pickUpRequest = pickUpRequest

    if (
      pickUpRequest.startDate != null
    ) {
      this.validateStartDate()
    }

    if (pickUpRequest.endDate != null) {
      this.validateEndDate()
    }

    if (
      command.additionalFiles !== undefined
      && command.additionalFiles.length > 0
    ) {
      await this.validateFiles(command.additionalFiles)
    }

    if (pickUpRequest.remarks != null) {
      this.validateRemark(pickUpRequest.remarks, false)
    }

    if (
      pickUpRequest.sendCopyToContacts != null
      && pickUpRequest.sendCopyToContacts.length > 0
    ) {
      this.validateSendCopyToContacts(
        pickUpRequest.sendCopyToContacts,
        false
      )
    }

    if (
      pickUpRequest.startTime != null
    ) {
      this.validateStartTime(
        pickUpRequest.startTime,
        false
      )
    }
  }

  validateSubmit (
    pickUpRequest: PickUpRequest,
    errorPointerPrefix?: string
  ) {
    this.errorPointerPrefix = errorPointerPrefix !== undefined
      ? errorPointerPrefix
      : '$'

    this.validationUtil = new PickUpRequestPlanningValidationUtil(pickUpRequest)
    this.pickUpRequest = pickUpRequest

    this.validateDates()
    this.validateRemark(pickUpRequest.remarks)
    this.validateSendCopyToContacts(pickUpRequest.sendCopyToContacts)
    this.validateStartTime(pickUpRequest.startTime)
    this.validateSubmitWic(pickUpRequest)
  }

  private validateDates () {
    if (this.validationUtil.isStartDateRequired) {
      if (this.pickUpRequest.startDate === null) {
        throw new MissingRequiredFieldError({ pointer: `${this.errorPointerPrefix}.startDate` })
      }
    }

    this.validateStartDate()

    if (this.validationUtil.isEndDateRequired) {
      if (this.pickUpRequest.endDate === null) {
        throw new MissingRequiredFieldError({ pointer: `${this.errorPointerPrefix}.endDate` })
      }
    }

    this.validateEndDate()
  }

  private validateRemark (
    remarks: string | null,
    isSubmit: boolean = true
  ) {
    if (!this.validationUtil.isRemarksAllowed) {
      if (remarks !== null) {
        throw new FieldMustBeNullError({ pointer: `${this.errorPointerPrefix}.remarks` })
      }
    }

    if (isSubmit && this.validationUtil.isRemarksRequired) {
      if (remarks === null) {
        throw new MissingRequiredFieldError({ pointer: `${this.errorPointerPrefix}.remarks` })
      }
    }
  }

  private validateSendCopyToContacts (
    sendCopyToContacts: Contact[],
    isSubmit: boolean = true
  ) {
    if (!this.validationUtil.isSendCopyToContactAllowed) {
      if (sendCopyToContacts.length > 0) {
        throw new FieldMustBeNullError({ pointer: `${this.errorPointerPrefix}.sendCopyToContacts` })
      }
    }

    if (isSubmit && this.validationUtil.isSendCopyToContactRequired) {
      if (sendCopyToContacts.length === 0) {
        throw new MissingRequiredFieldError({ pointer: `${this.errorPointerPrefix}.sendCopyToContacts` })
      }
    }
  }

  private validateStartTime (
    startTime: string | null,
    isSubmit: boolean = true
  ) {
    if (!this.validationUtil.isStartTimeAllowed) {
      if (startTime !== null) {
        throw new FieldMustBeNullError({ pointer: `${this.errorPointerPrefix}.startTime` })
      }
    }

    if (isSubmit && this.validationUtil.isStartTimeRequired) {
      if (startTime === null) {
        throw new MissingRequiredFieldError({ pointer: `${this.errorPointerPrefix}.startTime` })
      }
    }
  }

  private validateEndDate () {
    if (
      !this.validationUtil.isEndDateAllowed
      && this.pickUpRequest.endDate != null
    ) {
      throw new FieldMustBeNullError({ pointer: `${this.errorPointerPrefix}.endDate` })
    }

    if (this.pickUpRequest.startDate == null) {
      throw new MissingRequiredFieldError({ pointer: `${this.errorPointerPrefix}.startDate` })
    }

    if (dayjs(this.pickUpRequest.endDate).isBefore(this.pickUpRequest.startDate)) {
      throw new DateMustBeAfterError(dayjs(this.pickUpRequest.startDate).subtract(1, 'day').format('YYYY-MM-DD'), { pointer: `${this.errorPointerPrefix}.endDate` })
    }
  }

  private validateStartDate () {
    if (
      !this.validationUtil.isStartDateAllowed
      && this.pickUpRequest.startDate != null
    ) {
      throw new FieldMustBeNullError({ pointer: `${this.errorPointerPrefix}.startDate` })
    }

    if (dayjs(this.pickUpRequest.startDate).isBefore(dayjs())) {
      throw new DateMustBeAfterError(dayjs().subtract(1, 'day').format('YYYY-MM-DD'), { pointer: `${this.errorPointerPrefix}.startDate` })
    }
  }

  private async validateFiles (
    files: CreateFileLinkCommand[]
  ): Promise<void> {
    if (
      !this.validationUtil.isAdditionalFilesAllowed
      && files.length > 0
    ) {
      throw new FieldMustBeNullError({ pointer: `${this.errorPointerPrefix}.additionalFiles` })
    }

    const authUserUuid = this.authContext.getUserUuidOrFail()

    const fileCount = await this.fileRepository.countBy({
      uuid: In(files.map(file => file.fileUuid)),
      userUuid: authUserUuid
    })

    if (fileCount !== files.length) {
      throw new FileNotAccessibleError({ pointer: `${this.errorPointerPrefix}.additionalFiles` })
    }
  }

  private validateSubmitWic (
    pickUpRequest: PickUpRequest
  ): void {
    if (!this.validationUtil.isWicRequired) {
      return
    }

    const isWicConfirmed = pickUpRequest.isWicConfirmed ?? false

    if (!isWicConfirmed) {
      throw new MissingRequiredFieldError({ pointer: `${this.errorPointerPrefix}.isWicConfirmed` })
    }
  }
}
