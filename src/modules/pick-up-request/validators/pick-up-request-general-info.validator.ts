import { Injectable } from '@nestjs/common'
import { UserCustomerAuthService } from '../../auth/services/user-customer-auth.service.js'
import { CustomerPickUpAddressAuthService } from '../../auth/services/customer-pick-up-address-auth.service.js'
import { CustomerWasteProducerAuthService } from '../../auth/services/customer-waste-producer-auth.service.js'
import { UserWasteProducerAuthService } from '../../auth/services/user-waste-producer-auth.service.js'
import { PickUpRequest } from '../entities/pick-up-request.entity.js'
import { PickUpRequestGeneralInfoValidationUtil } from '../utils/pick-up-request-general-info-validation.util.js'
import { CustomerNotProvidedError } from '../../customer/errors/customer-not-provided.error.js'
import { PickUpAddressNotAccessibleError } from '../../pick-up-address/errors/pick-up-address-not-accessible.error.js'
import { WasteProducerNotAccessibleError } from '../../waste-producer/errors/waste-producer-not-accessible.error.js'
import { UpdatePickUpRequestCommand } from '../use-cases/update-pick-up-request/update-pick-up-request.command.js'
import { MissingRequiredFieldError } from '../../exceptions/generic/missing-required-field.error.js'
import { FieldMustBeNullError } from '../../exceptions/generic/field-must-be-null.error.js'
import { AuthContext } from '../../auth/auth.context.js'
import { SelectedCustomerFilterMismatchError } from '../../customer/errors/selected-customer-filter-mismatch.error.js'

@Injectable()
export class PickUpRequestGeneralInfoValidator {
  constructor (
    private readonly authContext: AuthContext,
    private readonly userCustomerAuthService: UserCustomerAuthService,
    private readonly customerWasteProducerAuthService: CustomerWasteProducerAuthService,
    private readonly userWasteProducerAuthService: UserWasteProducerAuthService,
    private readonly customerPickUpAddressAuthService: CustomerPickUpAddressAuthService
  ) {}

  private errorPointerPrefix: string = '$'
  private validationUtil: PickUpRequestGeneralInfoValidationUtil

  async validateUpdate (
    command: UpdatePickUpRequestCommand,
    pickUpRequest: PickUpRequest
  ): Promise<void> {
    this.validationUtil = new PickUpRequestGeneralInfoValidationUtil(
      pickUpRequest,
      false
    )

    if (command.customerId != null) {
      this.canAccessCustomerId(command.customerId)
    }
    if (command.wasteProducerId != null) {
      await this.canAccessWasteProducerId(
        command.wasteProducerId,
        command?.customerId ?? pickUpRequest.customerId ?? null
      )
    }
    if (command.pickUpAddressIds != null) {
      await this.canAccessPickUpAddresses(
        command.pickUpAddressIds,
        command?.customerId ?? pickUpRequest.customerId ?? null
      )
    }

    this.validateCustomerId(command.customerId ?? null, false)
    this.validateWasteProducerId(command.wasteProducerId ?? null, false)
    this.validatePickUpAddressIds(command.pickUpAddressIds ?? [], false)
  }

  async validateSubmit (
    pickUpRequest: PickUpRequest,
    errorPointerPrefix?: string
  ): Promise<void> {
    this.errorPointerPrefix = errorPointerPrefix !== undefined
      ? errorPointerPrefix
      : '$'

    this.validationUtil = new PickUpRequestGeneralInfoValidationUtil(
      pickUpRequest,
      true
    )

    // CustomerId
    this.validateCustomerId(pickUpRequest.customerId)

    if (pickUpRequest.customerId !== null) {
      this.canAccessCustomerId(pickUpRequest.customerId)
    }

    // wasteProducerId
    this.validateWasteProducerId(pickUpRequest.wasteProducerId)

    if (pickUpRequest.wasteProducerId !== null) {
      await this.canAccessWasteProducerId(
        pickUpRequest.wasteProducerId,
        pickUpRequest.customerId
      )
    }

    // pickUpAddressIds
    this.validatePickUpAddressIds(pickUpRequest.pickUpAddressIds)

    if (pickUpRequest.pickUpAddressIds !== null) {
      await this.canAccessPickUpAddresses(
        pickUpRequest.pickUpAddressIds,
        pickUpRequest.customerId
      )
    }
  }

  private validateCustomerId (
    customerId: string | null,
    isSubmit: boolean = true
  ) {
    if (!this.validationUtil.isCustomerIdAllowed) {
      if (customerId !== null) {
        throw new FieldMustBeNullError({ pointer: `${this.errorPointerPrefix}.customerId` })
      }
    }

    if (isSubmit && this.validationUtil.isCustomerIdRequired) {
      if (customerId === null) {
        throw new MissingRequiredFieldError({ pointer: `${this.errorPointerPrefix}.customerId` })
      }
    }
  }

  private validateWasteProducerId (
    wasteProducerId: string | null,
    isSubmit: boolean = true
  ) {
    if (!this.validationUtil.isWasteProducerIdAllowed) {
      if (wasteProducerId !== null) {
        throw new FieldMustBeNullError({ pointer: `${this.errorPointerPrefix}.wasteProducerId` })
      }
    }

    if (isSubmit && this.validationUtil.isWasteProducerIdRequired) {
      if (wasteProducerId === null) {
        throw new MissingRequiredFieldError({ pointer: `${this.errorPointerPrefix}.wasteProducerId` })
      }
    }
  }

  private validatePickUpAddressIds (
    pickUpAddressIds: string[],
    isSubmit: boolean = true
  ) {
    if (!this.validationUtil.isPickUpAddressIdsAllowed) {
      if (pickUpAddressIds.length > 0) {
        throw new FieldMustBeNullError({ pointer: `${this.errorPointerPrefix}.pickUpAddressIds` })
      }
    }

    if (isSubmit && this.validationUtil.isPickUpAddressIdsRequired) {
      if (pickUpAddressIds.length === 0) {
        throw new MissingRequiredFieldError({ pointer: `${this.errorPointerPrefix}$.pickUpAddressIds` })
      }
    }
  }

  private canAccessCustomerId (customerId: string): void {
    const selectedCustomerId = this.authContext.getSelectedCustomerId()
    if (selectedCustomerId != null && selectedCustomerId !== customerId) {
      throw new SelectedCustomerFilterMismatchError({ pointer: '$.customerId' })
    }
  }

  private async canAccessWasteProducerId (
    wasteProducerId: string,
    customerId: string | null
  ): Promise<void> {
    const pointer = `${this.errorPointerPrefix}.wasteProducerId`

    if (customerId === null) {
      throw new CustomerNotProvidedError({ pointer })
    }

    const userId = this.authContext.getAzureEntraUpn()

    const canCustomerAccessWasteProducer = await this.customerWasteProducerAuthService
      .canCustomerAccessWasteProducer(
        customerId,
        wasteProducerId
      )

    if (!canCustomerAccessWasteProducer) {
      throw new WasteProducerNotAccessibleError({ pointer })
    }

    const canUserAccessWasteProducer = await this.userWasteProducerAuthService
      .canUserAccessWasteProducer(
        userId,
        customerId,
        wasteProducerId
      )

    if (!canUserAccessWasteProducer) {
      throw new WasteProducerNotAccessibleError({ pointer })
    }
  }

  private async canAccessPickUpAddresses (
    pickUpAddressIds: string[],
    customerId: string | null
  ): Promise<void> {
    if (pickUpAddressIds.length === 0) return

    const pointer = `${this.errorPointerPrefix}.pickUpAddressIds`

    if (customerId === null) {
      throw new CustomerNotProvidedError({ pointer })
    }

    const canCustomerAccessPickUpAddress = await this.customerPickUpAddressAuthService
      .canCustomerAccessPickUpAddresses(
        customerId,
        pickUpAddressIds
      )

    if (!canCustomerAccessPickUpAddress) {
      throw new PickUpAddressNotAccessibleError({ pointer })
    }
  }
}
