import { before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import { expect } from 'expect'
import dayjs from 'dayjs'
import { createStubInstance, SinonStubbedInstance } from 'sinon'
import { Repository } from 'typeorm'
import { UpdatePickUpRequestCommandBuilder } from '../../use-cases/update-pick-up-request/tests/update-pick-up-request-command.builder.js'
import { TestBench } from '../../../../../test/setup/test-bench.js'
import { PickUpRequestPlanningValidator } from '../pick-up-request-planning.validator.js'
import { DateMustBeAfterError } from '../../../exceptions/generic/date-must-be-after.js'
import { UpdatePickUpRequestMapper } from '../../use-cases/update-pick-up-request/mappers/update-pick-up-request.mapper.js'
import { PickUpRequest } from '../../entities/pick-up-request.entity.js'
import { PickUpRequestEntityBuilder } from '../../tests/pick-up-request-entity.builder.js'
import { ValidPickUpRequestEntityBuilder } from '../../tests/valid-pick-up-request-entity.builder.js'
import { File } from '../../../files/entities/file.entity.js'
import { FileNotAccessibleError } from '../../../files/errors/file-not-accessible.error.js'
import { MissingRequiredFieldError } from '../../../exceptions/generic/missing-required-field.error.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { FieldMustBeNullError } from '../../../exceptions/generic/field-must-be-null.error.js'
import { WIC_PROCESS_CODE } from '../../constants/wic-process-code.constant.js'
import { PickUpRequestMaterialBuilder } from '../../tests/pick-up-request-material.builder.js'
import { PickUpRequestMaterial } from '../../types/pick-up-request-material.type.js'

describe('Pick-up request file validator unit test', () => {
  let validator: PickUpRequestPlanningValidator

  let pickUpRequest: PickUpRequest
  let commandBuilder: UpdatePickUpRequestCommandBuilder

  let pickUpRequestBuilder: ValidPickUpRequestEntityBuilder
  let fileRepository: SinonStubbedInstance<Repository<File>>

  let wicMaterial: PickUpRequestMaterial

  before(() => {
    TestBench.setupUnitTest()

    const authContext = createStubInstance(AuthContext)

    fileRepository = createStubInstance<Repository<File>>(
      Repository<File>, {
        countBy: Promise.resolve(1)
      }
    )

    pickUpRequest = new PickUpRequestEntityBuilder().build()
    commandBuilder = new UpdatePickUpRequestCommandBuilder()
    pickUpRequestBuilder = new ValidPickUpRequestEntityBuilder()

    validator = new PickUpRequestPlanningValidator(
      fileRepository,
      authContext
    )

    wicMaterial = new PickUpRequestMaterialBuilder()
      .withProcessCode(WIC_PROCESS_CODE)
      .build()
  })

  describe('Pick up request planning', () => {
    describe('Update pick up request planning', () => {
      it('throws error when start date in the past', async () => {
        const command = commandBuilder
          .withStartDate(dayjs().subtract(1, 'day').format('YYYY-MM-DD'))
          .build()

        const mergedPickUpRequest = UpdatePickUpRequestMapper.mapToMergedPickUpRequest(
          command,
          pickUpRequest
        )

        await expect(validator.validateUpdate(
          command,
          mergedPickUpRequest
        ))
          .rejects.toThrow(DateMustBeAfterError)
      })

      it('throws an error when start is not given', async () => {
        const command = commandBuilder
          .withEndDate(dayjs().add(1, 'day').format('YYYY-MM-DD'))
          .build()

        const mergedPickUpRequest = UpdatePickUpRequestMapper.mapToMergedPickUpRequest(
          command,
          pickUpRequest
        )

        await expect(validator.validateUpdate(
          command,
          mergedPickUpRequest
        ))
          .rejects.toThrow(MissingRequiredFieldError)
      })

      it('throws an error when end date is before start date', async () => {
        const command = commandBuilder
          .withStartDate(dayjs().add(2, 'day').format('YYYY-MM-DD'))
          .withEndDate(dayjs().add(1, 'day').format('YYYY-MM-DD'))
          .build()

        const mergedPickUpRequest = UpdatePickUpRequestMapper.mapToMergedPickUpRequest(
          command,
          pickUpRequest
        )

        await expect(validator.validateUpdate(
          command,
          mergedPickUpRequest
        ))
          .rejects.toThrow(DateMustBeAfterError)
      })

      it('does not throw an error when files are found', async () => {
        const command = commandBuilder
          .withStartDate(dayjs().add(2, 'day').format('YYYY-MM-DD'))
          .addAdditionalFile({
            fileUuid: randomUUID(),
            order: 1
          })
          .build()

        const mergedPickUpRequest = UpdatePickUpRequestMapper.mapToMergedPickUpRequest(
          command,
          pickUpRequest
        )

        await expect(validator.validateUpdate(command, mergedPickUpRequest))
          .resolves.not.toThrow()
      })

      it('throws an error when file can not be found', async () => {
        const command = commandBuilder
          .withStartDate(dayjs().add(2, 'day').format('YYYY-MM-DD'))
          .addAdditionalFile({
            fileUuid: randomUUID(),
            order: 1
          })
          .build()

        const mergedPickUpRequest = UpdatePickUpRequestMapper.mapToMergedPickUpRequest(
          command,
          pickUpRequest
        )

        fileRepository.countBy.resolves(0)

        await expect(validator.validateUpdate(command, mergedPickUpRequest))
          .rejects.toThrow(FileNotAccessibleError)
      })

      it(`throws an error when remark is given for weekly planning request's pick-up request`, async () => {
        const command = commandBuilder
          .withRemark('test')
          .build()

        const pickUpRequest = new PickUpRequestEntityBuilder()
          .withWeeklyPlanningRequestUuid(randomUUID())
          .build()

        const mergedPickUpRequest = UpdatePickUpRequestMapper.mapToMergedPickUpRequest(
          command,
          pickUpRequest
        )

        await expect(validator.validateUpdate(command, mergedPickUpRequest))
          .rejects.toThrow(FieldMustBeNullError)
      })

      it(`throws an error when end date is given for weekly planning request's pick-up request`, async () => {
        const command = commandBuilder
          .withEndDate(dayjs().add(3, 'day').format('YYYY-MM-DD'))
          .build()

        const pickUpRequest = new PickUpRequestEntityBuilder()
          .withWeeklyPlanningRequestUuid(randomUUID())
          .build()

        const mergedPickUpRequest = UpdatePickUpRequestMapper.mapToMergedPickUpRequest(
          command,
          pickUpRequest
        )

        await expect(validator.validateUpdate(command, mergedPickUpRequest))
          .rejects.toThrow(FieldMustBeNullError)
      })

      it(`throws an error when contact is given for weekly planning request's pick-up request`, async () => {
        const command = commandBuilder
          .addSendCopyToContacts({
            email: '<EMAIL>',
            firstName: 'John',
            lastName: 'Doe'
          })
          .build()

        const pickUpRequest = new PickUpRequestEntityBuilder()
          .withWeeklyPlanningRequestUuid(randomUUID())
          .build()

        const mergedPickUpRequest = UpdatePickUpRequestMapper.mapToMergedPickUpRequest(
          command,
          pickUpRequest
        )

        await expect(validator.validateUpdate(command, mergedPickUpRequest))
          .rejects.toThrow(FieldMustBeNullError)
      })

      it(`throws an error when start time is given`, async () => {
        const command = commandBuilder
          .withStartTime(dayjs().format('hh:mm:ss'))
          .build()

        const mergedPickUpRequest = UpdatePickUpRequestMapper.mapToMergedPickUpRequest(
          command,
          pickUpRequest
        )

        await expect(validator.validateUpdate(command, mergedPickUpRequest))
          .rejects.toThrow(FieldMustBeNullError)
      })
    })

    describe('Submit pick up request planning', () => {
      it('throws error when start date in the past', () => {
        const pickUpRequest = pickUpRequestBuilder
          .withStartDate(dayjs().subtract(1, 'day').format('YYYY-MM-DD'))
          .build()

        expect(() => validator.validateSubmit(pickUpRequest))
          .toThrow(DateMustBeAfterError)
      })

      it('throws an error when start date is not given', () => {
        const pickUpRequest = pickUpRequestBuilder
          .withEndDate(dayjs().add(1, 'day').format('YYYY-MM-DD'))
          .build()

        pickUpRequest.startDate = null

        expect(() => validator.validateSubmit(pickUpRequest))
          .toThrow(MissingRequiredFieldError)
      })

      it('throws an error when end date is before start date', () => {
        const pickUpRequest = pickUpRequestBuilder
          .withStartDate(dayjs().add(2, 'day').format('YYYY-MM-DD'))
          .withEndDate(dayjs().add(1, 'day').format('YYYY-MM-DD'))
          .build()

        expect(() => validator.validateSubmit(pickUpRequest))
          .toThrow(DateMustBeAfterError)
      })

      it('throws an error when wic needs confirmation and isWicConfirmed is null', () => {
        const pickUpRequest = pickUpRequestBuilder
          .withMaterials([{
            contractLine: null,
            ...wicMaterial
          }])
          .withIsWicConfirmed(null)
          .build()

        expect(() => validator.validateSubmit(pickUpRequest))
          .toThrow(MissingRequiredFieldError)
      })

      it('throws an error when wic needs confirmation and isWicConfirmed is false', () => {
        const pickUpRequest = pickUpRequestBuilder
          .withMaterials([{
            contractLine: null,
            ...new PickUpRequestMaterialBuilder()
              .withProcessCode(WIC_PROCESS_CODE)
              .build()
          }])
          .withIsWicConfirmed(false)
          .build()

        expect(() => validator.validateSubmit(pickUpRequest))
          .toThrow(MissingRequiredFieldError)
      })

      it('throws no error when wic needs no confirmation', () => {
        const pickUpRequest = pickUpRequestBuilder
          .withMaterials([])
          .withIsWicConfirmed(false)
          .build()

        expect(() => validator.validateSubmit(pickUpRequest))
          .not
          .toThrow()
      })

      it('throws no error when wic needs confirmation and wic is confirmed', () => {
        const pickUpRequest = pickUpRequestBuilder
          .withMaterials([{
            contractLine: null,
            ...new PickUpRequestMaterialBuilder()
              .withProcessCode(WIC_PROCESS_CODE)
              .build()
          }])
          .withIsWicConfirmed(true)
          .build()

        expect(() => validator.validateSubmit(pickUpRequest))
          .not
          .toThrow()
      })
    })
  })
})
