import { before, describe, it, afterEach } from 'node:test'
import { randomUUID } from 'crypto'
import { expect } from 'expect'
import { SinonStubbedInstance, createStubInstance } from 'sinon'
import { randCountryCode } from '@ngneat/falso'
import { TestBench } from '../../../../../test/setup/test-bench.js'
import { PickUpRequest } from '../../entities/pick-up-request.entity.js'
import { PickUpTransportMode } from '../../enums/pick-up-transport-mode.enum.js'
import { PickUpRequestMaterialCommand } from '../../use-cases/update-pick-up-request/commands/pick-up-request-material.command.js'
import { UpdatePickUpRequestMaterialMapper } from '../../use-cases/update-pick-up-request/mappers/update-pick-up-request-material.mapper.js'
import { UpdatePickUpRequestMaterialCommandBuilder } from '../../use-cases/update-pick-up-request/tests/update-pick-up-request-material-command.builder.js'
import { PickUpRequestTransportValidator } from '../pick-up-request-transport.validator.js'
import { FieldMustBeNullError } from '../../../exceptions/generic/field-must-be-null.error.js'
import { PickUpRequestEntityBuilder } from '../../tests/pick-up-request-entity.builder.js'
import { InvalidTotalQuantityPalletsError } from '../../errors/invalid-total-quantity-pallets.error.js'
import { MissingRequiredFieldError } from '../../../exceptions/generic/missing-required-field.error.js'
import { PickUpRequestMaterialBuilder } from '../../tests/pick-up-request-material.builder.js'
import { ValidPickUpRequestMaterialBuilder } from '../../tests/valid-pick-up-request-material.builder.js'
import { ValidPickUpRequestEntityBuilder } from '../../tests/valid-pick-up-request-entity.builder.js'
import { InvalidTransportModeError } from '../../errors/invalid-transport-mode.error.js'
import { SapGetIsPoReferenceRequiredUseCase } from '../../../sap/use-cases/get-is-po-reference-required/get-is-po-reference-required.use-case.js'
import { SapGetIsCostCenterRequiredUseCase } from '../../../sap/use-cases/get-is-cost-center-required/get-is-cost-center-required.use-case.js'
import { ViewCustomerCountryUseCase } from '../../../customer/use-cases/view-customer-country/view-customer-country.use-case.js'
import { ViewCustomerCountryResponse } from '../../../customer/use-cases/view-customer-country/view-customer-country.response.js'
import { SAP_COUNTRY_CODE } from '../../../sap/constants/country-code.constant.js'
import { WasteMeasurementUnit } from '../../../waste-inquiry/enums/waste-measurement-unit.enum.js'

describe('Pick-up request transport validator unit test', () => {
  let validator: PickUpRequestTransportValidator

  let sapGetIsPoReferenceRequiredUseCase: SinonStubbedInstance<SapGetIsPoReferenceRequiredUseCase>
  let sapGetIsCostCenterRequiredUseCase: SinonStubbedInstance<SapGetIsCostCenterRequiredUseCase>
  let viewCustomerCountryUseCase: SinonStubbedInstance<ViewCustomerCountryUseCase>

  let pickUpRequestBuilder: PickUpRequestEntityBuilder
  let materialCommandBuilder: UpdatePickUpRequestMaterialCommandBuilder
  let materialBuilder: PickUpRequestMaterialBuilder

  let validMaterialBuilder: ValidPickUpRequestMaterialBuilder
  let validPickUpRequestBuilder: ValidPickUpRequestEntityBuilder

  let pickUpRequest: PickUpRequest

  before(() => {
    TestBench.setupUnitTest()

    pickUpRequestBuilder = new PickUpRequestEntityBuilder()
    materialCommandBuilder = new UpdatePickUpRequestMaterialCommandBuilder()
    materialBuilder = new PickUpRequestMaterialBuilder()
    validMaterialBuilder = new ValidPickUpRequestMaterialBuilder()
    validPickUpRequestBuilder = new ValidPickUpRequestEntityBuilder()

    sapGetIsPoReferenceRequiredUseCase = createStubInstance(SapGetIsPoReferenceRequiredUseCase)
    sapGetIsCostCenterRequiredUseCase = createStubInstance(SapGetIsCostCenterRequiredUseCase)
    viewCustomerCountryUseCase = createStubInstance(ViewCustomerCountryUseCase)

    validator = new PickUpRequestTransportValidator(
      sapGetIsPoReferenceRequiredUseCase,
      sapGetIsCostCenterRequiredUseCase,
      viewCustomerCountryUseCase
    )
  })

  function createPickUpRequest (
    materialCommand: PickUpRequestMaterialCommand,
    transportMode: PickUpTransportMode
  ) {
    const material = UpdatePickUpRequestMaterialMapper.mapToMaterial(materialCommand)

    pickUpRequest = pickUpRequestBuilder
      .addMaterial(material)
      .withTransportMode(transportMode)
      .build()
  }

  afterEach(() => {
    mockMethods()
  })

  function mockMethods () {
    sapGetIsPoReferenceRequiredUseCase.execute.resolves(false)
    sapGetIsCostCenterRequiredUseCase.execute.resolves(false)
    viewCustomerCountryUseCase.execute.resolves(new ViewCustomerCountryResponse(randCountryCode()))
  }

  describe('Update validation', () => {
    describe('Packaged with curtain sider or Truck', () => {
      it(`throws an error when for weekly planning request's pick up request`, () => {
        const materialCommand = materialCommandBuilder
          .withContainerType('test')
          .build()

        const material = UpdatePickUpRequestMaterialMapper.mapToMaterial(materialCommand)

        createPickUpRequest(
          material,
          PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK
        )

        pickUpRequest.weeklyPlanningRequestUuid = randomUUID()

        expect(validator.validateUpdate(pickUpRequest)).rejects.toThrow(InvalidTransportModeError)
      })

      it('throws an error when container type is given', () => {
        const materialCommand = materialCommandBuilder
          .withContainerType('test')
          .build()

        const material = UpdatePickUpRequestMaterialMapper.mapToMaterial(materialCommand)

        createPickUpRequest(
          material,
          PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK
        )

        expect(validator.validateUpdate(pickUpRequest)).rejects
          .toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.containerType' }
          }))
      })

      it('throws an error when container transport type is given', () => {
        const materialCommand = materialCommandBuilder
          .withContainerTransportType('07')
          .build()

        const material = UpdatePickUpRequestMaterialMapper.mapToMaterial(materialCommand)

        createPickUpRequest(
          material,
          PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK
        )

        expect(validator.validateUpdate(pickUpRequest)).rejects
          .toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.containerTransportType' }
          }))
      })

      it('throws an error when container covered is given', () => {
        const materialCommand = materialCommandBuilder
          .withIsContainerCovered(true)
          .build()

        const material = UpdatePickUpRequestMaterialMapper.mapToMaterial(materialCommand)

        createPickUpRequest(
          material,
          PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK
        )

        expect(validator.validateUpdate(pickUpRequest)).rejects
          .toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.isContainerCovered' }
          }))
      })

      it('throws an error when containerNumber is given', () => {
        const materialCommand = materialCommandBuilder
          .withContainerNumber('1')
          .build()

        const material = UpdatePickUpRequestMaterialMapper.mapToMaterial(materialCommand)

        createPickUpRequest(
          material,
          PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK
        )

        expect(validator.validateUpdate(pickUpRequest)).rejects
          .toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.containerNumber' }
          }))
      })

      it('throws an error when tanker type is given', () => {
        const materialCommand = materialCommandBuilder
          .withTankerType('01')
          .build()

        const material = UpdatePickUpRequestMaterialMapper.mapToMaterial(materialCommand)

        createPickUpRequest(
          material,
          PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK
        )

        expect(validator.validateUpdate(pickUpRequest)).rejects
          .toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.tankerType' }
          }))
      })
    })

    describe('Bulk waste in skips or containers', () => {
      it('throws an error when package type is given', () => {
        const materialCommand = materialCommandBuilder
          .withPackagingType('test')
          .build()

        const material = UpdatePickUpRequestMaterialMapper.mapToMaterial(materialCommand)

        createPickUpRequest(
          material,
          PickUpTransportMode.BULK_SKIPS_CONTAINER
        )

        expect(validator.validateUpdate(pickUpRequest)).rejects
          .toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.packagingType' }
          }))
      })

      it('throws an error when quantity of packages type is given', () => {
        const materialCommand = materialCommandBuilder
          .withQuantityPackages(1)
          .build()

        const material = UpdatePickUpRequestMaterialMapper.mapToMaterial(materialCommand)

        createPickUpRequest(
          material,
          PickUpTransportMode.BULK_SKIPS_CONTAINER
        )

        expect(validator.validateUpdate(pickUpRequest)).rejects
          .toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.quantityPackages' }
          }))
      })

      it('throws an error when quantity of labels is given', () => {
        const materialCommand = materialCommandBuilder
          .withQuantityLabels(1)
          .build()

        const material = UpdatePickUpRequestMaterialMapper.mapToMaterial(materialCommand)

        createPickUpRequest(
          material,
          PickUpTransportMode.BULK_SKIPS_CONTAINER
        )

        expect(validator.validateUpdate(pickUpRequest)).rejects
          .toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.quantityLabels' }
          }))
      })

      it('throws an error when quantity of pallets is given', () => {
        const materialCommand = materialCommandBuilder
          .withQuantityPallets(1)
          .build()

        const material = UpdatePickUpRequestMaterialMapper.mapToMaterial(materialCommand)

        createPickUpRequest(
          material,
          PickUpTransportMode.BULK_SKIPS_CONTAINER
        )

        expect(validator.validateUpdate(pickUpRequest)).rejects
          .toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.quantityPallets' }
          }))
      })

      it('throws an error when tanker type is given', () => {
        const materialCommand = materialCommandBuilder
          .withTankerType('01')
          .build()

        const material = UpdatePickUpRequestMaterialMapper.mapToMaterial(materialCommand)

        createPickUpRequest(
          material,
          PickUpTransportMode.BULK_SKIPS_CONTAINER
        )

        expect(validator.validateUpdate(pickUpRequest)).rejects
          .toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.tankerType' }
          }))
      })

      it('throws an error when packaging remark is given', () => {
        const materialCommand = materialCommandBuilder
          .build()

        const material = UpdatePickUpRequestMaterialMapper.mapToMaterial(materialCommand)

        const pickUpRequest = pickUpRequestBuilder
          .addMaterial(material)
          .withPackagingRemark('test')
          .withTransportMode(PickUpTransportMode.BULK_SKIPS_CONTAINER)
          .build()

        expect(validator.validateUpdate(pickUpRequest)).rejects
          .toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.packagingRemark' }
          }))
      })

      it('throws an error when total quantity of pallets is given', () => {
        const materialCommand = materialCommandBuilder
          .build()

        const material = UpdatePickUpRequestMaterialMapper.mapToMaterial(materialCommand)

        const pickUpRequest = pickUpRequestBuilder
          .addMaterial(material)
          .withTotalQuantityPallets(5)
          .withTransportMode(PickUpTransportMode.BULK_SKIPS_CONTAINER)
          .build()

        expect(validator.validateUpdate(pickUpRequest)).rejects
          .toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.totalQuantityPallets' }
          }))
      })

      it('throws an error when return packaging is given', () => {
        const materialCommand = materialCommandBuilder
          .build()

        const material = UpdatePickUpRequestMaterialMapper.mapToMaterial(materialCommand)

        const pickUpRequest = pickUpRequestBuilder
          .addMaterial(material)
          .withIsReturnPackaging(true)
          .withTransportMode(PickUpTransportMode.BULK_SKIPS_CONTAINER)
          .build()

        expect(validator.validateUpdate(pickUpRequest)).rejects
          .toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.isReturnPackaging' }
          }))
      })
    })

    describe('Bulk waste in vacuum tank or road tanker', () => {
      it('throws an error when package type is given', () => {
        const materialCommand = materialCommandBuilder
          .withPackagingType('test')
          .build()

        const material = UpdatePickUpRequestMaterialMapper.mapToMaterial(materialCommand)

        createPickUpRequest(
          material,
          PickUpTransportMode.BULK_VACUUM_TANKERS_ROAD_TANKERS
        )

        expect(validator.validateUpdate(pickUpRequest)).rejects
          .toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.packagingType' }
          }))
      })

      it('throws an error when quantity of packages type is given', () => {
        const materialCommand = materialCommandBuilder
          .withQuantityPackages(1)
          .build()

        const material = UpdatePickUpRequestMaterialMapper.mapToMaterial(materialCommand)

        createPickUpRequest(
          material,
          PickUpTransportMode.BULK_VACUUM_TANKERS_ROAD_TANKERS
        )

        expect(validator.validateUpdate(pickUpRequest)).rejects
          .toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.quantityPackages' }
          }))
      })

      it('throws an error when quantity of labels is given', () => {
        const materialCommand = materialCommandBuilder
          .withQuantityLabels(1)
          .build()

        const material = UpdatePickUpRequestMaterialMapper.mapToMaterial(materialCommand)

        createPickUpRequest(
          material,
          PickUpTransportMode.BULK_VACUUM_TANKERS_ROAD_TANKERS
        )

        expect(validator.validateUpdate(pickUpRequest)).rejects
          .toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.quantityLabels' }
          }))
      })

      it('throws an error when quantity of pallets is given', () => {
        const materialCommand = materialCommandBuilder
          .withQuantityPallets(1)
          .build()

        const material = UpdatePickUpRequestMaterialMapper.mapToMaterial(materialCommand)

        createPickUpRequest(
          material,
          PickUpTransportMode.BULK_VACUUM_TANKERS_ROAD_TANKERS
        )

        expect(validator.validateUpdate(pickUpRequest)).rejects
          .toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.quantityPallets' }
          }))
      })

      it('throws an error when container type is given', () => {
        const materialCommand = materialCommandBuilder
          .withContainerType('test')
          .build()

        const material = UpdatePickUpRequestMaterialMapper.mapToMaterial(materialCommand)

        createPickUpRequest(
          material,
          PickUpTransportMode.BULK_VACUUM_TANKERS_ROAD_TANKERS
        )

        expect(validator.validateUpdate(pickUpRequest)).rejects
          .toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.containerType' }
          }))
      })

      it('throws an error when container transport type is given', () => {
        const materialCommand = materialCommandBuilder
          .withContainerTransportType('07')
          .build()

        const material = UpdatePickUpRequestMaterialMapper.mapToMaterial(materialCommand)

        createPickUpRequest(
          material,
          PickUpTransportMode.BULK_VACUUM_TANKERS_ROAD_TANKERS
        )

        expect(validator.validateUpdate(pickUpRequest)).rejects
          .toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.containerTransportType' }
          }))
      })

      it('throws an error when container covered is given', () => {
        const materialCommand = materialCommandBuilder
          .withIsContainerCovered(true)
          .build()

        const material = UpdatePickUpRequestMaterialMapper.mapToMaterial(materialCommand)

        createPickUpRequest(
          material,
          PickUpTransportMode.BULK_VACUUM_TANKERS_ROAD_TANKERS
        )

        expect(validator.validateUpdate(pickUpRequest)).rejects
          .toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.isContainerCovered' }
          }))
      })

      it('throws an error when containerNumber is given', () => {
        const materialCommand = materialCommandBuilder
          .withContainerNumber('1')
          .build()

        const material = UpdatePickUpRequestMaterialMapper.mapToMaterial(materialCommand)

        createPickUpRequest(
          material,
          PickUpTransportMode.BULK_VACUUM_TANKERS_ROAD_TANKERS
        )

        expect(validator.validateUpdate(pickUpRequest)).rejects
          .toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.containerNumber' }
          }))
      })

      it('throws an error when packaging remark is given', () => {
        const materialCommand = materialCommandBuilder
          .build()

        const material = UpdatePickUpRequestMaterialMapper.mapToMaterial(materialCommand)

        const pickUpRequest = pickUpRequestBuilder
          .addMaterial(material)
          .withPackagingRemark('test')
          .withTransportMode(PickUpTransportMode.BULK_VACUUM_TANKERS_ROAD_TANKERS)
          .build()

        expect(validator.validateUpdate(pickUpRequest)).rejects
          .toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.packagingRemark' }
          }))
      })

      it('throws an error when total quantity of pallets is given', () => {
        const materialCommand = materialCommandBuilder
          .build()

        const material = UpdatePickUpRequestMaterialMapper.mapToMaterial(materialCommand)

        const pickUpRequest = pickUpRequestBuilder
          .addMaterial(material)
          .withTotalQuantityPallets(5)
          .withTransportMode(PickUpTransportMode.BULK_VACUUM_TANKERS_ROAD_TANKERS)
          .build()

        expect(validator.validateUpdate(pickUpRequest)).rejects
          .toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.totalQuantityPallets' }
          }))
      })

      it('throws an error when return packaging is given', () => {
        const materialCommand = materialCommandBuilder
          .build()

        const material = UpdatePickUpRequestMaterialMapper.mapToMaterial(materialCommand)

        const pickUpRequest = pickUpRequestBuilder
          .addMaterial(material)
          .withIsReturnPackaging(true)
          .withTransportMode(PickUpTransportMode.BULK_VACUUM_TANKERS_ROAD_TANKERS)
          .build()

        expect(validator.validateUpdate(pickUpRequest)).rejects
          .toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.isReturnPackaging' }
          }))
      })
    })

    describe('Tank container or ISO tank', () => {
      it('throws an error when package type is given', () => {
        const materialCommand = materialCommandBuilder
          .withPackagingType('test')
          .build()

        const material = UpdatePickUpRequestMaterialMapper.mapToMaterial(materialCommand)

        createPickUpRequest(
          material,
          PickUpTransportMode.BULK_ISO_TANK
        )

        expect(validator.validateUpdate(pickUpRequest)).rejects
          .toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.packagingType' }
          }))
      })

      it('throws an error when quantity of packages type is given', () => {
        const materialCommand = materialCommandBuilder
          .withQuantityPackages(1)
          .build()

        const material = UpdatePickUpRequestMaterialMapper.mapToMaterial(materialCommand)

        createPickUpRequest(
          material,
          PickUpTransportMode.BULK_ISO_TANK
        )

        expect(validator.validateUpdate(pickUpRequest)).rejects
          .toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.quantityPackages' }
          }))
      })

      it('throws an error when quantity of labels is given', () => {
        const materialCommand = materialCommandBuilder
          .withQuantityLabels(1)
          .build()

        const material = UpdatePickUpRequestMaterialMapper.mapToMaterial(materialCommand)

        createPickUpRequest(
          material,
          PickUpTransportMode.BULK_ISO_TANK
        )

        expect(validator.validateUpdate(pickUpRequest)).rejects
          .toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.quantityLabels' }
          }))
      })

      it('throws an error when quantity of pallets is given', () => {
        const materialCommand = materialCommandBuilder
          .withQuantityPallets(1)
          .build()

        const material = UpdatePickUpRequestMaterialMapper.mapToMaterial(materialCommand)

        createPickUpRequest(
          material,
          PickUpTransportMode.BULK_ISO_TANK
        )

        expect(validator.validateUpdate(pickUpRequest)).rejects
          .toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.quantityPallets' }
          }))
      })

      it('throws an error when tanker type is given', () => {
        const materialCommand = materialCommandBuilder
          .withTankerType('01')
          .build()

        const material = UpdatePickUpRequestMaterialMapper.mapToMaterial(materialCommand)

        createPickUpRequest(
          material,
          PickUpTransportMode.BULK_ISO_TANK
        )

        expect(validator.validateUpdate(pickUpRequest)).rejects
          .toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.tankerType' }
          }))
      })

      it('throws an error when is container covered is given', () => {
        const materialCommand = materialCommandBuilder
          .withIsContainerCovered(true)
          .build()

        const material = UpdatePickUpRequestMaterialMapper.mapToMaterial(materialCommand)

        createPickUpRequest(
          material,
          PickUpTransportMode.BULK_ISO_TANK
        )

        expect(validator.validateUpdate(pickUpRequest)).rejects
          .toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.isContainerCovered' }
          }))
      })

      it('throws an error when packaging remark is given', () => {
        const materialCommand = materialCommandBuilder
          .build()

        const material = UpdatePickUpRequestMaterialMapper.mapToMaterial(materialCommand)

        const pickUpRequest = pickUpRequestBuilder
          .addMaterial(material)
          .withPackagingRemark('test')
          .withTransportMode(PickUpTransportMode.BULK_ISO_TANK)
          .build()

        expect(validator.validateUpdate(pickUpRequest)).rejects
          .toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.packagingRemark' }
          }))
      })

      it('throws an error when total quantity of pallets is given', () => {
        const materialCommand = materialCommandBuilder
          .build()

        const material = UpdatePickUpRequestMaterialMapper.mapToMaterial(materialCommand)

        const pickUpRequest = pickUpRequestBuilder
          .addMaterial(material)
          .withTotalQuantityPallets(5)
          .withTransportMode(PickUpTransportMode.BULK_ISO_TANK)
          .build()

        expect(validator.validateUpdate(pickUpRequest)).rejects
          .toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.totalQuantityPallets' }
          }))
      })

      it('throws an error when return packaging is given', () => {
        const materialCommand = materialCommandBuilder
          .build()

        const material = UpdatePickUpRequestMaterialMapper.mapToMaterial(materialCommand)

        const pickUpRequest = pickUpRequestBuilder
          .addMaterial(material)
          .withIsReturnPackaging(true)
          .withTransportMode(PickUpTransportMode.BULK_ISO_TANK)
          .build()

        expect(validator.validateUpdate(pickUpRequest)).rejects
          .toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.isReturnPackaging' }
          }))
      })
    })
  })

  describe('Submit validation', () => {
    describe('Requires po number and cost center', () => {
      it('Throws error when cost center is required', async () => {
        sapGetIsCostCenterRequiredUseCase.execute.resolves(true)

        const material = validMaterialBuilder
          .withPackageCurtainSiderTruckProperties()
          .withCostCenter(null)
          .build()

        const pickUpRequest = validPickUpRequestBuilder
          .addMaterial(material, true)
          .withTransportMode(PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK)
          .build()

        await expect(validator.validateSubmit(pickUpRequest))
          .rejects
          .toThrow(expect.objectContaining({
            code: new MissingRequiredFieldError().code,
            source: { pointer: '$.costCenter' }
          }))
      })

      it('Throws error when po number is required', async () => {
        sapGetIsPoReferenceRequiredUseCase.execute.resolves(true)

        const material = validMaterialBuilder
          .withPackageCurtainSiderTruckProperties()
          .withPoNumber(null)
          .build()

        const pickUpRequest = validPickUpRequestBuilder
          .addMaterial(material, true)
          .withTransportMode(PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK)
          .build()

        await expect(validator.validateSubmit(pickUpRequest))
          .rejects
          .toThrow(expect.objectContaining({
            code: new MissingRequiredFieldError().code,
            source: { pointer: '$.poNumber' }
          }))
      })
    })

    describe('Packaged with curtain sider or Truck', () => {
      it('does not throw when quantity of pallets is null in materials', async () => {
        const material = validMaterialBuilder
          .withPackageCurtainSiderTruckProperties()
          .build()

        material.quantityPallets = null

        const pickUpRequest = validPickUpRequestBuilder
          .addMaterial(material, true)
          .withTotalQuantityPallets(5)
          .withTransportMode(PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK)
          .build()

        await expect(validator.validateSubmit(pickUpRequest))
          .resolves.not.toThrow()
      })

      it('does not throw when total quantity pallets is more or equal than the sum of quantity pallets in materials', async () => {
        const quantityPalletsMaterial = 2

        const material = validMaterialBuilder
          .withPackageCurtainSiderTruckProperties()
          .withQuantityPallets(quantityPalletsMaterial)
          .build()

        const pickUpRequest = validPickUpRequestBuilder
          .addMaterial(material, true)
          .withTotalQuantityPallets(quantityPalletsMaterial + 1)
          .withTransportMode(PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK)
          .build()

        expect(() => validator.validateSubmit(pickUpRequest))
          .not.toThrow()

        pickUpRequest.totalQuantityPallets = quantityPalletsMaterial

        await expect(validator.validateSubmit(pickUpRequest))
          .resolves.not.toThrow()
      })

      it('throws an error when total quantity pallets is less than the sum of quantity pallets in materials', async () => {
        const quantityPalletsMaterial = 5

        const material = validMaterialBuilder
          .withPackageCurtainSiderTruckProperties()
          .withQuantityPallets(quantityPalletsMaterial)
          .build()

        const pickUpRequest = validPickUpRequestBuilder
          .addMaterial(material, true)
          .withTotalQuantityPallets(quantityPalletsMaterial - 1)
          .withTransportMode(PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK)
          .build()

        await expect(validator.validateSubmit(pickUpRequest))
          .rejects.toThrow(expect.objectContaining({
            code: new InvalidTotalQuantityPalletsError().code,
            source: { pointer: '$.totalQuantityPallets' }
          }))
      })

      it('throws an error when total quantity of pallets is not given', async () => {
        const material = validMaterialBuilder
          .withPackageCurtainSiderTruckProperties()
          .build()

        const pickUpRequest = validPickUpRequestBuilder
          .addMaterial(material, true)
          .withTransportMode(PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK)
          .build()

        pickUpRequest.totalQuantityPallets = null

        await expect(validator.validateSubmit(pickUpRequest))
          .rejects.toThrow(expect.objectContaining({
            code: new MissingRequiredFieldError().code,
            source: { pointer: '$.totalQuantityPallets' }
          }))
      })

      it('throws an error when total is return packaging is true and packaging remark is not given', async () => {
        const material = validMaterialBuilder
          .withPackageCurtainSiderTruckProperties()
          .build()

        const pickUpRequest = validPickUpRequestBuilder
          .addMaterial(material, true)
          .withIsReturnPackaging(true)
          .withTransportMode(PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK)
          .build()

        pickUpRequest.packagingRemark = null

        await expect(validator.validateSubmit(pickUpRequest))
          .rejects.toThrow(expect.objectContaining({
            code: new MissingRequiredFieldError().code,
            source: { pointer: '$.packagingRemark' }
          }))
      })

      it('throws an error when estimated weight or volume value is not given', async () => {
        const material = validMaterialBuilder
          .withPackageCurtainSiderTruckProperties()
          .build()

        const pickUpRequest = validPickUpRequestBuilder
          .addMaterial({ ...material, estimatedWeightOrVolumeValue: null }, true)
          .withTransportMode(PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK)
          .build()

        await expect(validator.validateSubmit(pickUpRequest))
          .rejects.toThrow(expect.objectContaining({
            code: new MissingRequiredFieldError().code,
            source: { pointer: '$.estimatedWeightOrVolumeValue' }
          }))
      })

      it('throws an error when estimated weight or volume unit is not given', async () => {
        const material = validMaterialBuilder
          .withPackageCurtainSiderTruckProperties()
          .build()

        const pickUpRequest = validPickUpRequestBuilder
          .addMaterial({ ...material, estimatedWeightOrVolumeUnit: null }, true)
          .withTransportMode(PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK)
          .build()

        await expect(validator.validateSubmit(pickUpRequest))
          .rejects.toThrow(expect.objectContaining({
            code: new MissingRequiredFieldError().code,
            source: { pointer: '$.estimatedWeightOrVolumeUnit' }
          }))
      })

      it('throws an error when package type is not given', async () => {
        const material = validMaterialBuilder
          .withPackageCurtainSiderTruckProperties()
          .build()

        const pickUpRequest = validPickUpRequestBuilder
          .addMaterial({ ...material, packagingType: null }, true)
          .withTransportMode(PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK)
          .build()

        await expect(validator.validateSubmit(pickUpRequest))
          .rejects.toThrow(expect.objectContaining({
            code: new MissingRequiredFieldError().code,
            source: { pointer: '$.packagingType' }
          }))
      })

      it('throws an error when quantity packages is not given', async () => {
        const material = validMaterialBuilder
          .withPackageCurtainSiderTruckProperties()
          .build()

        const pickUpRequest = validPickUpRequestBuilder
          .addMaterial({ ...material, quantityPackages: null }, true)
          .withTransportMode(PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK)
          .build()

        await expect(validator.validateSubmit(pickUpRequest))
          .rejects.toThrow(expect.objectContaining({
            code: new MissingRequiredFieldError().code,
            source: { pointer: '$.quantityPackages' }
          }))
      })

      it('throws an error when quantity labels is not given', async () => {
        const material = validMaterialBuilder
          .withPackageCurtainSiderTruckProperties()
          .build()

        const pickUpRequest = validPickUpRequestBuilder
          .addMaterial({ ...material, quantityLabels: null }, true)
          .withTransportMode(PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK)
          .build()

        await expect(validator.validateSubmit(pickUpRequest))
          .rejects.toThrow(expect.objectContaining({
            code: new MissingRequiredFieldError().code,
            source: { pointer: '$.quantityLabels' }
          }))
      })

      it('throws an error when un number is not given', async () => {
        const material = validMaterialBuilder
          .withPackageCurtainSiderTruckProperties()
          .build()

        const pickUpRequest = validPickUpRequestBuilder
          .addMaterial({ ...material, unNumber: null }, true)
          .withTransportMode(PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK)
          .build()

        await expect(validator.validateSubmit(pickUpRequest))
          .rejects.toThrow(expect.objectContaining({
            code: new MissingRequiredFieldError().code,
            source: { pointer: '$.unNumber' }
          }))
      })

      it('throws an error when container type is given', async () => {
        const material = materialBuilder
          .withContainerType('test')
          .build()

        const pickUpRequest = validPickUpRequestBuilder
          .addMaterial(material, true)
          .withTransportMode(PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK)
          .build()

        await expect(validator.validateSubmit(pickUpRequest))
          .rejects.toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.containerType' }
          }))
      })

      it('throws an error when container transport type is given', async () => {
        const material = materialBuilder
          .withContainerTransportType('07')
          .build()

        const pickUpRequest = validPickUpRequestBuilder
          .addMaterial(material, true)
          .withTransportMode(PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK)
          .build()

        await expect(validator.validateSubmit(pickUpRequest))
          .rejects.toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.containerTransportType' }
          }))
      })

      it('throws an error when is container covered is given', async () => {
        const material = materialBuilder
          .withIsContainerCovered(true)
          .build()

        const pickUpRequest = validPickUpRequestBuilder
          .addMaterial(material, true)
          .withTransportMode(PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK)
          .build()

        await expect(validator.validateSubmit(pickUpRequest))
          .rejects.toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.isContainerCovered' }
          }))
      })

      it('throws an error when is containerNumber is given', async () => {
        const material = materialBuilder
          .withContainerNumber('test')
          .build()

        const pickUpRequest = validPickUpRequestBuilder
          .addMaterial(material, true)
          .withTransportMode(PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK)
          .build()

        await expect(validator.validateSubmit(pickUpRequest))
          .rejects.toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.containerNumber' }
          }))
      })

      it('throws an error when is tanker type is given', async () => {
        const material = materialBuilder
          .withTankerType('01')
          .build()

        const pickUpRequest = validPickUpRequestBuilder
          .addMaterial(material, true)
          .withTransportMode(PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK)
          .build()

        await expect(validator.validateSubmit(pickUpRequest))
          .rejects.toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.tankerType' }
          }))
      })

      it('throws an error when quantity labels not given for any customer not of Germany', async () => {
        viewCustomerCountryUseCase.execute.resolves(
          new ViewCustomerCountryResponse(SAP_COUNTRY_CODE.IE)
        )

        const materialCommand = materialCommandBuilder
          .withQuantityPallets(1)
          .withEstimatedWeightOrVolumeValue(100)
          .withEstimatedWeightOrVolumeUnit(WasteMeasurementUnit.KG)
          .withPackagingType('packaging type')
          .withQuantityPackages(1)
          .build()

        const material = UpdatePickUpRequestMaterialMapper.mapToMaterial(materialCommand)

        createPickUpRequest(
          material,
          PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK
        )
        pickUpRequest.customerId = randomUUID()

        await expect(validator.validateSubmit(pickUpRequest)).rejects
          .toThrow(expect.objectContaining({
            code: new MissingRequiredFieldError().code,
            source: { pointer: '$.quantityLabels' }
          }))
      })

      it('throws an error when quantity pallets not given for customer of Germany', async () => {
        viewCustomerCountryUseCase.execute.resolves(
          new ViewCustomerCountryResponse(SAP_COUNTRY_CODE.DE)
        )

        const materialCommand = materialCommandBuilder
          .withQuantityLabels(1)
          .withEstimatedWeightOrVolumeValue(100)
          .withEstimatedWeightOrVolumeUnit(WasteMeasurementUnit.KG)
          .withPackagingType('packaging type')
          .withQuantityPackages(1)
          .build()

        const material = UpdatePickUpRequestMaterialMapper.mapToMaterial(materialCommand)

        createPickUpRequest(
          material,
          PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK
        )
        pickUpRequest.customerId = randomUUID()

        await expect(validator.validateSubmit(pickUpRequest)).rejects
          .toThrow(expect.objectContaining({
            code: new MissingRequiredFieldError().code,
            source: { pointer: '$.quantityPallets' }
          }))
      })

      it('throws an error when reconciliation number given when customer not of Germany', () => {
        viewCustomerCountryUseCase.execute.resolves(
          new ViewCustomerCountryResponse(SAP_COUNTRY_CODE.IE)
        )

        const materialCommand = materialCommandBuilder
          .withReconciliationNumber('12345')
          .build()

        const material = UpdatePickUpRequestMaterialMapper.mapToMaterial(materialCommand)

        createPickUpRequest(
          material,
          PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK
        )
        pickUpRequest.customerId = randomUUID()

        expect(validator.validateSubmit(pickUpRequest)).rejects
          .toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.reconciliationNumber' }
          }))
      })

      it('throws an error when hazard inducers given when customer not of Germany', () => {
        viewCustomerCountryUseCase.execute.resolves(
          new ViewCustomerCountryResponse(SAP_COUNTRY_CODE.IE)
        )

        const materialCommand = materialCommandBuilder
          .withHazardInducers('12345')
          .build()

        const material = UpdatePickUpRequestMaterialMapper.mapToMaterial(materialCommand)

        createPickUpRequest(
          material,
          PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK
        )
        pickUpRequest.customerId = randomUUID()

        expect(validator.validateSubmit(pickUpRequest)).rejects
          .toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.hazardInducers' }
          }))
      })

      it('throws an error when UN number hazardous and hazard inducers not given when customer of Germany', async () => {
        viewCustomerCountryUseCase.execute.resolves(
          new ViewCustomerCountryResponse(SAP_COUNTRY_CODE.DE)
        )

        const materialCommand = materialCommandBuilder
          .withQuantityLabels(1)
          .withQuantityPallets(1)
          .withEstimatedWeightOrVolumeValue(100)
          .withEstimatedWeightOrVolumeUnit(WasteMeasurementUnit.KG)
          .withPackagingType('packing type')
          .withUnNumberHazardous(true)
          .withQuantityPackages(1)
          .withUnNumber('1234')
          .build()

        const material = UpdatePickUpRequestMaterialMapper.mapToMaterial(materialCommand)

        createPickUpRequest(
          material,
          PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK
        )
        pickUpRequest.customerId = randomUUID()

        await expect(validator.validateSubmit(pickUpRequest)).rejects
          .toThrow(expect.objectContaining({
            code: new MissingRequiredFieldError().code,
            source: { pointer: '$.hazardInducers' }
          }))
      })
    })

    describe('Tank container or ISO tank', () => {
      it('throws an error when estimated weight or volume value is not given', async () => {
        const material = validMaterialBuilder
          .withBulkIsoTankProperties()
          .build()

        const pickUpRequest = pickUpRequestBuilder
          .addMaterial({ ...material, estimatedWeightOrVolumeValue: null })
          .withTransportMode(PickUpTransportMode.BULK_ISO_TANK)
          .build()

        await expect(validator.validateSubmit(pickUpRequest))
          .rejects.toThrow(expect.objectContaining({
            code: new MissingRequiredFieldError().code,
            source: { pointer: '$.estimatedWeightOrVolumeValue' }
          }))
      })

      it('throws an error when estimated weight or volume unit is not given', async () => {
        const material = validMaterialBuilder
          .withBulkIsoTankProperties()
          .build()

        const pickUpRequest = pickUpRequestBuilder
          .addMaterial({ ...material, estimatedWeightOrVolumeUnit: null })
          .withTransportMode(PickUpTransportMode.BULK_ISO_TANK)
          .build()

        await expect(validator.validateSubmit(pickUpRequest))
          .rejects.toThrow(expect.objectContaining({
            code: new MissingRequiredFieldError().code,
            source: { pointer: '$.estimatedWeightOrVolumeUnit' }
          }))
      })

      it('throws an error when container transport type is not given', async () => {
        const material = validMaterialBuilder
          .withBulkIsoTankProperties()
          .build()

        const pickUpRequest = pickUpRequestBuilder
          .addMaterial({ ...material, containerTransportType: null })
          .withTransportMode(PickUpTransportMode.BULK_ISO_TANK)
          .build()

        await expect(validator.validateSubmit(pickUpRequest))
          .rejects.toThrow(expect.objectContaining({
            code: new MissingRequiredFieldError().code,
            source: { pointer: '$.containerTransportType' }
          }))
      })

      it('throws an error when un number is not given', async () => {
        const material = validMaterialBuilder
          .withBulkIsoTankProperties()
          .build()

        const pickUpRequest = pickUpRequestBuilder
          .addMaterial({ ...material, unNumber: null })
          .withTransportMode(PickUpTransportMode.BULK_ISO_TANK)
          .build()

        await expect(validator.validateSubmit(pickUpRequest))
          .rejects.toThrow(expect.objectContaining({
            code: new MissingRequiredFieldError().code,
            source: { pointer: '$.unNumber' }
          }))
      })

      it('throws an error when package type is given', async () => {
        const material = materialBuilder
          .withPackagingType('test')
          .build()

        const pickUpRequest = pickUpRequestBuilder
          .addMaterial(material)
          .withTransportMode(PickUpTransportMode.BULK_ISO_TANK)
          .build()

        await expect(validator.validateSubmit(pickUpRequest))
          .rejects.toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.packagingType' }
          }))
      })

      it('throws an error when quantity of packages type is given', async () => {
        const material = materialBuilder
          .withQuantityPackages(1)
          .build()

        const pickUpRequest = pickUpRequestBuilder
          .addMaterial(material)
          .withTransportMode(PickUpTransportMode.BULK_ISO_TANK)
          .build()

        await expect(validator.validateSubmit(pickUpRequest))
          .rejects.toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.quantityPackages' }
          }))
      })

      it('throws an error when quantity of labels is given', async () => {
        const material = materialBuilder
          .withQuantityLabels(1)
          .build()

        const pickUpRequest = pickUpRequestBuilder
          .addMaterial(material)
          .withTransportMode(PickUpTransportMode.BULK_ISO_TANK)
          .build()

        await expect(validator.validateSubmit(pickUpRequest))
          .rejects.toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.quantityLabels' }
          }))
      })

      it('throws an error when quantity of pallets is given', async () => {
        const material = materialBuilder
          .withQuantityPallets(1)
          .build()

        const pickUpRequest = pickUpRequestBuilder
          .addMaterial(material)
          .withTransportMode(PickUpTransportMode.BULK_ISO_TANK)
          .build()

        await expect(validator.validateSubmit(pickUpRequest))
          .rejects.toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.quantityPallets' }
          }))
      })

      it('throws an error when tanker type is given', async () => {
        const material = materialBuilder
          .withTankerType('01')
          .build()

        const pickUpRequest = pickUpRequestBuilder
          .addMaterial(material)
          .withTransportMode(PickUpTransportMode.BULK_ISO_TANK)
          .build()

        await expect(validator.validateSubmit(pickUpRequest))
          .rejects.toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.tankerType' }
          }))
      })

      it('throws an error when is container covered is given', async () => {
        const material = materialBuilder
          .withIsContainerCovered(true)
          .build()

        const pickUpRequest = pickUpRequestBuilder
          .addMaterial(material)
          .withTransportMode(PickUpTransportMode.BULK_ISO_TANK)
          .build()

        await expect(validator.validateSubmit(pickUpRequest))
          .rejects.toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.isContainerCovered' }
          }))
      })

      it('throws an error when container type is given', async () => {
        const materialCommand = materialCommandBuilder
          .withEstimatedWeightOrVolumeValue(100)
          .withEstimatedWeightOrVolumeUnit(WasteMeasurementUnit.KG)
          .withUnNumber('1234')
          .withContainerType('test')
          .build()

        const material = UpdatePickUpRequestMaterialMapper.mapToMaterial(materialCommand)

        createPickUpRequest(
          material,
          PickUpTransportMode.BULK_ISO_TANK
        )
        pickUpRequest.customerId = randomUUID()

        await expect(validator.validateSubmit(pickUpRequest))
          .rejects.toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.containerType' }
          }))
      })
    })

    describe('Bulk waste in skips or containers', () => {
      it('throws an error when estimated weight or volume value is not given', async () => {
        const material = validMaterialBuilder
          .withBulkSkipsContainerProperties()
          .build()

        const pickUpRequest = pickUpRequestBuilder
          .addMaterial({ ...material, estimatedWeightOrVolumeValue: null })
          .withTransportMode(PickUpTransportMode.BULK_SKIPS_CONTAINER)
          .build()

        await expect(validator.validateSubmit(pickUpRequest))
          .rejects.toThrow(expect.objectContaining({
            code: new MissingRequiredFieldError().code,
            source: { pointer: '$.estimatedWeightOrVolumeValue' }
          }))
      })

      it('throws an error when estimated weight or volume unit is not given', async () => {
        const material = validMaterialBuilder
          .withBulkSkipsContainerProperties()
          .build()

        const pickUpRequest = pickUpRequestBuilder
          .addMaterial({ ...material, estimatedWeightOrVolumeUnit: null })
          .withTransportMode(PickUpTransportMode.BULK_SKIPS_CONTAINER)
          .build()

        await expect(validator.validateSubmit(pickUpRequest))
          .rejects.toThrow(expect.objectContaining({
            code: new MissingRequiredFieldError().code,
            source: { pointer: '$.estimatedWeightOrVolumeUnit' }
          }))
      })

      it('throws an error when container transport type is not given', async () => {
        const material = validMaterialBuilder
          .withBulkSkipsContainerProperties()
          .build()

        const pickUpRequest = pickUpRequestBuilder
          .addMaterial({ ...material, containerTransportType: null })
          .withTransportMode(PickUpTransportMode.BULK_SKIPS_CONTAINER)
          .build()

        await expect(validator.validateSubmit(pickUpRequest))
          .rejects.toThrow(expect.objectContaining({
            code: new MissingRequiredFieldError().code,
            source: { pointer: '$.containerTransportType' }
          }))
      })

      it('throws an error when un number is not given', async () => {
        const material = validMaterialBuilder
          .withBulkSkipsContainerProperties()
          .build()

        const pickUpRequest = pickUpRequestBuilder
          .addMaterial({ ...material, unNumber: null })
          .withTransportMode(PickUpTransportMode.BULK_SKIPS_CONTAINER)
          .build()

        await expect(validator.validateSubmit(pickUpRequest))
          .rejects.toThrow(expect.objectContaining({
            code: new MissingRequiredFieldError().code,
            source: { pointer: '$.unNumber' }
          }))
      })

      it('throws an error when package type is given', async () => {
        const material = materialBuilder
          .withPackagingType('test')
          .build()

        const pickUpRequest = pickUpRequestBuilder
          .addMaterial(material)
          .withTransportMode(PickUpTransportMode.BULK_SKIPS_CONTAINER)
          .build()

        await expect(validator.validateSubmit(pickUpRequest))
          .rejects.toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.packagingType' }
          }))
      })

      it('throws an error when quantity of packages type is given', async () => {
        const material = materialBuilder
          .withQuantityPackages(1)
          .build()

        const pickUpRequest = pickUpRequestBuilder
          .addMaterial(material)
          .withTransportMode(PickUpTransportMode.BULK_SKIPS_CONTAINER)
          .build()

        await expect(validator.validateSubmit(pickUpRequest))
          .rejects.toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.quantityPackages' }
          }))
      })

      it('throws an error when quantity of labels is given', async () => {
        const material = materialBuilder
          .withQuantityLabels(1)
          .build()

        const pickUpRequest = pickUpRequestBuilder
          .addMaterial(material)
          .withTransportMode(PickUpTransportMode.BULK_SKIPS_CONTAINER)
          .build()

        await expect(validator.validateSubmit(pickUpRequest))
          .rejects.toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.quantityLabels' }
          }))
      })

      it('throws an error when quantity of pallets is given', async () => {
        const material = materialBuilder
          .withQuantityPallets(1)
          .build()

        const pickUpRequest = pickUpRequestBuilder
          .addMaterial(material)
          .withTransportMode(PickUpTransportMode.BULK_SKIPS_CONTAINER)
          .build()

        await expect(validator.validateSubmit(pickUpRequest))
          .rejects.toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.quantityPallets' }
          }))
      })

      it('throws an error when tanker type is given', async () => {
        const material = materialBuilder
          .withTankerType('01')
          .build()

        const pickUpRequest = pickUpRequestBuilder
          .addMaterial(material)
          .withTransportMode(PickUpTransportMode.BULK_SKIPS_CONTAINER)
          .build()

        await expect(validator.validateSubmit(pickUpRequest))
          .rejects.toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.tankerType' }
          }))
      })

      it('throws an error when container type not given for customer of Germany', async () => {
        viewCustomerCountryUseCase.execute.resolves(
          new ViewCustomerCountryResponse(SAP_COUNTRY_CODE.DE)
        )

        const materialCommand = materialCommandBuilder
          .withContainerVolumeSize('10m3')
          .withEstimatedWeightOrVolumeValue(100)
          .withEstimatedWeightOrVolumeUnit(WasteMeasurementUnit.KG)
          .withUnNumber('1234')
          .withContainerTransportType('07')
          .build()

        const material = UpdatePickUpRequestMaterialMapper.mapToMaterial(materialCommand)

        createPickUpRequest(
          material,
          PickUpTransportMode.BULK_SKIPS_CONTAINER
        )
        pickUpRequest.customerId = randomUUID()

        await expect(validator.validateSubmit(pickUpRequest))
          .rejects.toThrow(expect.objectContaining({
            code: new MissingRequiredFieldError().code,
            source: { pointer: '$.containerType' }
          }))
      })

      it('throws an error when container volume size not given for customer of Germany', async () => {
        viewCustomerCountryUseCase.execute.resolves(
          new ViewCustomerCountryResponse(SAP_COUNTRY_CODE.DE)
        )

        const materialCommand = materialCommandBuilder
          .withEstimatedWeightOrVolumeValue(100)
          .withEstimatedWeightOrVolumeUnit(WasteMeasurementUnit.KG)
          .withUnNumber('1234')
          .build()

        const material = UpdatePickUpRequestMaterialMapper.mapToMaterial(materialCommand)

        createPickUpRequest(
          material,
          PickUpTransportMode.BULK_SKIPS_CONTAINER
        )
        pickUpRequest.customerId = randomUUID()

        await expect(validator.validateSubmit(pickUpRequest)).rejects
          .toThrow(expect.objectContaining({
            code: new MissingRequiredFieldError().code,
            source: { pointer: '$.containerVolumeSize' }
          }))
      })

      it('throws an error when container volume size not given for customer of Ireland', async () => {
        viewCustomerCountryUseCase.execute.resolves(
          new ViewCustomerCountryResponse(SAP_COUNTRY_CODE.IE)
        )

        const materialCommand = materialCommandBuilder
          .withEstimatedWeightOrVolumeValue(100)
          .withEstimatedWeightOrVolumeUnit(WasteMeasurementUnit.KG)
          .withUnNumber('1234')
          .build()

        const material = UpdatePickUpRequestMaterialMapper.mapToMaterial(materialCommand)

        createPickUpRequest(
          material,
          PickUpTransportMode.BULK_SKIPS_CONTAINER
        )
        pickUpRequest.customerId = randomUUID()

        await expect(validator.validateSubmit(pickUpRequest)).rejects
          .toThrow(expect.objectContaining({
            code: new MissingRequiredFieldError().code,
            source: { pointer: '$.containerVolumeSize' }
          }))
      })
    })

    describe('Bulk waste in vacuum tank or road tanker', () => {
      it('throws an error when estimated weight or volume value is not given', async () => {
        const material = validMaterialBuilder
          .withBulkVacuumTankersRoadTankers()
          .build()

        const pickUpRequest = pickUpRequestBuilder
          .addMaterial({ ...material, estimatedWeightOrVolumeValue: null })
          .withTransportMode(PickUpTransportMode.BULK_VACUUM_TANKERS_ROAD_TANKERS)
          .build()

        await expect(validator.validateSubmit(pickUpRequest))
          .rejects.toThrow(expect.objectContaining({
            code: new MissingRequiredFieldError().code,
            source: { pointer: '$.estimatedWeightOrVolumeValue' }
          }))
      })

      it('throws an error when estimated weight or volume unit is not given', async () => {
        const material = validMaterialBuilder
          .withBulkVacuumTankersRoadTankers()
          .build()

        const pickUpRequest = pickUpRequestBuilder
          .addMaterial({ ...material, estimatedWeightOrVolumeUnit: null })
          .withTransportMode(PickUpTransportMode.BULK_VACUUM_TANKERS_ROAD_TANKERS)
          .build()

        await expect(validator.validateSubmit(pickUpRequest))
          .rejects.toThrow(expect.objectContaining({
            code: new MissingRequiredFieldError().code,
            source: { pointer: '$.estimatedWeightOrVolumeUnit' }
          }))
      })

      it('throws an error when un number is not given', async () => {
        const material = validMaterialBuilder
          .withBulkVacuumTankersRoadTankers()
          .build()

        const pickUpRequest = pickUpRequestBuilder
          .addMaterial({ ...material, unNumber: null })
          .withTransportMode(PickUpTransportMode.BULK_VACUUM_TANKERS_ROAD_TANKERS)
          .build()

        await expect(validator.validateSubmit(pickUpRequest))
          .rejects.toThrow(expect.objectContaining({
            code: new MissingRequiredFieldError().code,
            source: { pointer: '$.unNumber' }
          }))
      })

      it('throws an error when package type is given', async () => {
        const material = materialBuilder
          .withPackagingType('test')
          .build()

        const pickUpRequest = pickUpRequestBuilder
          .addMaterial(material)
          .withTransportMode(PickUpTransportMode.BULK_VACUUM_TANKERS_ROAD_TANKERS)
          .build()

        await expect(validator.validateSubmit(pickUpRequest))
          .rejects.toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.packagingType' }
          }))
      })

      it('throws an error when quantity of packages type is given', async () => {
        const material = materialBuilder
          .withQuantityPackages(1)
          .build()

        const pickUpRequest = pickUpRequestBuilder
          .addMaterial(material)
          .withTransportMode(PickUpTransportMode.BULK_VACUUM_TANKERS_ROAD_TANKERS)
          .build()

        await expect(validator.validateSubmit(pickUpRequest))
          .rejects.toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.quantityPackages' }
          }))
      })

      it('throws an error when quantity of labels is given', async () => {
        const material = materialBuilder
          .withQuantityLabels(1)
          .build()

        const pickUpRequest = pickUpRequestBuilder
          .addMaterial(material)
          .withTransportMode(PickUpTransportMode.BULK_VACUUM_TANKERS_ROAD_TANKERS)
          .build()

        await expect(validator.validateSubmit(pickUpRequest))
          .rejects.toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.quantityLabels' }
          }))
      })

      it('throws an error when quantity of pallets is given', async () => {
        const material = materialBuilder
          .withQuantityPallets(1)
          .build()

        const pickUpRequest = pickUpRequestBuilder
          .addMaterial(material)
          .withTransportMode(PickUpTransportMode.BULK_VACUUM_TANKERS_ROAD_TANKERS)
          .build()

        await expect(validator.validateSubmit(pickUpRequest))
          .rejects.toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.quantityPallets' }
          }))
      })

      it('throws an error when container type is given', async () => {
        const material = materialBuilder
          .withContainerType('test')
          .build()

        const pickUpRequest = pickUpRequestBuilder
          .addMaterial(material)
          .withTransportMode(PickUpTransportMode.BULK_VACUUM_TANKERS_ROAD_TANKERS)
          .build()

        await expect(validator.validateSubmit(pickUpRequest))
          .rejects.toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.containerType' }
          }))
      })

      it('throws an error when container transport type is given', async () => {
        const material = materialBuilder
          .withContainerTransportType('07')
          .build()

        const pickUpRequest = pickUpRequestBuilder
          .addMaterial(material)
          .withTransportMode(PickUpTransportMode.BULK_VACUUM_TANKERS_ROAD_TANKERS)
          .build()

        await expect(validator.validateSubmit(pickUpRequest))
          .rejects.toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.containerTransportType' }
          }))
      })

      it('throws an error when is container covered is given', async () => {
        const material = materialBuilder
          .withIsContainerCovered(true)
          .build()

        const pickUpRequest = pickUpRequestBuilder
          .addMaterial(material)
          .withTransportMode(PickUpTransportMode.BULK_VACUUM_TANKERS_ROAD_TANKERS)
          .build()

        await expect(validator.validateSubmit(pickUpRequest))
          .rejects.toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.isContainerCovered' }
          }))
      })

      it('throws an error when containerNumber is given', async () => {
        const material = materialBuilder
          .withContainerNumber('test')
          .build()

        const pickUpRequest = pickUpRequestBuilder
          .addMaterial(material)
          .withTransportMode(PickUpTransportMode.BULK_VACUUM_TANKERS_ROAD_TANKERS)
          .build()

        await expect(validator.validateSubmit(pickUpRequest))
          .rejects.toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.containerNumber' }
          }))
      })
    })
  })
})
