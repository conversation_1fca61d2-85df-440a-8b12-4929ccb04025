import { randomUUID } from 'crypto'
import dayjs from 'dayjs'
import { PickUpRequest } from '../entities/pick-up-request.entity.js'
import { PickUpTransportMode } from '../enums/pick-up-transport-mode.enum.js'
import { PickUpRequestMaterial } from '../types/pick-up-request-material.type.js'
import { User } from '../../../app/users/entities/user.entity.js'
import { PickUpRequestMaterialWithContractLine } from '../types/pick-up-request-material-with-contract-line.type.js'
import { ValidPickUpRequestMaterialBuilder } from './valid-pick-up-request-material.builder.js'

export class ValidPickUpRequestEntityBuilder {
  private readonly ADD_START_DATE_DAYS = 1
  private readonly ADD_END_DATE_DAYS = 5

  private pickUpRequest: PickUpRequest

  constructor () {
    this.reset()
  }

  reset (): this {
    this.pickUpRequest = new PickUpRequest()

    this.pickUpRequest.uuid = randomUUID()
    this.pickUpRequest.createdAt = new Date()
    this.pickUpRequest.updatedAt = new Date()
    this.pickUpRequest.customerId = randomUUID()
    this.pickUpRequest.wasteProducerId = randomUUID()
    this.pickUpRequest.pickUpAddressIds = [randomUUID()]
    this.pickUpRequest.startDate = dayjs().add(this.ADD_START_DATE_DAYS, 'day').format('YYYY-MM-DD')
    this.pickUpRequest.endDate = dayjs().add(this.ADD_END_DATE_DAYS, 'day').format('YYYY-MM-DD')
    this.pickUpRequest.submittedOn = null
    this.pickUpRequest.isReturnPackaging = true
    this.pickUpRequest.packagingRemark = 'test'
    this.pickUpRequest.totalQuantityPallets = 1
    this.pickUpRequest.materials = [
      new ValidPickUpRequestMaterialBuilder()
        .withPackageCurtainSiderTruckProperties()
        .build()
    ]
    this.pickUpRequest.packagingRequestMaterials = []
    this.pickUpRequest.transportMode = PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK
    this.pickUpRequest.isTransportByIndaver = true
    this.pickUpRequest.weeklyPlanningRequestUuid = null
    this.pickUpRequest.sendCopyToContacts = []
    this.pickUpRequest.additionalFiles = []
    this.pickUpRequest.templateName = null
    this.pickUpRequest.templateUpdatedByUserUuid = null
    this.pickUpRequest.materialsWithContractLine = []
    this.pickUpRequest.isWicConfirmed = null
    this.pickUpRequest.startTime = null

    return this
  }

  withUuid (uuid: string): this {
    this.pickUpRequest.uuid = uuid

    return this
  }

  createdByUserUuid (userUuid: string): this {
    this.pickUpRequest.createdByUserUuid = userUuid

    return this
  }

  createdByUser (user: User): this {
    this.pickUpRequest.createdByUser = user
    this.pickUpRequest.createdByUserUuid = user.uuid

    return this
  }

  withTransportMode (transportMode: PickUpTransportMode): this {
    this.pickUpRequest.transportMode = transportMode

    return this
  }

  withCustomerId (customerId: string): this {
    this.pickUpRequest.customerId = customerId

    return this
  }

  withWasteProducerId (wasteProducerId: string): this {
    this.pickUpRequest.wasteProducerId = wasteProducerId

    return this
  }

  addPickUpAddressId (pickUpAddressId: string): this {
    this.pickUpRequest.pickUpAddressIds.push(pickUpAddressId)

    return this
  }

  addMaterial (material: PickUpRequestMaterial, reset: boolean = false) {
    if (reset) {
      this.pickUpRequest.materials = []
    }

    this.pickUpRequest.materials.push(material)

    return this
  }

  withSubmittedOn (submittedOn: Date): this {
    this.pickUpRequest.submittedOn = submittedOn

    return this
  }

  withTotalQuantityPallets (totalQuantityPallets: number | null): this {
    this.pickUpRequest.totalQuantityPallets = totalQuantityPallets

    return this
  }

  withStartDate (startDate: string): this {
    this.pickUpRequest.startDate = startDate

    return this
  }

  withIsReturnPackaging (isReturnPackaging: boolean | null): this {
    this.pickUpRequest.isReturnPackaging = isReturnPackaging

    return this
  }

  withPackagingRemark (packagingRemark: string | null): this {
    this.pickUpRequest.packagingRemark = packagingRemark

    return this
  }

  withEndDate (endDate: string | null): this {
    this.pickUpRequest.endDate = endDate

    return this
  }

  withWeeklyPlanningRequestUuid (uuid: string) {
    this.pickUpRequest.weeklyPlanningRequestUuid = uuid

    return this
  }

  withTemplateName (templateName: string): this {
    this.pickUpRequest.templateName = templateName

    return this
  }

  withMaterials (materials: PickUpRequestMaterialWithContractLine[]): this {
    this.pickUpRequest.materialsWithContractLine = materials

    return this
  }

  withIsWicConfirmed (isConfirmed: boolean | null): this {
    this.pickUpRequest.isWicConfirmed = isConfirmed
    return this
  }

  build (): PickUpRequest {
    const result = this.pickUpRequest

    this.reset()

    return result
  }
}
