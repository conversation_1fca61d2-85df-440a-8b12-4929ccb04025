import { randomUUID } from 'crypto'
import { PickUpRequestMaterial } from '../types/pick-up-request-material.type.js'
import { WasteMeasurementUnit } from '../../waste-inquiry/enums/waste-measurement-unit.enum.js'

export class PickUpRequestMaterialBuilder {
  private material: PickUpRequestMaterial

  constructor () {
    this.reset()
  }

  reset (): this {
    this.material = {
      contractLineId: randomUUID(),
      contractNumber: randomUUID(),
      contractItem: randomUUID()
    }

    return this
  }

  withContractLineId (contractLineId: string): this {
    this.material.contractLineId = contractLineId

    return this
  }

  withContractNumber (contractNumber: string): this {
    this.material.contractNumber = contractNumber

    return this
  }

  withContractItem (contractItem: string): this {
    this.material.contractItem = contractItem

    return this
  }

  withEstimatedWeightOrVolumeValue (estimatedWeightOrVolumeValue: number): this {
    this.material.estimatedWeightOrVolumeValue = estimatedWeightOrVolumeValue

    return this
  }

  withEstimatedWeightOrVolumeUnit (estimatedWeightOrVolumeUnit: WasteMeasurementUnit): this {
    this.material.estimatedWeightOrVolumeUnit = estimatedWeightOrVolumeUnit

    return this
  }

  withCostCenter (costCenter: string): this {
    this.material.costCenter = costCenter

    return this
  }

  withPoNumber (poNumber: string): this {
    this.material.poNumber = poNumber

    return this
  }

  withUnNumber (unNumber: string): this {
    this.material.unNumber = unNumber

    return this
  }

  withPackagingType (packagingType: string): this {
    this.material.packagingType = packagingType

    return this
  }

  withQuantityPackages (quantityPackages: number): this {
    this.material.quantityPackages = quantityPackages

    return this
  }

  withQuantityLabels (quantityLabels: number): this {
    this.material.quantityLabels = quantityLabels

    return this
  }

  withQuantityPallets (quantityPallets: number): this {
    this.material.quantityPallets = quantityPallets

    return this
  }

  withContainerType (containerType: string): this {
    this.material.containerType = containerType

    return this
  }

  withContainerVolumeSize (containerVolumeSize: string): this {
    this.material.containerVolumeSize = containerVolumeSize

    return this
  }

  withContainerNumber (containerNumber: string): this {
    this.material.containerNumber = containerNumber

    return this
  }

  withContainerTransportType (containerTransportType: string): this {
    this.material.containerTransportType = containerTransportType

    return this
  }

  withTankerType (tankerType: string): this {
    this.material.tankerType = tankerType

    return this
  }

  withIsContainerCovered (isContainerCovered: boolean): this {
    this.material.isContainerCovered = isContainerCovered

    return this
  }

  withProcessCode (processCode: string): this {
    this.material.processCode = processCode
    return this
  }

  build (): PickUpRequestMaterial {
    const result = this.material

    this.reset()

    return result
  }
}
