import { CronjobType } from './cronjob-type.enum.js'

export const CronjobTypeMapping = {
  [CronjobType.SYNC_ROLES]: async () => {
    return {
      module: (await import('../microsoft-graph/use-cases/sync-roles-from-azure-entra/sync-roles-from-azure-entra.module.js')).SyncRolesFromAzureEntraModule,
      useCase: (await import('../microsoft-graph/use-cases/sync-roles-from-azure-entra/sync-roles-from-azure-entra.use-case.js')).SyncRolesFromAzureEntraUseCase
    }
  },
  [CronjobType.SYNC_UN_NUMBERS]: async () => {
    return {
      module: (await import('../un-number/use-cases/sync-un-numbers-from-sap/sync-un-numbers-from-sap.module.js')).SyncUnNumbersFromSapModule,
      useCase: (await import('../un-number/use-cases/sync-un-numbers-from-sap/sync-un-numbers-from-sap.use-case.js')).SyncUnNumbersFromSapUseCase
    }
  },
  [CronjobType.SYNC_ENTRA_USERS]: async () => {
    return {
      module: (await import('../../app/users/sync-entra-users/sync-entra-users.cron-job.module.js')).SyncEntraUsersCronjobModule,
      useCase: (await import('../../app/users/sync-entra-users/sync-entra-users.use-case.js')).SyncEntraUsersUseCase
    }
  }
}
