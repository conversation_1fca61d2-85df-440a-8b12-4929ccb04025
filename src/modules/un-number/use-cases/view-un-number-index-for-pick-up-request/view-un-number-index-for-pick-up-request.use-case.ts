import { Injectable } from '@nestjs/common'
import { SapGetUnNumberIndexForPickUpRequestUseCase } from '../../../sap/use-cases/get-un-number-index-for-pick-up-request/get-un-number-index-for-pick-up-request.use-case.js'
import { SapGetUnNumberIndexForPickUpRequestResponse } from '../../../sap/use-cases/get-un-number-index-for-pick-up-request/get-un-number-index-for-pick-up-request.response.js'
import { SapQuery } from '../../../sap/query/sap-query.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { ViewUnNumberIndexUseCase } from '../view-un-number-index/view-un-number-index.use-case.js'
import { PackingGroup } from '../../../waste-inquiry/enums/packaging-group.enum.js'
import { UN_NUMBER_NOT_REGULATED } from '../../constants/un-number-not-regulated.constant.js'
import { ViewUnNumberIndexForPickUpRequestResponse } from './view-un-number-index-for-pick-up-request.response.js'
import { ViewUnNumberIndexForPickUpRequestQuery } from './query/view-un-number-index-for-pick-up-request.query.js'

@Injectable()
export class ViewUnNumberIndexForPickUpRequestUseCase {
  constructor (
    private readonly authContext: AuthContext,
    private readonly sapGetUnNumberIndex: SapGetUnNumberIndexForPickUpRequestUseCase,
    private readonly viewUnNumberIndexUseCase: ViewUnNumberIndexUseCase
  ) {}

  public async execute (
    query: ViewUnNumberIndexForPickUpRequestQuery
  ): Promise<ViewUnNumberIndexForPickUpRequestResponse> {
    // 1. Get the restricted UN numbers using a separate SAP endpoint
    const sapQuery = this.getSapQuery(query)
    const sapResponse = await this.sapGetUnNumberIndex.execute(sapQuery)

    const seenUnNumberCombinations = new Set<string>()

    const unNumberKeys = sapResponse
      .filter((unNumber) => {
        if (unNumber.Dgkey === undefined || unNumber.Dgkey.trim() === '') {
          return false
        }

        const dgnu = unNumber.Dgnu ?? ''
        const pgro = unNumber.Pgro ?? ''
        const combinationKey = `${dgnu}|${pgro}`

        if (seenUnNumberCombinations.has(combinationKey)) {
          return false
        }

        seenUnNumberCombinations.add(combinationKey)
        return true
      })
      .map(unNumber => unNumber.Dgkey!)

    if (unNumberKeys.length === 0) {
      return new ViewUnNumberIndexForPickUpRequestResponse([{
        number: '0000',
        packingGroup: PackingGroup.NOT_APPLICABLE,
        dangerLabel1: null,
        dangerLabel2: null,
        dangerLabel3: null,
        isHazardous: false,
        description: UN_NUMBER_NOT_REGULATED
      }])
    }

    // 2. Retrieve the matching UN numbers from the database
    const uniqueUnNumberKeys = Array.from(new Set(unNumberKeys))
    const unNumbers = await this.viewUnNumberIndexUseCase.execute({
      filter: {
        keys: uniqueUnNumberKeys
      },
      search: query.search
    })

    return new ViewUnNumberIndexForPickUpRequestResponse(unNumbers.items)
  }

  private getSapQuery (
    query: ViewUnNumberIndexForPickUpRequestQuery
  ): SapQuery<SapGetUnNumberIndexForPickUpRequestResponse> {
    const sapQuery = new SapQuery<SapGetUnNumberIndexForPickUpRequestResponse>()
      .addSelect([
        'Dgkey',
        'Dgnu',
        'Pgro'
      ])
      .where('Vbeln', query.filter.contractNumber.padStart(10, '0'))
      .andWhere('Posnr', query.filter.contractItem)

    if (query.filter.tcNumber !== undefined) {
      sapQuery.andWhere('ActionNr', query.filter.tcNumber)
    }

    const selectedCustomer = this.authContext.getSelectedCustomerId()
    if (selectedCustomer != null) {
      sapQuery.andWhere('KunnrAg', selectedCustomer.padStart(10, '0'))
    }

    return sapQuery
  }
}
