import { afterEach, before, describe, it } from 'node:test'
import { createStubInstance, SinonStubbedInstance, assert } from 'sinon'
import { Repository } from 'typeorm'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { SyncUnNumbersFromSapUseCase } from '../sync-un-numbers-from-sap.use-case.js'
import { SapGetUnNumberIndexUseCase } from '../../../../sap/use-cases/get-un-number-index/get-un-number-index.use-case.js'
import { stubDataSource } from '../../../../../../test/utils/stub-datasource.js'
import { UnNumber } from '../../../entities/un-number.entity.js'
import { Locale } from '../../../../localization/enums/locale.enum.js'

describe('Sync UN numbers from SAP use-case unit test', () => {
  let useCase: SyncUnNumbersFromSapUseCase

  let sapGetUnNumberIndexUseCase: SinonStubbedInstance<SapGetUnNumberIndexUseCase>
  let unNumberRepository: SinonStubbedInstance<Repository<UnNumber>>

  before(() => {
    TestBench.setupUnitTest()

    sapGetUnNumberIndexUseCase = createStubInstance(SapGetUnNumberIndexUseCase)
    unNumberRepository = createStubInstance<Repository<UnNumber>>(Repository<UnNumber>)

    useCase = new SyncUnNumbersFromSapUseCase(
      sapGetUnNumberIndexUseCase,
      stubDataSource(),
      unNumberRepository
    )

    mockMethods()
  })

  afterEach(() => {
    mockMethods()
  })

  function mockMethods () {
    sapGetUnNumberIndexUseCase.execute.resolves([])
  }

  it('Calls all methods for each locale', async () => {
    await useCase.execute()

    const localeCount = Object.values(Locale).length

    assert.callCount(sapGetUnNumberIndexUseCase.execute, localeCount)
    assert.callCount(unNumberRepository.delete, localeCount)
    assert.callCount(unNumberRepository.upsert, localeCount)
  })
})
