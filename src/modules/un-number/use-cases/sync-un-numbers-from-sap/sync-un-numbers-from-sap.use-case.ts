import { Injectable, Logger } from '@nestjs/common'
import { DataSource, Repository } from 'typeorm'
import { InjectRepository, transaction } from '@wisemen/nestjs-typeorm'
import { CronjobUseCase } from '../../../cronjobs/cronjob.use-case.js'
import { SapGetUnNumberIndexUseCase } from '../../../sap/use-cases/get-un-number-index/get-un-number-index.use-case.js'
import { SapQuery } from '../../../sap/query/sap-query.js'
import { SapGetUnNumberIndexResponse } from '../../../sap/use-cases/get-un-number-index/get-un-number-index.response.js'
import { UnNumber } from '../../entities/un-number.entity.js'
import { MapUnNumberSapService } from '../../services/map-un-number-sap.service.js'
import { Locale } from '../../../localization/enums/locale.enum.js'
import { SapLanguageMapper } from '../../../sap/services/sap-language.mapper.js'

@Injectable()
export class SyncUnNumbersFromSapUseCase implements CronjobUseCase {
  constructor (
    private readonly sapGetUnNumberIndexUseCase: SapGetUnNumberIndexUseCase,
    private readonly dataSource: DataSource,
    @InjectRepository(UnNumber)
    private readonly unNumberRepository: Repository<UnNumber>
  ) {}

  async execute (): Promise<void> {
    await Promise.all(
      Object.values(Locale).map(async (locale) => {
        const sapLanguage = SapLanguageMapper.fromLocale(locale)
        const sapQuery = new SapQuery<SapGetUnNumberIndexResponse>()
          .setLanguage(sapLanguage)

        const sapUnNumbers = await this.sapGetUnNumberIndexUseCase.execute(sapQuery)

        let unNumbers = MapUnNumberSapService.mapResultsToUnNumbers(sapUnNumbers, locale)

        // Filter out unNumbers with empty numbers
        unNumbers = unNumbers.filter(unNumber => unNumber.number.trim() !== '')

        // Filter out any duplicates
        const seenUnNumberDgKeys = new Set<string>()
        unNumbers = unNumbers.filter((unNumber) => {
          const dgKey = unNumber.key
          if (dgKey !== null && seenUnNumberDgKeys.has(dgKey)) {
            Logger.log('warn', `(${locale}) Duplicate UN number DgKey found: ${dgKey}. Skipping.`)
            return false
          }

          if (dgKey !== null) {
            seenUnNumberDgKeys.add(dgKey)
          }
          return true
        })

        await transaction(this.dataSource, async () => {
          await this.unNumberRepository.delete({
            language: locale
          })
          await this.unNumberRepository.upsert(unNumbers, {
            conflictPaths: {
              key: true,
              language: true
            }
          })
        })
      })
    )
  }
}
