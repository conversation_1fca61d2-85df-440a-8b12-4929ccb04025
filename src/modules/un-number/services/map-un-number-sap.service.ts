import { randomUUID } from 'crypto'
import { SapGetUnNumberIndexResponse } from '../../sap/use-cases/get-un-number-index/get-un-number-index.response.js'
import { SAP_ENDPOINTS } from '../../sap/constants/endpoint.constants.js'
import { UnNumber } from '../entities/un-number.entity.js'
import { mapSapIValueToPackingGroup, PackingGroup } from '../../waste-inquiry/enums/packaging-group.enum.js'
import { DEFAULT_LANGUAGE } from '../../localization/constants/defaults.constant.js'
import { Locale } from '../../localization/enums/locale.enum.js'
import { UN_NUMBER_NOT_REGULATED } from '../constants/un-number-not-regulated.constant.js'
import { SapLanguage } from '../../sap/enums/sap-language.enum.js'
import { exhaustiveCheck } from '../../../utils/helpers/exhaustive-check.helper.js'
import { SapLanguageMapper } from '../../sap/services/sap-language.mapper.js'

export class MapUnNumberSapService {
  static mapResultToUnNumber (response: SapGetUnNumberIndexResponse, language: Locale = DEFAULT_LANGUAGE): Omit<UnNumber, 'createdAt' | 'updatedAt'> {
    return {
      key: response.Dgkey,
      number: response.Dgnu,
      packingGroup: response.Pgro !== ''
        ? mapSapIValueToPackingGroup(response.Pgro)
        : PackingGroup.NOT_APPLICABLE,
      dangerLabel1: response.Dgcl1 !== '' ? response.Dgcl1 : null,
      dangerLabel2: response.Dgcl2 !== '' ? response.Dgcl2 : null,
      dangerLabel3: response.Dgcl3 !== '' ? response.Dgcl3 : null,
      isHazardous: MapUnNumberSapService.isDgnaHazardous(
        response.Dgna,
        SapLanguageMapper.fromLocale(language)
      ),
      description: response.Dgna !== ''
        ? response.Dgna
        : response.Dgnu === '0000'
          ? UN_NUMBER_NOT_REGULATED
          : null,
      language
    }
  }

  static mapResultsToUnNumbers (responses: SapGetUnNumberIndexResponse[] = [], language: Locale = DEFAULT_LANGUAGE): Omit<UnNumber, 'createdAt' | 'updatedAt'>[] {
    return responses.map(response => this.mapResultToUnNumber(response, language))
  }

  static mapUnNumberToResult (unNumber: UnNumber): SapGetUnNumberIndexResponse {
    const id = randomUUID()

    return {
      __metadata: {
        id,
        uri: `https://services.odata.org/OData${SAP_ENDPOINTS.UN_NUMBER.INDEX}/${id}`,
        type: 'ZMDX_TAOF_SRV.un'
      },
      Dgnu: unNumber.number,
      Dgna: unNumber.description ?? '',
      Pgro: unNumber.packingGroup ?? '',
      Dgcl1: unNumber.dangerLabel1 ?? '',
      Dgcl2: unNumber.dangerLabel2 ?? '',
      Dgcl3: unNumber.dangerLabel3 ?? '',
      Dgkey: '',
      Hnu: '',
      DgnuRemark: ''
    }
  }

  static mapUnNumbersToResults (unNumbers: UnNumber[]): SapGetUnNumberIndexResponse[] {
    return unNumbers.map(unNumber => this.mapUnNumberToResult(unNumber))
  }

  private static isDgnaHazardous (dgna: string, language: SapLanguage): boolean {
    switch (language) {
      case SapLanguage.EN:
        return dgna.includes('N.O.S.')
      case SapLanguage.NL:
        return dgna.includes('N.E.G.')
      case SapLanguage.FR:
        return dgna.includes('N.S.A.')
      case SapLanguage.DE:
        return dgna.includes('N.A.G.')
      case SapLanguage.ES:
        return dgna.includes('N.O.S.')
      default:
        exhaustiveCheck(language)
    }
  }
}
