import { before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import { createStubInstance, SinonStubbedInstance } from 'sinon'
import { expect } from 'expect'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { AuthContext } from '../../../../auth/auth.context.js'
import { GetUserSiteIndexUseCase } from '../../../../sharepoint/use-cases/get-user-site-index/get-user-site-index.use-case.js'
import { GetUserSiteIndexResponseBuilder } from '../../../../sharepoint/use-cases/get-user-site-index/builders/get-user-site-index.response.builder.js'
import { DownloadDocumentValidator } from '../download-document.validator.js'
import { ViewNameValidator } from '../../../validators/view-name.validator.js'
import { GetDocumentIndexUseCase } from '../../../../sharepoint/use-cases/get-document-index/get-document-index.use-case.js'
import { GetDocumentIndexResponseBuilder } from '../../../../sharepoint/use-cases/get-document-index/builders/get-document-index.response.builder.js'
import { GetDocumentIndexFileResponseBuilder } from '../../../../sharepoint/use-cases/get-document-index/builders/get-document-index-file.response.builder.js'
import { DownloadDocumentCommandBuilder } from './builders/download-document.command.builder.js'

describe('DownloadDocumentValidator', () => {
  let validator: DownloadDocumentValidator

  let authContext: SinonStubbedInstance<AuthContext>
  let getUserSiteIndexUseCase: SinonStubbedInstance<GetUserSiteIndexUseCase>
  let getDocumentIndexUseCase: SinonStubbedInstance<GetDocumentIndexUseCase>
  let viewNameValidator: SinonStubbedInstance<ViewNameValidator>

  const azureEntraUpn = randomUUID()
  const siteId = randomUUID()

  before(() => {
    TestBench.setupUnitTest()

    authContext = createStubInstance(AuthContext)
    getUserSiteIndexUseCase = createStubInstance(GetUserSiteIndexUseCase)
    getDocumentIndexUseCase = createStubInstance(GetDocumentIndexUseCase)
    viewNameValidator = createStubInstance(ViewNameValidator)

    authContext.getAzureEntraUpn.returns(azureEntraUpn)
    getUserSiteIndexUseCase.execute.resolves([
      new GetUserSiteIndexResponseBuilder()
        .withSiteId(siteId)
        .build()
    ])
    getDocumentIndexUseCase.execute.resolves(
      new GetDocumentIndexResponseBuilder()
        .withFiles([
          new GetDocumentIndexFileResponseBuilder().build()
        ])
        .build()
    )
    viewNameValidator.validateViewName.resolves()

    validator = new DownloadDocumentValidator(
      authContext,
      getUserSiteIndexUseCase,
      getDocumentIndexUseCase,
      viewNameValidator
    )
  })

  it('validates command', async () => {
    const command = new DownloadDocumentCommandBuilder()
      .withCustomerUuid(siteId)
      .build()

    await expect(validator.validateAndReturnMatchingSite(command)).resolves.not.toThrow()
    await expect(validator.validateDocumentAccessible(
      command,
      new GetUserSiteIndexResponseBuilder().build()
    )).resolves.not.toThrow()
  })

  it('throws error if no matching site was found', () => {
    getUserSiteIndexUseCase.execute.resolves([
      new GetUserSiteIndexResponseBuilder()
        .withSiteId(randomUUID())
        .build()
    ])

    const command = new DownloadDocumentCommandBuilder().build()

    expect(validator.validateAndReturnMatchingSite(command)).rejects.toThrow()
  })
})
