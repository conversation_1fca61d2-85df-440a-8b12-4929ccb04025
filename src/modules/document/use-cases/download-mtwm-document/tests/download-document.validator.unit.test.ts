import { before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import { createStubInstance } from 'sinon'
import { expect } from 'expect'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { AuthContext } from '../../../../auth/auth.context.js'
import { GetUserSiteIndexUseCase } from '../../../../sharepoint/use-cases/get-user-site-index/get-user-site-index.use-case.js'
import { GetUserSiteIndexResponseBuilder } from '../../../../sharepoint/use-cases/get-user-site-index/builders/get-user-site-index.response.builder.js'
import { DownloadDocumentValidator } from '../download-document.validator.js'
import { DownloadDocumentCommandBuilder } from './builders/download-document.command.builder.js'

describe('DownloadDocumentValidator', () => {
  before(() => TestBench.setupUnitTest())

  it('throws error if no matching site was found', () => {
    const authContext = createStubInstance(AuthContext)
    const getUserSiteIndexUseCase = createStubInstance(GetUserSiteIndexUseCase)

    getUserSiteIndexUseCase.execute.resolves([
      new GetUserSiteIndexResponseBuilder()
        .withSiteId(randomUUID())
        .build()
    ])

    const validator = new DownloadDocumentValidator(authContext, getUserSiteIndexUseCase)

    const command = new DownloadDocumentCommandBuilder().build()

    expect(validator.validateAndReturnMatchingSite(command)).rejects.toThrow()
  })
})
