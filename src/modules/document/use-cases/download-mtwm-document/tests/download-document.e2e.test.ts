import { after, before, describe, it, mock } from 'node:test'
import request from 'supertest'
import { expect } from 'expect'
import { HttpStatus } from '@nestjs/common'
import { EndToEndTestSetup } from '../../../../../../test/setup/end-to-end-test-setup.js'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { Permission } from '../../../../permission/permission.enum.js'
import { DownloadDocumentValidator } from '../download-document.validator.js'
import { GetUserSiteIndexResponseBuilder } from '../../../../sharepoint/use-cases/get-user-site-index/builders/get-user-site-index.response.builder.js'
import { DownloadDocumentCommandBuilder } from './builders/download-document.command.builder.js'

describe('DownloadDocument - E2E Tests', () => {
  let setup: EndToEndTestSetup

  before(async () => {
    setup = await TestBench.setupEndToEndTest()
  })

  after(async () => await setup.teardown())

  it('redirects to the url', async () => {
    const user = await setup.authContext.getUser([Permission.DOCUMENT_READ_MASTER_TABLE])

    const command = new DownloadDocumentCommandBuilder().build()

    const validatorMock = mock.method(DownloadDocumentValidator.prototype, 'validateAndReturnMatchingSite', () => {
      return new GetUserSiteIndexResponseBuilder().build()
    })

    const response = await request(setup.httpServer)
      .post(`/documents/download`)
      .set('Authorization', `Bearer ${user.token}`)
      .send(command)

    validatorMock.mock.restore()

    expect(response).toHaveStatus(HttpStatus.FOUND)
  })
})
