import { randomUUID } from 'crypto'
import { DownloadDocumentCommand } from '../../download-document.command.js'

export class DownloadDocumentCommandBuilder {
  private command: DownloadDocumentCommand

  constructor () {
    this.reset()
  }

  private reset (): this {
    this.command = new DownloadDocumentCommand()

    this.command.customerUuid = randomUUID()
    this.command.documentId = randomUUID()

    return this
  }

  public build (): DownloadDocumentCommand {
    const result = this.command

    this.reset()

    return result
  }
}
