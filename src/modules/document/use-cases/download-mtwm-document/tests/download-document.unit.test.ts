import { before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import { createStubInstance } from 'sinon'
import { AxiosError } from 'axios'
import { expect } from 'expect'
import { HttpStatus } from '@nestjs/common'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { DownloadDocumentUseCase } from '../download-document.use.case.js'
import { MwtmSharepointDownloadDocumentUseCase } from '../../../../sharepoint/use-cases/download-mtwm-document/download-mtwm-document.use-case.js'
import { DownloadDocumentValidator } from '../download-document.validator.js'
import { DocumentNotFoundError } from '../../../errors/document-not-found-error.js'
import { GetUserSiteIndexResponseBuilder } from '../../../../sharepoint/use-cases/get-user-site-index/builders/get-user-site-index.response.builder.js'
import { DownloadDocumentCommandBuilder } from './builders/download-document.command.builder.js'

describe('DownloadDocumentUseCase - Unit Tests', () => {
  before(() => TestBench.setupUnitTest())

  it('throws DocumentNotFoundError when Sharepoint returns 500', () => {
    const downloadUseCase = createStubInstance(MwtmSharepointDownloadDocumentUseCase)
    const validator = createStubInstance(DownloadDocumentValidator)

    validator.validateAndReturnMatchingSite.resolves(new GetUserSiteIndexResponseBuilder().build())
    validator.validateDocumentAccessible.resolves()

    const error = new AxiosError()
    error.status = HttpStatus.INTERNAL_SERVER_ERROR

    downloadUseCase.execute.rejects(error)

    const useCase = new DownloadDocumentUseCase(downloadUseCase, validator)
    const command = new DownloadDocumentCommandBuilder().build()

    expect(useCase.execute(command)).rejects.toThrow(DocumentNotFoundError)
  })

  it('throws the sharepoint error if not 500', () => {
    const downloadUseCase = createStubInstance(MwtmSharepointDownloadDocumentUseCase)
    const validator = createStubInstance(DownloadDocumentValidator)

    validator.validateAndReturnMatchingSite.resolves(new GetUserSiteIndexResponseBuilder().build())
    validator.validateDocumentAccessible.resolves()

    const error = new AxiosError()
    error.status = HttpStatus.BAD_GATEWAY

    downloadUseCase.execute.rejects(error)

    const useCase = new DownloadDocumentUseCase(downloadUseCase, validator)
    const command = new DownloadDocumentCommandBuilder().build()

    expect(useCase.execute(command)).rejects.toThrow(AxiosError)
  })

  it('returns the url', async () => {
    const downloadUseCase = createStubInstance(MwtmSharepointDownloadDocumentUseCase)
    const validator = createStubInstance(DownloadDocumentValidator)

    const url = randomUUID()

    validator.validateAndReturnMatchingSite.resolves(new GetUserSiteIndexResponseBuilder().build())
    validator.validateDocumentAccessible.resolves()
    downloadUseCase.execute.resolves(url)

    const useCase = new DownloadDocumentUseCase(downloadUseCase, validator)
    const command = new DownloadDocumentCommandBuilder().build()

    const result = await useCase.execute(command)

    expect(result).toBe(url)
  })
})
