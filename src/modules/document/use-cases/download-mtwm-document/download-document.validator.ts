import { ForbiddenException, Injectable } from '@nestjs/common'
import { GetUserSiteIndexResponse } from '../../../sharepoint/use-cases/get-user-site-index/get-user-site-index.response.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { GetUserSiteIndexUseCase } from '../../../sharepoint/use-cases/get-user-site-index/get-user-site-index.use-case.js'
import { DownloadDocumentCommand } from './download-document.command.js'

@Injectable()
export class DownloadDocumentValidator {
  constructor (
    private readonly authContext: AuthContext,
    private readonly getUserSiteIndexUseCase: GetUserSiteIndexUseCase
  ) {}

  async validateAndReturnMatchingSite (
    command: DownloadDocumentCommand
  ): Promise<GetUserSiteIndexResponse> {
    const entraUpn = this.authContext.getAzureEntraUpn()
    const siteResponses = await this.getUserSiteIndexUseCase.execute(entraUpn)
    const matchingSite = siteResponses.find(r => r.siteId === command.customerUuid)

    if (matchingSite === undefined) {
      throw new ForbiddenException()
    }

    return matchingSite
  }
}
