import { HttpStatus, Injectable } from '@nestjs/common'
import { AxiosError } from 'axios'
import { MwtmSharepointDownloadDocumentUseCase } from '../../../sharepoint/use-cases/download-mtwm-document/download-mtwm-document.use-case.js'
import { SharepointDownloadDocumentCommandBuilder } from '../../../sharepoint/use-cases/download-mtwm-document/tests/builders/download-document.command.builder.js'
import { DocumentNotFoundError } from '../../errors/document-not-found-error.js'
import { DownloadDocumentCommand } from './download-document.command.js'
import { DownloadDocumentValidator } from './download-document.validator.js'

@Injectable()
export class DownloadDocumentUseCase {
  constructor (
    private readonly sharepointDownloadUseCase: MwtmSharepointDownloadDocumentUseCase,
    private readonly validator: DownloadDocumentValidator
  ) {}

  async execute (
    command: DownloadDocumentCommand
  ): Promise<string> {
    const matchingSite = await this.validator.validateAndReturnMatchingSite(command)

    const sharepointCommand = new SharepointDownloadDocumentCommandBuilder()
      .withCurrentListId(matchingSite.publishedDocumentsLibraryId)
      .withListItemId(command.documentId)
      .withSiteId(command.customerUuid)
      .build()

    try {
      return await this.sharepointDownloadUseCase.execute(sharepointCommand)
    } catch (error) {
      const axiosError = error as AxiosError

      if (axiosError.status === HttpStatus.INTERNAL_SERVER_ERROR) {
        throw new DocumentNotFoundError({
          customerUuid: command.customerUuid,
          documentId: command.documentId
        })
      }

      throw error
    }
  }
}
