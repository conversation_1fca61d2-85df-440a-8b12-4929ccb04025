import { Module } from '@nestjs/common'
import { SharepointModule } from '../../../sharepoint/sharepoint.module.js'
import { ViewNameValidator } from '../../validators/view-name.validator.js'
import { PermissionsGuardModule } from '../../../permission/guards/permission.guard.module.js'
import { DownloadDocumentValidator } from './download-document.validator.js'
import { DownloadDocumentUseCase } from './download-document.use.case.js'
import { DownloadDocumentController } from './download-document.controller.js'

@Module({
  imports: [
    SharepointModule,
    PermissionsGuardModule
  ],
  controllers: [
    DownloadDocumentController
  ],
  providers: [
    DownloadDocumentUseCase,
    DownloadDocumentValidator,
    ViewNameValidator
  ]
})
export class DownloadDocumentModule {}
