import { Module } from '@nestjs/common'
import { SharepointModule } from '../../../sharepoint/sharepoint.module.js'
import { DownloadDocumentValidator } from './download-document.validator.js'
import { DownloadDocumentUseCase } from './download-document.use.case.js'
import { DownloadDocumentController } from './download-document.controller.js'

@Module({
  imports: [SharepointModule],
  controllers: [DownloadDocumentController],
  providers: [DownloadDocumentUseCase, DownloadDocumentValidator]
})
export class DownloadDocumentModule {}
