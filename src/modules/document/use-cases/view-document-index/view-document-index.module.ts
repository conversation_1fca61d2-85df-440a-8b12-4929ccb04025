import { Module } from '@nestjs/common'
import { SharepointModule } from '../../../sharepoint/sharepoint.module.js'
import { PermissionsGuardModule } from '../../../permission/guards/permission.guard.module.js'
import { ViewNameValidator } from '../../validators/view-name.validator.js'
import { ViewDocumentIndexController } from './view-document-index.controller.js'
import { ViewDocumentIndexUseCase } from './view-document-index.use-case.js'
import { ViewDocumentIndexValidator } from './view-document-index.validator.js'

@Module({
  imports: [
    SharepointModule,
    PermissionsGuardModule
  ],
  controllers: [
    ViewDocumentIndexController
  ],
  providers: [
    ViewDocumentIndexUseCase,
    ViewDocumentIndexValidator,
    ViewNameValidator
  ]
})
export class ViewDocumentIndexModule {}
