import { Injectable } from '@nestjs/common'
import { GetUserSiteIndexUseCase } from '../../../sharepoint/use-cases/get-user-site-index/get-user-site-index.use-case.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { GetUserSiteIndexResponse } from '../../../sharepoint/use-cases/get-user-site-index/get-user-site-index.response.js'
import { ForbiddenError } from '../../../exceptions/generic/forbidden.error.js'
import { ViewNameValidator } from '../../validators/view-name.validator.js'
import { ViewDocumentIndexFilterQuery } from './view-document-index.query.js'

@Injectable()
export class ViewDocumentIndexValidator {
  constructor (
    private readonly authContext: AuthContext,
    private readonly viewNameValidator: ViewNameValidator,
    private readonly getUserSiteIndexUseCase: GetUserSiteIndexUseCase
  ) {}

  async validateAndReturnMatchingSite (
    filterQuery: ViewDocumentIndexFilterQuery
  ): Promise<GetUserSiteIndexResponse> {
    await this.viewNameValidator.validateViewName(filterQuery.viewName)
    const entraUpn = this.authContext.getAzureEntraUpn()
    const siteResponses = await this.getUserSiteIndexUseCase.execute(entraUpn)

    const matchingSite = siteResponses.find(r => r.siteId === filterQuery.customerUuid)

    if (matchingSite === undefined) {
      throw new ForbiddenError()
    }

    const allowedWasteProducerIds = matchingSite.wasteProducerResource.map(wp => wp.id.toString())

    const isAllowed = filterQuery.wasteProducerIds
      .every(wp => allowedWasteProducerIds.includes(wp))

    if (!isAllowed) {
      throw new ForbiddenError()
    }

    return matchingSite
  }
}
