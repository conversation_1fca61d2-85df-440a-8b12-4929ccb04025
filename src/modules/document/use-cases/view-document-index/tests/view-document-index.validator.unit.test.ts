import { before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import { createStubInstance, assert } from 'sinon'
import { expect } from 'expect'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { ViewDocumentIndexValidator } from '../view-document-index.validator.js'
import { AuthContext } from '../../../../auth/auth.context.js'
import { GetUserSiteIndexUseCase } from '../../../../sharepoint/use-cases/get-user-site-index/get-user-site-index.use-case.js'
import { GetUserSiteIndexResponseBuilder } from '../../../../sharepoint/use-cases/get-user-site-index/builders/get-user-site-index.response.builder.js'
import { GetUserSiteIndexWasteProducerResponseBuilder } from '../../../../sharepoint/use-cases/get-user-site-index/builders/get-user-site-index-waste-producer.builder.js'
import { ForbiddenError } from '../../../../exceptions/generic/forbidden.error.js'
import { ViewNameValidator } from '../../../validators/view-name.validator.js'
import { ViewDocumentIndexFilterQueryBuilder } from './builders/view-document-index.filter.query.builder.js'

describe('ViewDocumentIndexValidator - Unit tests', () => {
  before(() => TestBench.setupUnitTest())

  it('throws error if no matching site was found', () => {
    const authContext = createStubInstance(AuthContext)
    const getUserSiteIndexUseCase = createStubInstance(GetUserSiteIndexUseCase)
    const viewNameValidator = createStubInstance(ViewNameValidator)

    getUserSiteIndexUseCase.execute.resolves([
      new GetUserSiteIndexResponseBuilder()
        .withSiteId(randomUUID())
        .build()
    ])
    viewNameValidator.validateViewName.resolves()

    const validator = new ViewDocumentIndexValidator(
      authContext,
      viewNameValidator,
      getUserSiteIndexUseCase
    )

    const filter = new ViewDocumentIndexFilterQueryBuilder()
      .withCustomerUuid(randomUUID())
      .build()

    expect(validator.validateAndReturnMatchingSite(filter)).rejects.toThrow(ForbiddenError)
  })

  it('throws error if theres a waste producer id in the query that does not exists in the matching site', () => {
    const authContext = createStubInstance(AuthContext)
    const getUserSiteIndexUseCase = createStubInstance(GetUserSiteIndexUseCase)
    const viewNameValidator = createStubInstance(ViewNameValidator)

    const siteUuid = randomUUID()

    const wasteProducerId = 1

    getUserSiteIndexUseCase.execute.resolves([
      new GetUserSiteIndexResponseBuilder()
        .withWasteProducerResource([
          new GetUserSiteIndexWasteProducerResponseBuilder()
            .withId(wasteProducerId + 1)
            .build()
        ])
        .withSiteId(siteUuid)
        .build()
    ])
    viewNameValidator.validateViewName.resolves()

    const validator = new ViewDocumentIndexValidator(
      authContext,
      viewNameValidator,
      getUserSiteIndexUseCase
    )

    const filter = new ViewDocumentIndexFilterQueryBuilder()
      .withCustomerUuid(siteUuid)
      .build()

    expect(validator.validateAndReturnMatchingSite(filter)).rejects.toThrow(ForbiddenError)
  })

  it('returns the matching site', async () => {
    const authContext = createStubInstance(AuthContext)
    const getUserSiteIndexUseCase = createStubInstance(GetUserSiteIndexUseCase)
    const viewNameValidator = createStubInstance(ViewNameValidator)

    const siteUuid = randomUUID()

    const wasteProducerId = 1

    getUserSiteIndexUseCase.execute.resolves([
      new GetUserSiteIndexResponseBuilder()
        .withWasteProducerResource([
          new GetUserSiteIndexWasteProducerResponseBuilder()
            .withId(wasteProducerId)
            .build()
        ])
        .withSiteId(siteUuid)
        .build()
    ])
    viewNameValidator.validateViewName.resolves()

    const validator = new ViewDocumentIndexValidator(
      authContext,
      viewNameValidator,
      getUserSiteIndexUseCase
    )

    const filter = new ViewDocumentIndexFilterQueryBuilder()
      .withCustomerUuid(siteUuid)
      .withWasteProducerIds([wasteProducerId.toString()])
      .build()

    const result = await validator.validateAndReturnMatchingSite(filter)

    expect(result).toBeDefined()
    assert.calledOnce(viewNameValidator.validateViewName)
  })
})
