import { Injectable } from '@nestjs/common'
import { GetDocumentIndexUseCase } from '../../../sharepoint/use-cases/get-document-index/get-document-index.use-case.js'
import { GetDocumentIndexCommandBuilder } from '../../../sharepoint/use-cases/get-document-index/builders/get-document-index.command.builder.js'
import { SharepointDocumentViewName } from '../../../sharepoint/enums/sharepoint-document-view-name.enum.js'
import { MwtmSharepointGetDocumentFiltersUseCase } from '../../../sharepoint/use-cases/get-document-filters/get-document-filters.use-case.js'
import { GetDocumentFiltersCommandBuilder } from '../../../sharepoint/use-cases/get-document-filters/builders/get-document-filters.command.builder.js'
import { GetUserSiteIndexResponse } from '../../../sharepoint/use-cases/get-user-site-index/get-user-site-index.response.js'
import { GetDocumentIndexResponse } from '../../../sharepoint/use-cases/get-document-index/get-document-index.response.js'
import { ViewDocumentIndexResponse } from './view-document-index.response.js'
import { ViewDocumentIndexQuery } from './view-document-index.query.js'
import { ViewDocumentIndexValidator } from './view-document-index.validator.js'

@Injectable()
export class ViewDocumentIndexUseCase {
  constructor (
    private readonly validator: ViewDocumentIndexValidator,
    private readonly getDocumentFiltersUseCase: MwtmSharepointGetDocumentFiltersUseCase,
    private readonly getDocumentIndexUseCase: GetDocumentIndexUseCase
  ) {}

  async execute (
    query: ViewDocumentIndexQuery
  ): Promise<ViewDocumentIndexResponse> {
    const matchingSite = await this.validator.validateAndReturnMatchingSite(query.filter)

    const command = new GetDocumentIndexCommandBuilder()
      .withViewName(query.filter.viewName)
      .withSiteId(query.filter.customerUuid)
      .withPublishedDocumentsLibraryId(matchingSite.publishedDocumentsLibraryId)
      .withWasteProducerIds(query.filter.wasteProducerIds.map(id => Number(id)))
      .withTextSearchFilter(query.search ?? '')
      .withStatusFilter(query.filter.status ?? '')
      .withNextLink(query.pagination?.key ?? '')
      .withWebUrl(matchingSite.webUrl)
      .withYearFilter(query.filter.year ?? '')
      .withRefextFilter(query.filter.refExt ?? '')
      .withTypeTransportDocFilter(query.filter.transportType ?? '')
      .build()

    const response = await this.getDocumentIndexUseCase.execute(command)

    if (query.filter.viewName === SharepointDocumentViewName.TFS) {
      await this.loadTfsType(query, matchingSite, response)
    }

    return new ViewDocumentIndexResponse(response, matchingSite)
  }

  private async loadTfsType (
    query: ViewDocumentIndexQuery,
    matchingSite: GetUserSiteIndexResponse,
    documentResponse: GetDocumentIndexResponse
  ): Promise<void> {
    const documentFiltersCommand = new GetDocumentFiltersCommandBuilder()
      .withViewName(query.filter.viewName)
      .withSiteId(query.filter.customerUuid)
      .withWasteProducerIds(query.filter.wasteProducerIds)
      .withWebUrl(matchingSite.webUrl)
      .withPublishedDocumentsLibraryId(matchingSite.publishedDocumentsLibraryId)
      .withTypeTFSListId(matchingSite.typeTFSListId)
      .withTypeTransportDocListId(matchingSite.typeTransportDocListId)
      .build()
    const externalResponse = await this.getDocumentFiltersUseCase.execute(documentFiltersCommand)

    const tfsTypeMap = new Map<string, string>()
    Object.entries(externalResponse.lookupValuesTFS).map(([key, value]) => {
      tfsTypeMap.set(key, value)
    })

    for (const file of documentResponse.files) {
      if (file.typeTFS !== '') {
        file.typeTFS = tfsTypeMap.get(file.typeTFS) ?? file.typeTFS
      }
    }
  }
}
