import { Injectable } from '@nestjs/common'
import { typeormPagination } from '@wisemen/pagination'
import { captureException } from '@sentry/nestjs'
import { MapCustomerSapService } from '../../services/map-customer-sap.service.js'
import { SapGetCustomerIndexUseCase } from '../../../sap/use-cases/get-customer-index/get-customer-index.use-case.js'
import { SapGetCustomerIndexResponse } from '../../../sap/use-cases/get-customer-index/get-customer-index.response.js'
import { UserCustomerAuthService } from '../../../auth/services/user-customer-auth.service.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { SapQuery } from '../../../sap/query/sap-query.js'
import { LogContext, OpenTelemetryLoggerService } from '../../../../utils/opentelemetry/modules/logger.service.js'
import { ViewCustomerIndexResponse } from './view-customer-index.response.js'
import { ViewCustomerIndexQuery } from './query/view-customer-index.query.js'

@Injectable()
export class ViewCustomerIndexUseCase {
  constructor (
    private readonly authContext: AuthContext,
    private readonly userCustomerAuthService: UserCustomerAuthService,
    private readonly sapGetCustomerIndex: SapGetCustomerIndexUseCase,
    private readonly logger: OpenTelemetryLoggerService
  ) {}

  public async execute (
    query: ViewCustomerIndexQuery
  ): Promise<ViewCustomerIndexResponse> {
    const userId = this.authContext.getAzureEntraUpn()

    try {
      this.logger.info({
        context: LogContext.INFO,
        body: { message: 'Start fetching customers' },
        attributes: { query, userId }
      })

      const pagination = typeormPagination(query.pagination)

      this.logger.info({
        context: LogContext.INFO,
        body: { message: 'User identified' },
        attributes: { userId }
      })

      const accessibleCustomerIds = await this.userCustomerAuthService.getAccessibleCustomerIds(
        userId
      )

      this.logger.info({
        context: LogContext.INFO,
        body: { message: 'Fetched accessible customer IDs' },
        attributes: { count: accessibleCustomerIds.length, accessibleCustomerIds }
      })

      if (accessibleCustomerIds.length === 0) {
        this.logger.info({
          context: LogContext.INFO,
          body: { message: 'User has no accessible customers, returning empty response' },
          attributes: { userId }
        })
        return new ViewCustomerIndexResponse([], null, pagination.take, pagination.skip)
      }

      const sapQuery = this.getSapQuery(query, accessibleCustomerIds)

      this.logger.info({
        context: LogContext.SAP,
        body: { message: 'Executing SAP query' },
        attributes: { sapQuery }
      })

      const sapResponse = await this.sapGetCustomerIndex.execute(sapQuery)

      this.logger.info({
        context: LogContext.SAP,
        body: { message: 'Received SAP response' },
        attributes: { count: sapResponse.items.length, items: sapResponse.items }
      })

      const customers = MapCustomerSapService.mapResultsToCustomers(sapResponse.items)

      this.logger.info({
        context: LogContext.INFO,
        body: { message: 'Mapped SAP customers' },
        attributes: { count: customers.length }
      })

      const response = new ViewCustomerIndexResponse(
        customers,
        null,
        pagination.take,
        pagination.skip
      )

      this.logger.info({
        context: LogContext.INFO,
        body: { message: 'Returning customer index response' },
        attributes: { response }
      })

      return response
    } catch (error) {
      captureException(error, {
        extra: { userId, query }
      })

      throw error
    }
  }

  private getSapQuery (
    query: ViewCustomerIndexQuery,
    accessibleCustomerIds: string[]
  ): SapQuery<SapGetCustomerIndexResponse> {
    const sapQuery = new SapQuery<SapGetCustomerIndexResponse>(query)

    if (accessibleCustomerIds.length > 0) {
      sapQuery.where((qb) => {
        qb.where('Customer', accessibleCustomerIds[0])
        for (let i = 1; i < accessibleCustomerIds.length; i++) {
          qb.orWhere('Customer', accessibleCustomerIds[i])
        }
        return qb
      })
    }

    return sapQuery
  }
}
