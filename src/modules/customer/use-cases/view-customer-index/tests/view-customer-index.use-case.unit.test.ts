import { before, describe, it, afterEach } from 'node:test'
import { randomUUID } from 'crypto'
import { createStubInstance, SinonStubbedInstance } from 'sinon'
import { expect } from 'expect'
import { randCity, randFullName, randStreetAddress, randZipCode } from '@ngneat/falso'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { ViewCustomerIndexUseCase } from '../view-customer-index.use-case.js'
import { Customer } from '../../../types/customer.type.js'
import { MapCustomerSapService } from '../../../services/map-customer-sap.service.js'
import { SapGetCustomerIndexUseCase } from '../../../../sap/use-cases/get-customer-index/get-customer-index.use-case.js'
import { SapPaginatedResponseBuilder } from '../../../../sap/tests/builders/responses/sap-paginated.response.builder.js'
import { SapGetCustomerIndexResponse } from '../../../../sap/use-cases/get-customer-index/get-customer-index.response.js'
import { AuthContext } from '../../../../auth/auth.context.js'
import { UserCustomerAuthService } from '../../../../auth/services/user-customer-auth.service.js'

describe('View customer index use-case unit test', () => {
  let useCase: ViewCustomerIndexUseCase

  let authContext: SinonStubbedInstance<AuthContext>
  let userCustomerAuthService: SinonStubbedInstance<UserCustomerAuthService>
  let sapGetCustomerIndex: SinonStubbedInstance<SapGetCustomerIndexUseCase>

  before(() => {
    TestBench.setupUnitTest()

    authContext = createStubInstance(AuthContext)
    userCustomerAuthService = createStubInstance(UserCustomerAuthService)
    sapGetCustomerIndex = createStubInstance(SapGetCustomerIndexUseCase)

    useCase = new ViewCustomerIndexUseCase(
      authContext,
      userCustomerAuthService,
      sapGetCustomerIndex
    )

    mockMethods()
  })

  afterEach(() => {
    mockMethods()
  })

  function mockMethods () {
    authContext.getAzureEntraUpn.returns(randomUUID())
    userCustomerAuthService.getAccessibleCustomerIds.resolves([])
    sapGetCustomerIndex.execute.resolves(
      new SapPaginatedResponseBuilder<SapGetCustomerIndexResponse>().build()
    )
  }

  it('Returns parsed result from SAP', async () => {
    const customers: Customer[] = [{
      id: randomUUID(),
      name: randFullName(),
      address: {
        countryCode: null,
        postalCode: randZipCode(),
        locality: randCity(),
        addressLine1: randStreetAddress(),
        addressLine2: null,
        coordinates: null
      }
    }]

    const sapResults = MapCustomerSapService.mapCustomersToResults(customers)

    userCustomerAuthService.getAccessibleCustomerIds.resolves(
      customers.map(customer => customer.id)
    )
    sapGetCustomerIndex.execute.resolves(
      new SapPaginatedResponseBuilder<SapGetCustomerIndexResponse>()
        .addItem(sapResults)
        .build()
    )

    const result = await useCase.execute({})

    expect(result.items).toStrictEqual(expect.arrayContaining(customers))
  })
})
