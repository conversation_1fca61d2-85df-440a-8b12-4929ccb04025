import { Injectable } from '@nestjs/common'
import { typeormPagination } from '@wisemen/pagination'
import { SapQuery } from '../../../sap/query/sap-query.js'
import { SapGetSalesOrganisationIndexUseCase } from '../../../sap/use-cases/get-sales-organisation-index/get-sales-organisation-index.use-case.js'
import { SapGetSalesOrganisationIndexResponse } from '../../../sap/use-cases/get-sales-organisation-index/get-sales-organisation-index.response.js'
import { SalesOrganisationSapMapper } from '../../services/sales-organisation-sap.mapper.js'
import { ViewSalesOrganisationIndexQuery } from './query/view-sales-organisation-index.query.js'
import { ViewSalesOrganisationIndexResponse } from './view-sales-organisation-index.response.js'

@Injectable()
export class ViewSalesOrganisationIndexUseCase {
  constructor (
    private readonly sapGetSalesOrganisationIndexUseCase: SapGetSalesOrganisationIndexUseCase
  ) {}

  public async execute (
    query: ViewSalesOrganisationIndexQuery
  ): Promise<ViewSalesOrganisationIndexResponse> {
    const sapQuery = this.getSapQuery(query)
    const sapResponse = await this.sapGetSalesOrganisationIndexUseCase.execute(sapQuery)

    const salesOrganisations = SalesOrganisationSapMapper.toSalesOrganisations(sapResponse)

    const pagination = typeormPagination(query.pagination)
    return new ViewSalesOrganisationIndexResponse(
      salesOrganisations,
      null,
      pagination.take,
      pagination.skip
    )
  }

  private getSapQuery (
    query: ViewSalesOrganisationIndexQuery
  ): SapQuery<SapGetSalesOrganisationIndexResponse> {
    const sapQuery = new SapQuery<SapGetSalesOrganisationIndexResponse>(query)

    return sapQuery
  }
}
