import { Controller, Get, Query } from '@nestjs/common'
import { <PERSON>piT<PERSON><PERSON>, ApiOAuth2, ApiOkResponse } from '@nestjs/swagger'
import { Permission } from '../../../permission/permission.enum.js'
import { Permissions } from '../../../permission/permission.decorator.js'
import { ViewSalesOrganisationIndexUseCase } from './view-sales-organisation-index.use-case.js'
import { ViewSalesOrganisationIndexQuery } from './query/view-sales-organisation-index.query.js'
import { ViewSalesOrganisationIndexResponse } from './view-sales-organisation-index.response.js'

@ApiTags('Sales organisation')
@ApiOAuth2([])
@Controller('sales-organisations')
export class ViewSalesOrganisationIndexController {
  constructor (
    private readonly useCase: ViewSalesOrganisationIndexUseCase
  ) { }

  @Get()
  @Permissions(
    Permission.NEWS_ITEM_MANAGE,
    Permission.ANNOUNCEMENT_MANAGE,
    Permission.WASTE_INQUIRY_READ,
    Permission.WASTE_INQUIRY_MANAGE
  )
  @ApiOkResponse({ type: ViewSalesOrganisationIndexResponse })
  public async viewSalesOrganisationIndex (
    @Query() query: ViewSalesOrganisationIndexQuery
  ): Promise<ViewSalesOrganisationIndexResponse> {
    return await this.useCase.execute(query)
  }
}
