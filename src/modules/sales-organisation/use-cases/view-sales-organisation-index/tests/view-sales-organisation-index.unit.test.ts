import { before, describe, it, afterEach } from 'node:test'
import { createStubInstance, SinonStubbedInstance, assert } from 'sinon'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { ViewSalesOrganisationIndexUseCase } from '../view-sales-organisation-index.use-case.js'
import { SapGetSalesOrganisationIndexUseCase } from '../../../../sap/use-cases/get-sales-organisation-index/get-sales-organisation-index.use-case.js'
import { GetSalesOrganisationIndexResponseBuilder } from '../../../../sap/use-cases/get-sales-organisation-index/tests/get-sales-organisation-index.response.builder.js'
import { ViewSalesOrganisationIndexQueryBuilder } from './view-sales-organisation-index.query.builder.js'

describe('ViewSalesOrganisationIndexUseCase - Unit test', () => {
  let useCase: ViewSalesOrganisationIndexUseCase

  let sapGetSalesOrganisationIndex: SinonStubbedInstance<SapGetSalesOrganisationIndexUseCase>

  before(() => {
    TestBench.setupUnitTest()

    sapGetSalesOrganisationIndex = createStubInstance(SapGetSalesOrganisationIndexUseCase)

    useCase = new ViewSalesOrganisationIndexUseCase(
      sapGetSalesOrganisationIndex
    )

    mockMethods()
  })

  afterEach(() => {
    mockMethods()
  })

  function mockMethods () {
    sapGetSalesOrganisationIndex.execute.resolves(
      [new GetSalesOrganisationIndexResponseBuilder().build()]
    )
  }

  it('Calls every method once', async () => {
    const query = new ViewSalesOrganisationIndexQueryBuilder().build()

    await useCase.execute(query)

    assert.calledOnce(sapGetSalesOrganisationIndex.execute)
  })
})
