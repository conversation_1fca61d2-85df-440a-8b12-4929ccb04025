import { ViewSalesOrganisationIndexQuery } from '../query/view-sales-organisation-index.query.js'

export class ViewSalesOrganisationIndexQueryBuilder {
  private query: ViewSalesOrganisationIndexQuery

  constructor () {
    this.reset()
  }

  reset (): this {
    this.query = new ViewSalesOrganisationIndexQuery()

    return this
  }

  build (): ViewSalesOrganisationIndexQuery {
    const result = this.query

    this.reset()

    return result
  }
}
