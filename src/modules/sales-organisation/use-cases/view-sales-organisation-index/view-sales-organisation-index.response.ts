import { ApiProperty } from '@nestjs/swagger'
import { PaginatedOffsetResponse } from '@wisemen/pagination'
import { calculatePageSize } from '../../../../utils/helpers/unknown-page-size.helper.js'
import { SalesOrganisation } from '../../types/sales-organisation.type.js'

class ViewSalesOrganisationResponse {
  @ApiProperty({ type: String })
  id: string

  @ApiProperty({ type: String })
  name: string

  constructor (salesOrganisation: SalesOrganisation) {
    this.id = salesOrganisation.id
    this.name = salesOrganisation.name
  }
}

export class ViewSalesOrganisationIndexResponse extends PaginatedOffsetResponse<
  ViewSalesOrganisationResponse
> {
  @ApiProperty({ type: ViewSalesOrganisationResponse, isArray: true })
  declare items: ViewSalesOrganisationResponse[]

  constructor (
    items: SalesOrganisation[],
    total: number | null,
    limit: number,
    offset: number
  ) {
    const result = items.map((salesOrganisation) => {
      return new ViewSalesOrganisationResponse(salesOrganisation)
    })

    if (total === null) {
      total = calculatePageSize(items, offset, limit)
    }

    super(result, total, limit, offset)
  }
}
