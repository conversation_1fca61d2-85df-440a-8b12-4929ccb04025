import { ApiProperty } from '@nestjs/swagger'
import { PaginatedOffsetSearchQuery } from '@wisemen/pagination'
import { IsUndefinable } from '@wisemen/validators'
import { Equals } from 'class-validator'

export class ViewSalesOrganisationIndexQuery extends PaginatedOffsetSearchQuery {
  @Equals(undefined)
  sort?: never

  @Equals(undefined)
  filter?: never

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  search?: string
}
