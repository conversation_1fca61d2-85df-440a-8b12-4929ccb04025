import { SapGetSalesOrganisationIndexResponse } from '../../sap/use-cases/get-sales-organisation-index/get-sales-organisation-index.response.js'
import { SalesOrganisation } from '../types/sales-organisation.type.js'

export class SalesOrganisationSapMapper {
  private static mapEntity (entity: SapGetSalesOrganisationIndexResponse): SalesOrganisation {
    return {
      id: entity.SalesOrganization,
      name: entity.SalesOrganizationName,
      countryCode: entity.Country,
      countryName: entity.CountryName
    }
  }

  public static toSalesOrganisation (
    sapEntity: SapGetSalesOrganisationIndexResponse
  ): SalesOrganisation {
    return this.mapEntity(sapEntity)
  }

  public static toSalesOrganisations (
    sapEntity: SapGetSalesOrganisationIndexResponse[]
  ): SalesOrganisation[] {
    return sapEntity.map(entity => this.mapEntity(entity))
  }
}
