import { Injectable } from '@nestjs/common'
import { UpdateWeeklyPlanningRequestCommand } from '../use-cases/update-weekly-planning-request/update-weekly-planning-request.command.js'
import { WeeklyPlanningRequest } from '../entities/weekly-planning-request.entity.js'
import { UserCustomerAuthService } from '../../auth/services/user-customer-auth.service.js'
import { CustomerNotAccessibleError } from '../../customer/errors/customer-not-accessible.error.js'
import { WeeklyPlanningRequestValidationUtil } from '../utils/weekly-planning-request-validation.util.js'
import { FieldMustBeNullError } from '../../exceptions/generic/field-must-be-null.error.js'
import { CustomerNotProvidedError } from '../../customer/errors/customer-not-provided.error.js'
import { CustomerPickUpAddressAuthService } from '../../auth/services/customer-pick-up-address-auth.service.js'
import { CustomerWasteProducerAuthService } from '../../auth/services/customer-waste-producer-auth.service.js'
import { UserWasteProducerAuthService } from '../../auth/services/user-waste-producer-auth.service.js'
import { WasteProducerNotAccessibleError } from '../../waste-producer/errors/waste-producer-not-accessible.error.js'
import { PickUpAddressNotAccessibleError } from '../../pick-up-address/errors/pick-up-address-not-accessible.error.js'
import { AuthContext } from '../../auth/auth.context.js'
import { MissingRequiredFieldError } from '../../exceptions/generic/missing-required-field.error.js'

@Injectable()
export class WeeklyPlanningRequestGeneralInfoValidator {
  constructor (
    private readonly authContext: AuthContext,
    private readonly userCustomerAuthService: UserCustomerAuthService,
    private readonly customerWasteProducerAuthService: CustomerWasteProducerAuthService,
    private readonly userWasteProducerAuthService: UserWasteProducerAuthService,
    private readonly customerPickUpAddressAuthService: CustomerPickUpAddressAuthService
  ) {}

  private validationUtil: WeeklyPlanningRequestValidationUtil

  async validateUpdate (
    command: UpdateWeeklyPlanningRequestCommand,
    weeklyPlanningRequest: Partial<WeeklyPlanningRequest>
  ) {
    this.validationUtil = new WeeklyPlanningRequestValidationUtil(weeklyPlanningRequest)

    const userId = this.authContext.getAzureEntraUpn()

    if (command.customerId !== undefined) {
      this.validateCustomerId(command.customerId, false)
    }

    if (command.customerId != null) {
      await this.canAccessCustomerId(userId, command.customerId)
    }

    if (command.wasteProducerId !== undefined) {
      this.validateWasteProducerId(command.wasteProducerId, false)
    }

    if (command.wasteProducerId != null) {
      await this.canAccessWasteProducerId(
        userId,
        command.wasteProducerId,
        weeklyPlanningRequest.customerId ?? null
      )
    }

    if (command.pickUpAddressIds !== undefined) {
      this.validatePickUpAddresses(command.pickUpAddressIds, false)
    }

    if (command.pickUpAddressIds != null) {
      await this.canAccessPickUpAddresses(
        command.pickUpAddressIds,
        weeklyPlanningRequest.customerId ?? null
      )
    }
  }

  async validateSubmit (
    weeklyPlanningRequest: WeeklyPlanningRequest
  ): Promise<void> {
    this.validationUtil = new WeeklyPlanningRequestValidationUtil(weeklyPlanningRequest)

    const userId = this.authContext.getAzureEntraUpn()

    this.validateCustomerId(weeklyPlanningRequest.customerId)

    if (weeklyPlanningRequest.customerId !== null) {
      await this.canAccessCustomerId(userId, weeklyPlanningRequest.customerId)
    }

    this.validateWasteProducerId(weeklyPlanningRequest.wasteProducerId)

    if (weeklyPlanningRequest.wasteProducerId != null) {
      await this.canAccessWasteProducerId(
        userId,
        weeklyPlanningRequest.wasteProducerId,
        weeklyPlanningRequest.customerId ?? null
      )
    }

    this.validatePickUpAddresses(weeklyPlanningRequest.pickUpAddressIds)

    await this.canAccessPickUpAddresses(
      weeklyPlanningRequest.pickUpAddressIds,
      weeklyPlanningRequest.customerId ?? null
    )
  }

  private validateCustomerId (
    customerId: string | null,
    isSubmit: boolean = true
  ): void {
    if (!this.validationUtil.isCustomerIdAllowed) {
      if (customerId !== null) {
        throw new FieldMustBeNullError({ pointer: '$.customerId' })
      }
    }

    if (isSubmit && this.validationUtil.isCustomerIdRequired) {
      if (customerId === null) {
        throw new MissingRequiredFieldError({ pointer: '$.customerId' })
      }
    }
  }

  private async canAccessCustomerId (
    userId: string,
    customerId: string
  ): Promise<void> {
    if (this.authContext.isInternalUser()) {
      return
    }

    const canUserAccessCustomer = await this.userCustomerAuthService.canUserAccessCustomer(
      userId,
      customerId
    )

    if (!canUserAccessCustomer) {
      throw new CustomerNotAccessibleError({ pointer: '$.customerId' })
    }
  }

  private validateWasteProducerId (
    wasteProducerId: string | null,
    isSubmit: boolean = true
  ): void {
    if (!this.validationUtil.isWasteProducerIdAllowed) {
      if (wasteProducerId !== null) {
        throw new FieldMustBeNullError({ pointer: '$.wasteProducerId' })
      }
    }

    if (isSubmit && this.validationUtil.isWasteProducerIdRequired) {
      if (wasteProducerId === null) {
        throw new MissingRequiredFieldError({ pointer: '$.wasteProducerId' })
      }
    }
  }

  private async canAccessWasteProducerId (
    userId: string,
    wasteProducerId: string,
    customerId: string | null
  ) {
    const pointer = '$.wasteProducerId'

    if (customerId === null) {
      throw new CustomerNotProvidedError({ pointer })
    }

    const canCustomerAccessWasteProducer = await this.customerWasteProducerAuthService
      .canCustomerAccessWasteProducer(
        customerId,
        wasteProducerId
      )

    if (!canCustomerAccessWasteProducer) {
      throw new WasteProducerNotAccessibleError({ pointer })
    }

    const canUserAccessWasteProducer = await this.userWasteProducerAuthService
      .canUserAccessWasteProducer(
        userId,
        customerId,
        wasteProducerId
      )

    if (!canUserAccessWasteProducer) {
      throw new WasteProducerNotAccessibleError({ pointer })
    }
  }

  private validatePickUpAddresses (
    pickUpAddressIds: string[],
    isSubmit: boolean = true
  ): void {
    if (!this.validationUtil.isPickUpAddressIdsAllowed) {
      if (pickUpAddressIds.length > 0) {
        throw new FieldMustBeNullError({ pointer: '$.pickUpAddressIds' })
      }
    }

    if (isSubmit && this.validationUtil.isPickUpAddressIdsRequired) {
      if (pickUpAddressIds.length === 0) {
        throw new MissingRequiredFieldError({ pointer: '$.pickUpAddressIds' })
      }
    }
  }

  private async canAccessPickUpAddresses (
    pickUpAddressIds: string[],
    customerId: string | null
  ): Promise<void> {
    if (pickUpAddressIds.length === 0) return

    const pointer = '$.pickUpAddressIds'

    if (customerId === null) {
      throw new CustomerNotProvidedError({ pointer })
    }

    const canCustomerAccessPickUpAddress = await this.customerPickUpAddressAuthService
      .canCustomerAccessPickUpAddresses(
        customerId,
        pickUpAddressIds
      )

    if (!canCustomerAccessPickUpAddress) {
      throw new PickUpAddressNotAccessibleError({ pointer })
    }
  }
}
