import { after, before, describe, it, mock } from 'node:test'
import { randomUUID } from 'crypto'
import request from 'supertest'
import { expect } from 'expect'
import { Repository } from 'typeorm'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { EndToEndTestSetup } from '../../../../../../test/setup/end-to-end-test-setup.js'
import { Permission } from '../../../../permission/permission.enum.js'
import { TestUser } from '../../../../../app/users/tests/setup-user.type.js'
import { WeeklyPlanningRequest } from '../../../entities/weekly-planning-request.entity.js'
import { ValidWeeklyPlanningRequestEntityBuilder } from '../../../tests/valid-weekly-planning-request-entity.builder.js'
import { UserCustomerAuthService } from '../../../../auth/services/user-customer-auth.service.js'
import { PickUpRequest } from '../../../../pick-up-request/entities/pick-up-request.entity.js'
import { ValidPickUpRequestEntityBuilder } from '../../../../pick-up-request/tests/valid-pick-up-request-entity.builder.js'
import { PickUpTransportMode } from '../../../../pick-up-request/enums/pick-up-transport-mode.enum.js'
import { ValidPickUpRequestMaterialBuilder } from '../../../../pick-up-request/tests/valid-pick-up-request-material.builder.js'
import { SapGetContractLineIndexUseCase } from '../../../../sap/use-cases/get-contract-line-index/get-contract-line-index.use-case.js'
import { GetContractLineIndexResponseBuilder } from '../../../../sap/use-cases/get-contract-line-index/tests/get-contract-line-index.response.builder.js'
import { UserCustomer } from '../../../../auth/entities/user-customer.entity.js'
import { UserCustomerEntityBuilder } from '../../../../auth/entities/user-customer.entity-builder.js'

describe('Submit weekly planning request e2e test', () => {
  let setup: EndToEndTestSetup

  let weeklyPlanningRequestRepository: Repository<WeeklyPlanningRequest>
  let pickUpRequestRepository: Repository<PickUpRequest>
  let userCustomerRepository: Repository<UserCustomer>

  let unauthorizedUser: TestUser
  let authorizedUser: TestUser

  before(async () => {
    setup = await TestBench.setupEndToEndTest()

    weeklyPlanningRequestRepository = setup.dataSource.getRepository(WeeklyPlanningRequest)
    pickUpRequestRepository = setup.dataSource.getRepository(PickUpRequest)
    userCustomerRepository = setup.dataSource.getRepository(UserCustomer)

    const [_unauthorizedUser, _authorizedUser] = await Promise.all([
      await setup.authContext.getUser([]),
      await setup.authContext.getUser([Permission.WEEKLY_PLANNING_REQUEST_MANAGE])
    ])

    unauthorizedUser = _unauthorizedUser
    authorizedUser = _authorizedUser

    await userCustomerRepository.save(
      new UserCustomerEntityBuilder()
        .withUserId(authorizedUser.user.azureEntraUpn)
        .withCustomerId(randomUUID())
        .build()
    )
  })

  after(async () => await setup.teardown())

  it('returns 401 when not authenticated', async () => {
    const response = await request(setup.httpServer)
      .post(`/weekly-planning-requests/${randomUUID()}/submit`)

    expect(response).toHaveStatus(401)
  })

  it('returns 403 when unauthorized', async () => {
    const response = await request(setup.httpServer)
      .post(`/weekly-planning-requests/${randomUUID()}/submit`)
      .set('Authorization', `Bearer ${unauthorizedUser.token}`)

    expect(response).toHaveStatus(403)
  })

  it('returns 404 when weekly planning request created by other user', async () => {
    const weeklyPlanningRequest = await weeklyPlanningRequestRepository.save(
      new ValidWeeklyPlanningRequestEntityBuilder()
        .createdByUserUuid(unauthorizedUser.user.uuid)
        .build()
    )

    const response = await request(setup.httpServer)
      .post(`/weekly-planning-requests/${weeklyPlanningRequest.uuid}/submit`)
      .set('Authorization', `Bearer ${authorizedUser.token}`)

    expect(response).toHaveStatus(404)
  })

  it('returns 200 when authorized and weekly planning request created by self', async () => {
    mock.method(UserCustomerAuthService.prototype, 'canUserAccessCustomer', () => {
      return true
    })

    const weeklyPlanningRequest = await weeklyPlanningRequestRepository.save(
      new ValidWeeklyPlanningRequestEntityBuilder()
        .createdByUserUuid(authorizedUser.user.uuid)
        .build()
    )

    const pickUpRequest = await pickUpRequestRepository.save(
      new ValidPickUpRequestEntityBuilder()
        .createdByUserUuid(authorizedUser.user.uuid)
        .withWeeklyPlanningRequestUuid(weeklyPlanningRequest.uuid)
        .withTransportMode(PickUpTransportMode.BULK_ISO_TANK)
        .withEndDate(null)
        .withIsReturnPackaging(null)
        .withTotalQuantityPallets(null)
        .withPackagingRemark(null)
        .addMaterial(
          new ValidPickUpRequestMaterialBuilder()
            .withBulkIsoTankProperties()
            .build(),
          true
        )
        .build()
    )

    mock.method(UserCustomerAuthService.prototype, 'canUserAccessCustomer', () => {
      return true
    })
    mock.method(SapGetContractLineIndexUseCase.prototype, 'execute', () => {
      return [
        new GetContractLineIndexResponseBuilder()
          .withKunnr(pickUpRequest.customerId!)
          .withKunnrWe(pickUpRequest.pickUpAddressIds[0])
          .withVbeln(pickUpRequest.materials[0].contractNumber)
          .withPosnr(pickUpRequest.materials[0].contractItem)
          .build()
      ]
    })

    const response = await request(setup.httpServer)
      .post(`/weekly-planning-requests/${weeklyPlanningRequest.uuid}/submit`)
      .set('Authorization', `Bearer ${authorizedUser.token}`)

    expect(response).toHaveStatus(200)
  })
})
