import { ApiProperty } from '@nestjs/swagger'
import { ContractLinePackagingType, ContractLinePackagingTypeApiProperty } from '../../../contract-line/enums/contract-line-packaging-type.enum.js'
import { ViewWprPickUpRequest } from './view-wpr-pick-up-request.type.js'

export class ViewWprPickUpRequestResponse {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({ type: String, format: 'date-time' })
  createdAt: string

  @ApiProperty({ type: String, format: 'date-time' })
  updatedAt: string

  @ApiProperty({ type: String })
  contractLineId: string

  @ApiProperty({ type: String })
  contractNumber: string

  @ApiProperty({ type: String })
  contractItem: string

  @ApiProperty({ type: String, nullable: true })
  customerReference: string | null

  @ApiProperty({ type: String, nullable: true })
  wasteMaterial: string | null

  @ApiProperty({ type: String, nullable: true })
  materialNumber: string | null

  @ApiProperty({ type: String, nullable: true })
  treatmentCenterName: string | null

  @ApiProperty({ type: String, nullable: true })
  installationName: string | null

  @ApiProperty({ type: String, nullable: true })
  customerId: string | null

  @ApiProperty({ type: String, nullable: true })
  customerName: string | null

  @ApiProperty({ type: String, nullable: true })
  wasteProducerId: string | null

  @ApiProperty({ type: String, nullable: true })
  wasteProducerName: string | null

  @ApiProperty({ type: String, nullable: true })
  pickUpAddressId: string | null

  @ApiProperty({ type: String, nullable: true })
  pickUpAddressName: string | null

  @ApiProperty({ type: String, nullable: true })
  asn: string | null

  @ApiProperty({ type: Boolean, nullable: true })
  tfs: boolean | null

  @ApiProperty({ type: Boolean, nullable: true })
  isHazardous: boolean | null

  @ContractLinePackagingTypeApiProperty({ nullable: true })
  packaged: ContractLinePackagingType | null

  @ApiProperty({ type: String, nullable: true })
  tcNumber: string | null

  @ApiProperty({ type: String, nullable: true })
  materialAnalysis: string | null

  @ApiProperty({ type: String, nullable: true })
  ewcCode: string | null

  @ApiProperty({ type: String, nullable: true })
  endTreatmentCenterId: string | null

  @ApiProperty({ type: String, nullable: true })
  endTreatmentCenterName: string | null

  @ApiProperty({ type: String, nullable: true })
  remarks: string | null

  @ApiProperty({ type: String, nullable: true })
  processCode: string | null

  @ApiProperty({ type: String, nullable: true })
  esnNumber: string | null

  @ApiProperty({ type: String, nullable: true })
  deliveryInfo: string | null

  @ApiProperty({ type: String, nullable: true })
  materialType: string | null

  @ApiProperty({ type: String, nullable: true })
  packagingIndicator: string | null

  @ApiProperty({ type: String, format: 'date', nullable: true })
  startDate: string | null

  @ApiProperty({ type: String, format: 'time', nullable: true })
  startTime: string | null

  constructor (item: ViewWprPickUpRequest) {
    this.uuid = item.pickUpRequestUuid
    this.createdAt = item.createdAt.toISOString()
    this.updatedAt = item.updatedAt.toISOString()
    this.contractLineId = item.contractLineId
    this.contractNumber = item.contractNumber
    this.contractItem = item.contractItem
    this.customerReference = item.customerReference
    this.wasteMaterial = item.wasteMaterial
    this.materialNumber = item.materialNumber
    this.treatmentCenterName = item.treatmentCenterName
    this.installationName = item.installationName
    this.customerId = item.customerId
    this.customerName = item.customerName
    this.wasteProducerId = item.wasteProducerId
    this.wasteProducerName = item.wasteProducerName
    this.pickUpAddressId = item.pickUpAddressId
    this.pickUpAddressName = item.pickUpAddressName
    this.asn = item.asn
    this.tfs = item.tfs
    this.isHazardous = item.isHazardous
    this.packaged = item.packaged
    this.tcNumber = item.tcNumber
    this.materialAnalysis = item.materialAnalysis
    this.ewcCode = item.ewcCode
    this.endTreatmentCenterId = item.endTreatmentCenterId
    this.endTreatmentCenterName = item.endTreatmentCenterName
    this.remarks = item.remarks
    this.processCode = item.processCode
    this.esnNumber = item.esnNumber
    this.deliveryInfo = item.deliveryInfo
    this.materialType = item.materialType
    this.packagingIndicator = item.packagingIndicator
    this.startDate = item.startDate
    this.startTime = item.startTime
  }
}
