import { toBoolean } from '../../../../utils/transformers/to-boolean.js'
import { SapDateFormatterService } from '../../../sap/services/sap-date-formatter.service.js'
import { SapGetGuidanceLetterIndexResponse } from '../../../sap/use-cases/get-guidance-letter-index/get-guidance-letter-index.response.js'
import { GuidanceLetter } from '../../types/guidance-letter.type.js'

export class MapGuidanceLetterSapService {
  static mapResultsToGuidanceLetters (
    sapResults: SapGetGuidanceLetterIndexResponse[]
  ): GuidanceLetter[] {
    return sapResults.map(result => ({
      shipmentId: result.TorId,
      contractNumber: result.Vbeln,
      guidanceLetter: toBoolean(result.PdfYbeg),
      attachment: toBoolean(result.PdfYdg),
      requestNumber: result.Reqno !== ''
        ? result.Reqno
        : null,
      wasteMaterial: result.Arktx !== ''
        ? result.Arktx
        : null,
      weightOrVolume: result.ZcsKwmeng !== ''
        ? parseFloat(result.ZcsKwmeng)
        : null,
      unit: result.Vrkme !== ''
        ? result.Vrkme
        : null,
      transportDate: result.Vdatu !== ''
        ? SapDateFormatterService.parseDateToString(result.Vdatu)
        : null,
      customerId: result.KunnrWe !== ''
        ? result.KunnrWe
        : null,
      wasteProducerId: result.KunnrY2 !== ''
        ? result.KunnrY2
        : null,
      pickUpAddressId: result.KunnrWe !== ''
        ? result.KunnrWe
        : null,
      customerName: result.KunnrDisplayName !== ''
        ? result.KunnrDisplayName
        : null,
      wasteProducerName: result.KunnrY2DisplayName !== ''
        ? result.KunnrY2DisplayName
        : null,
      pickUpAddressName: result.KunnrWeDisplayName !== ''
        ? result.KunnrWeDisplayName
        : null
    }))
  }
}
