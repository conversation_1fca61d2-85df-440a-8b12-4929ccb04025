import { Injectable } from '@nestjs/common'
import { typeormPagination } from '@wisemen/pagination'
import { SapQuery } from '../../../sap/query/sap-query.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { SapGetGuidanceLetterIndexResponse } from '../../../sap/use-cases/get-guidance-letter-index/get-guidance-letter-index.response.js'
import { SapGetGuidanceLetterIndexUseCase } from '../../../sap/use-cases/get-guidance-letter-index/get-guidance-letter-index.use-case.js'
import { UserWasteProducerAuthService } from '../../../auth/services/user-waste-producer-auth.service.js'
import { ViewGuidanceLetterIndexQuery } from './query/view-guidance-letter-index.query.js'
import { ViewGuidanceLetterIndexResponse } from './view-guidance-letter-index.response.js'
import { MapGuidanceLetterSapService } from './map-quidance-letter-index.mapper.js'
import { ViewGuidanceLetterIndexSortQueryKey } from './query/view-guidance-letter-index.sort-query.js'

@Injectable()
export class ViewGuidanceLetterIndexUseCase {
  constructor (
    private readonly authContext: AuthContext,
    private readonly userWasteProducerAuthService: UserWasteProducerAuthService,
    private readonly sapGetGuidanceLetterIndex: SapGetGuidanceLetterIndexUseCase
  ) {}

  public async execute (
    query: ViewGuidanceLetterIndexQuery
  ): Promise<ViewGuidanceLetterIndexResponse> {
    const sapQuery = await this.getSapQuery(query)
    const sapResult = await this.sapGetGuidanceLetterIndex.execute(sapQuery)

    const guidanceLetters = MapGuidanceLetterSapService.mapResultsToGuidanceLetters(sapResult)

    const pagination = typeormPagination(query.pagination)
    return new ViewGuidanceLetterIndexResponse(
      guidanceLetters,
      null,
      pagination.take,
      pagination.skip
    )
  }

  private async getSapQuery (
    query: ViewGuidanceLetterIndexQuery
  ): Promise<SapQuery<SapGetGuidanceLetterIndexResponse>> {
    const sapQuery = new SapQuery<SapGetGuidanceLetterIndexResponse>(
      query,
      {
        defaultOrderBy: {
          column: 'TorId',
          direction: 'desc'
        },
        keyMapper: (key: string) =>
          this.mapSortKeyToSapKey(key as ViewGuidanceLetterIndexSortQueryKey)
      }
    )
      .addSelect([
        'TorId',
        'PdfYbeg',
        'PdfYdg',
        'Reqno',
        'Vbeln',
        'Arktx',
        'KunnrWe',
        'ZcsKwmeng',
        'Vrkme',
        'Vdatu',
        'KunnrY2',
        'KunnrWe',
        'KunnrDisplayName',
        'KunnrWeDisplayName',
        'KunnrY2DisplayName'
      ])

    // Add filters based on selected customer and accessible waste producers
    const selectedCustomerId = this.authContext.getSelectedCustomerId()
    if (selectedCustomerId != null) {
      sapQuery.where('Kunnr', selectedCustomerId)

      const restrictedWasteProducerIds = await this.userWasteProducerAuthService
        .getRestrictedWasteProducerIds(
          this.authContext.getAzureEntraUpn(),
          selectedCustomerId
        )
      if (restrictedWasteProducerIds !== undefined && restrictedWasteProducerIds.length > 0) {
        sapQuery.andWhere((qb) => {
          for (let i = 0; i < restrictedWasteProducerIds.length; i++) {
            if (i === 0) {
              qb.where('KunnrY2', restrictedWasteProducerIds[i])
            } else {
              qb.orWhere('KunnrY2', restrictedWasteProducerIds[i])
            }
          }
          return qb
        })
      }
    }

    return sapQuery
  }

  private mapSortKeyToSapKey (
    key: ViewGuidanceLetterIndexSortQueryKey
  ): keyof SapGetGuidanceLetterIndexResponse {
    const mapping: Record<
      ViewGuidanceLetterIndexSortQueryKey,
      keyof SapGetGuidanceLetterIndexResponse
    > = {
      [ViewGuidanceLetterIndexSortQueryKey.SHIPMENT_ID]: 'TorId',
      [ViewGuidanceLetterIndexSortQueryKey.CONTRACT_NUMBER]: 'Vbeln',
      [ViewGuidanceLetterIndexSortQueryKey.REQUEST_NUMBER]: 'Reqno',
      [ViewGuidanceLetterIndexSortQueryKey.WASTE_MATERIAL]: 'Arktx',
      [ViewGuidanceLetterIndexSortQueryKey.TRANSPORT_DATE]: 'Vdatu',
      [ViewGuidanceLetterIndexSortQueryKey.CUSTOMER_ID]: 'Kunnr',
      [ViewGuidanceLetterIndexSortQueryKey.WASTE_PRODUCER_ID]: 'KunnrY2',
      [ViewGuidanceLetterIndexSortQueryKey.PICK_UP_ADDRESS_ID]: 'KunnrWe',
      [ViewGuidanceLetterIndexSortQueryKey.CUSTOMER_NAME]: 'KunnrDisplayName',
      [ViewGuidanceLetterIndexSortQueryKey.WASTE_PRODUCER_NAME]: 'KunnrY2DisplayName',
      [ViewGuidanceLetterIndexSortQueryKey.PICK_UP_ADDRESS_NAME]: 'KunnrWeDisplayName'
    }

    return mapping[key]
  }
}
