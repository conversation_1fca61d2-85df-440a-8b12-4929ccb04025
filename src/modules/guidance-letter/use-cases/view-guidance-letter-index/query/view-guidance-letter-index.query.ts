import { <PERSON><PERSON>y<PERSON>nique, Equals, <PERSON><PERSON><PERSON><PERSON>, ValidateNested } from 'class-validator'
import { ApiProperty } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { PaginatedOffsetSearchQuery } from '@wisemen/pagination'
import { IsUndefinable } from '@wisemen/validators'
import { ViewGuidanceLetterIndexSortQuery } from './view-guidance-letter-index.sort-query.js'

export class ViewGuidanceLetterIndexQuery extends PaginatedOffsetSearchQuery {
  @ApiProperty({ type: ViewGuidanceLetterIndexSortQuery, required: false, isArray: true })
  @Type(() => ViewGuidanceLetterIndexSortQuery)
  @ValidateNested({ each: true })
  @IsUndefinable()
  @IsArray()
  @ArrayUnique()
  sort?: ViewGuidanceLetterIndexSortQuery[]

  @Equals(undefined)
  filter?: never

  @Equals(undefined)
  search?: never
}
