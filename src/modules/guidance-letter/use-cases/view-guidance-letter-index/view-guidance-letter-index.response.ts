import { ApiProperty } from '@nestjs/swagger'
import { PaginatedOffsetResponse } from '@wisemen/pagination'
import { GuidanceLetter } from '../../types/guidance-letter.type.js'
import { calculatePageSize } from '../../../../utils/helpers/unknown-page-size.helper.js'

class GuidanceLetterResponse {
  @ApiProperty({ type: String })
  shipmentId: string

  @ApiProperty({ type: String })
  contractNumber: string

  @ApiProperty({ type: Boolean })
  guidanceLetter: boolean

  @ApiProperty({ type: Boolean })
  attachement: boolean

  @ApiProperty({ type: String, nullable: true })
  requestNumber: string | null

  @ApiProperty({ type: String, nullable: true })
  wasteMaterial: string | null

  @ApiProperty({ type: Number, nullable: true })
  weightOrVolume: number | null

  @ApiProperty({ type: String, nullable: true })
  unit: string | null

  @ApiProperty({ type: String, nullable: true })
  transportDate: string | null

  @ApiProperty({ type: String, nullable: true })
  customerId: string | null

  @ApiProperty({ type: String, nullable: true })
  wasteProducerId: string | null

  @ApiProperty({ type: String, nullable: true })
  pickUpAddressId: string | null

  @ApiProperty({ type: String, nullable: true })
  customerName: string | null

  @ApiProperty({ type: String, nullable: true })
  wasteProducerName: string | null

  @ApiProperty({ type: String, nullable: true })
  pickUpAddressName: string | null

  constructor (guidanceLetter: GuidanceLetter) {
    this.shipmentId = guidanceLetter.shipmentId
    this.contractNumber = guidanceLetter.contractNumber
    this.guidanceLetter = guidanceLetter.guidanceLetter
    this.attachement = guidanceLetter.attachement
    this.requestNumber = guidanceLetter.requestNumber
    this.wasteMaterial = guidanceLetter.wasteMaterial
    this.weightOrVolume = guidanceLetter.weightOrVolume
    this.unit = guidanceLetter.unit
    this.transportDate = guidanceLetter.transportDate
    this.customerId = guidanceLetter.customerId
    this.wasteProducerId = guidanceLetter.wasteProducerId
    this.pickUpAddressId = guidanceLetter.pickUpAddressId
    this.customerName = guidanceLetter.customerName
    this.wasteProducerName = guidanceLetter.wasteProducerName
    this.pickUpAddressName = guidanceLetter.pickUpAddressName
  }
}

export class ViewGuidanceLetterIndexResponse extends PaginatedOffsetResponse<
  GuidanceLetterResponse
> {
  @ApiProperty({ type: GuidanceLetterResponse, isArray: true })
  declare items: GuidanceLetterResponse[]

  constructor (items: GuidanceLetter[], total: number | null, limit: number, offset: number) {
    const result = items.map(contact => new GuidanceLetterResponse(contact))

    if (total === null) {
      total = calculatePageSize(items, offset, limit)
    }

    super(result, total, limit, offset)
  }
}
