import { Injectable } from '@nestjs/common'
import { SapGetGuidanceLetterIndexUseCase } from '../../../sap/use-cases/get-guidance-letter-index/get-guidance-letter-index.use-case.js'
import { SapGetGuidanceLetterIndexResponse } from '../../../sap/use-cases/get-guidance-letter-index/get-guidance-letter-index.response.js'
import { SapQuery } from '../../../sap/query/sap-query.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { UserWasteProducerAuthService } from '../../../auth/services/user-waste-producer-auth.service.js'
import { GuidanceLetterNotFoundError } from '../../errors/guidance-letter-not-found.error.js'
import { exhaustiveCheck } from '../../../../utils/helpers/exhaustive-check.helper.js'
import { GuidanceLetterDownloadTypeNotFoundError } from '../../errors/guidance-letter-download-type-not-found.error.js'
import { SapDownloadGuidanceLetterUseCase } from '../../../sap/use-cases/download-guidance-letter/download-guidance-letter.use.case.js'
import { DownloadGuidanceLetterQuery } from './download-guidance-letter.query.js'
import { DownloadGuidanceLetterType } from './download-guidance-letter.enum.js'

@Injectable()
export class DownloadGuidanceLetterSapUseCase {
  constructor (
    private readonly authContext: AuthContext,
    private readonly userWasteProducerAuthService: UserWasteProducerAuthService,
    private readonly sapGetGuidanceLetterIndex: SapGetGuidanceLetterIndexUseCase,
    private readonly sapDownloadGuidanceLetter: SapDownloadGuidanceLetterUseCase
  ) {}

  async execute (
    shipmentId: string,
    command: DownloadGuidanceLetterQuery
  ): Promise<Buffer> {
    const sapQuery = await this.getSapQuery(shipmentId)
    const sapResult = await this.sapGetGuidanceLetterIndex.execute(sapQuery)
    const guidanceLetter = sapResult.at(0)

    if (guidanceLetter === undefined) {
      throw new GuidanceLetterNotFoundError({ id: shipmentId })
    }

    const url = this.getDownloadUrl(guidanceLetter, command.type)
    return this.sapDownloadGuidanceLetter.execute(url)
  }

  private async getSapQuery (
    shipmentId: string
  ): Promise<SapQuery<SapGetGuidanceLetterIndexResponse>> {
    const sapQuery = new SapQuery<SapGetGuidanceLetterIndexResponse>()
      .addSelect([
        'TorId',
        'PdfYdgContent',
        'PdfYbegContent'
      ])
      .where('TorId', shipmentId)

    const selectedCustomerId = this.authContext.getSelectedCustomerId()

    if (selectedCustomerId != null) {
      sapQuery.andWhere('Kunnr', selectedCustomerId)

      const restrictedWasteProducerIds = await this.userWasteProducerAuthService
        .getRestrictedWasteProducerIds(
          this.authContext.getAzureEntraUpn(),
          selectedCustomerId
        )
      if (restrictedWasteProducerIds !== undefined && restrictedWasteProducerIds.length > 0) {
        sapQuery.andWhere((qb) => {
          for (let i = 0; i < restrictedWasteProducerIds.length; i++) {
            if (i === 0) {
              qb.where('KunnrY2', restrictedWasteProducerIds[i])
            } else {
              qb.orWhere('KunnrY2', restrictedWasteProducerIds[i])
            }
          }
          return qb
        })
      }
    }

    return sapQuery
  }

  private getDownloadUrl (
    guidanceLetter: SapGetGuidanceLetterIndexResponse,
    type: DownloadGuidanceLetterType
  ): string {
    this.validateGuidanceLetterField(guidanceLetter, type)

    switch (type) {
      case DownloadGuidanceLetterType.PRINT:
        return this.buildDownloadUrl(guidanceLetter.PdfYbegContent)
      case DownloadGuidanceLetterType.PREVIEW:
        return this.buildDownloadUrl(guidanceLetter.PdfYbegContent, true)
      case DownloadGuidanceLetterType.ATTACHMENT:
        return this.buildDownloadUrl(guidanceLetter.PdfYdgContent)
      default:
        return exhaustiveCheck(type)
    }
  }

  private validateGuidanceLetterField (
    guidanceLetter: SapGetGuidanceLetterIndexResponse,
    type: DownloadGuidanceLetterType
  ): void {
    const fieldKey = type === DownloadGuidanceLetterType.ATTACHMENT
      ? 'PdfYdgContent'
      : 'PdfYbegContent'
    const fieldValue = guidanceLetter[fieldKey]

    if (fieldValue === '' || fieldValue == null) {
      throw new GuidanceLetterDownloadTypeNotFoundError({ type })
    }
  }

  private buildDownloadUrl (
    downloadUrl: string,
    isOverlay: boolean = false
  ): string {
    return isOverlay
      ? downloadUrl.replace(/Overlay=false/, 'Overlay=true')
      : downloadUrl
  }
}
