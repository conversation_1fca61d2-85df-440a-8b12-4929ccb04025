export interface GuidanceLetter {
  shipmentId: string
  contractNumber: string
  guidanceLetter: boolean
  attachment: boolean
  requestNumber: string | null
  wasteMaterial: string | null
  weightOrVolume: number | null
  unit: string | null
  transportDate: string | null
  customerId: string | null
  wasteProducerId: string | null
  pickUpAddressId: string | null
  customerName: string | null
  wasteProducerName: string | null
  pickUpAddressName: string | null
}
