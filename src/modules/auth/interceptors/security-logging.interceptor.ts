import { Injectable, NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common'
import { Observable } from 'rxjs'
import { tap } from 'rxjs/operators'
import { Reflector } from '@nestjs/core'
import { Request } from 'express'
import { AuthContext } from '../auth.context.js'
import { SecurityLoggerService } from '../../../utils/opentelemetry/modules/security-logger.service.js'

// Decorator to mark endpoints that access sensitive data
export const SENSITIVE_DATA_KEY = 'sensitiveData'
export interface SensitiveDataMetadata {
  resourceType: string
  action: string
}

export const SensitiveData = (metadata: SensitiveDataMetadata) => 
  Reflector.createDecorator<SensitiveDataMetadata>({ key: SENSITIVE_DATA_KEY, value: metadata })

@Injectable()
export class SecurityLoggingInterceptor implements NestInterceptor {
  constructor(
    private readonly reflector: Reflector,
    private readonly authContext: AuthContext,
    private readonly securityLogger: SecurityLoggerService
  ) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const sensitiveDataMetadata = this.reflector.getAllAndOverride<SensitiveDataMetadata>(
      SENSITIVE_DATA_KEY,
      [context.getHandler(), context.getClass()]
    )

    if (!sensitiveDataMetadata) {
      return next.handle()
    }

    const request = context.switchToHttp().getRequest<Request>()
    const userUuid = this.authContext.getUserUuid()

    if (userUuid) {
      // Extract resource ID from request parameters
      const resourceId = this.extractResourceId(request, sensitiveDataMetadata.resourceType)

      return next.handle().pipe(
        tap(() => {
          this.securityLogger.logSensitiveDataAccess(
            userUuid,
            sensitiveDataMetadata.resourceType,
            resourceId || 'unknown',
            sensitiveDataMetadata.action,
            request.ip
          )
        })
      )
    }

    return next.handle()
  }

  private extractResourceId(request: Request, resourceType: string): string | null {
    // Try to extract resource ID from common parameter names
    const commonIdParams = ['id', 'uuid', `${resourceType}Id`, `${resourceType}Uuid`]
    
    for (const param of commonIdParams) {
      if (request.params[param]) {
        return request.params[param]
      }
    }

    // Try to extract from query parameters
    for (const param of commonIdParams) {
      if (request.query[param]) {
        return request.query[param] as string
      }
    }

    return null
  }
}
