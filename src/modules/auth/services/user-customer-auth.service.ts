import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { Any, Repository } from 'typeorm'
import { captureException } from '@sentry/nestjs'
import { UserCustomer } from '../entities/user-customer.entity.js'
import { UserWasteProducer } from '../entities/user-waste-producer.entity.js'
import { LogContext, OpenTelemetryLoggerService } from '../../../utils/opentelemetry/modules/logger.service.js'

export type AccessibleCustomersAndWasteProducers = {
  customerId: string
  accessibleWasteProducerIds: null | string[]
}

@Injectable()
export class UserCustomerAuthService {
  constructor (
    @InjectRepository(UserCustomer)
    private readonly userCustomerRepository: Repository<UserCustomer>,
    @InjectRepository(UserWasteProducer)
    private readonly userWasteProducerRepository: Repository<UserWasteProducer>,
    private readonly logger: OpenTelemetryLoggerService
  ) {}

  async canUserAccessCustomer (userId: string, customerId: string): Promise<boolean> {
    try {
      this.logger.info({
        context: LogContext.INFO,
        body: { message: 'Checking if user can access customer' },
        attributes: { userId, customerId }
      })

      const userCustomer = await this.userCustomerRepository.findOne({
        where: {
          customerId,
          userId: userId.toLowerCase()
        }
      })

      const canAccess = !!userCustomer

      this.logger.info({
        context: LogContext.INFO,
        body: { message: 'User access check result' },
        attributes: { userId, customerId, canAccess }
      })

      return canAccess
    } catch (error) {
      captureException(error, {
        extra: { userId, customerId }
      })

      throw error
    }
  }

  async getAccessibleCustomerIds (userId: string): Promise<string[]> {
    try {
      this.logger.info({
        context: LogContext.INFO,
        body: { message: 'Getting accessible customer IDs for user' },
        attributes: { userId }
      })

      const userCustomers = await this.userCustomerRepository.findBy({
        userId: userId.toLowerCase()
      })

      const customerIds = userCustomers.map(userCustomer => userCustomer.customerId)

      this.logger.info({
        context: LogContext.INFO,
        body: { message: 'Found accessible customer IDs' },
        attributes: { userId, count: customerIds.length, customerIds }
      })

      return customerIds
    } catch (error) {
      captureException(error, {
        extra: { userId }
      })

      throw error
    }
  }

  async getAccessibleCustomerIdsWithWasteProducerAccess (
    userId: string
  ): Promise<AccessibleCustomersAndWasteProducers[]> {
    const userCustomers = await this.userCustomerRepository.findBy({
      userId: userId.toLowerCase()
    })

    if (userCustomers.length === 0) return []

    const customerIds = userCustomers.map(userCustomer => userCustomer.customerId.toLowerCase())

    const userWasteProducers = await this.userWasteProducerRepository.findBy({
      userId,
      customerId: Any(customerIds)
    })

    return customerIds.map((customerId) => {
      const accessibleWasteProducerIds = userWasteProducers
        .filter(userWasteProducer => userWasteProducer.customerId === customerId)
        .map(userWasteProducer => userWasteProducer.wasteProducerId)

      return {
        customerId,
        accessibleWasteProducerIds: accessibleWasteProducerIds.length > 0
          ? accessibleWasteProducerIds
          : null
      }
    })
  }
}
