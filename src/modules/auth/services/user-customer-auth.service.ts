import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { Any, Repository } from 'typeorm'
import { UserCustomer } from '../entities/user-customer.entity.js'
import { UserWasteProducer } from '../entities/user-waste-producer.entity.js'

export type AccessibleCustomersAndWasteProducers = {
  customerId: string
  accessibleWasteProducerIds: null | string[]
}

@Injectable()
export class UserCustomerAuthService {
  constructor (
    @InjectRepository(UserCustomer)
    private readonly userCustomerRepository: Repository<UserCustomer>,
    @InjectRepository(UserWasteProducer)
    private readonly userWasteProducerRepository: Repository<UserWasteProducer>
  ) {}

  async canUserAccessCustomer (userId: string, customerId: string): Promise<boolean> {
    const userCustomer = await this.userCustomerRepository.findOne({
      where: {
        customerId,
        userId
      }
    })
    return !!userCustomer
  }

  async getAccessibleCustomerIds (userId: string): Promise<string[]> {
    const userCustomers = await this.userCustomerRepository.findBy({
      userId
    })

    return userCustomers.map(userCustomer => userCustomer.customerId)
  }

  async getAccessibleCustomerIdsWithWasteProducerAccess (
    userId: string
  ): Promise<AccessibleCustomersAndWasteProducers[]> {
    const userCustomers = await this.userCustomerRepository.findBy({
      userId
    })

    if (userCustomers.length === 0) return []

    const customerIds = userCustomers.map(userCustomer => userCustomer.customerId)

    const userWasteProducers = await this.userWasteProducerRepository.findBy({
      userId,
      customerId: Any(customerIds)
    })

    return customerIds.map((customerId) => {
      const accessibleWasteProducerIds = userWasteProducers
        .filter(userWasteProducer => userWasteProducer.customerId === customerId)
        .map(userWasteProducer => userWasteProducer.wasteProducerId)

      return {
        customerId,
        accessibleWasteProducerIds: accessibleWasteProducerIds.length > 0
          ? accessibleWasteProducerIds
          : null
      }
    })
  }
}
