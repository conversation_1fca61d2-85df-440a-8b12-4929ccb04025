import { Injectable, type NestMiddleware } from '@nestjs/common'
import type { Request, Response, NextFunction } from 'express'
import { createRemoteJWKSet, jwtVerify } from 'jose'
import { ConfigService } from '@nestjs/config'
import { context, trace, Span } from '@opentelemetry/api'
import { UnauthorizedError } from '../../exceptions/generic/unauthorized.error.js'
import { UserAuthService } from '../../../app/users/services/user-auth.service.js'
import { AuthContent, AuthContext } from '../auth.context.js'
import { setAuthContentSpanAttributes } from './auth-span.js'
import { SecurityLoggerService } from '../../../utils/opentelemetry/modules/security-logger.service.js'

export interface TokenContent {
  sub: string
  externalId?: string | null
  externalUpn?: string | null
  email: string
  firstName: string | null
  lastName: string | null
  roles?: string[] | null
}

@Injectable()
export class AuthMiddleware implements NestMiddleware {
  private readonly jwks: ReturnType<typeof createRemoteJWKSet>

  constructor(
    private readonly configService: ConfigService,
    private readonly authContext: AuthContext,
    private readonly userAuthService: UserAuthService,
    private readonly securityLogger: SecurityLoggerService
  ) {
    this.jwks = createRemoteJWKSet(
      new URL(this.configService.getOrThrow('AUTH_JWKS_ENDPOINT'))
    )
  }

  public async use(req: Request, _res: Response, next: NextFunction): Promise<void> {
    if (req.headers.authorization == null) {
      next()

      return
    }

    if (!req.headers.authorization.startsWith('Bearer ')) {
      throw new UnauthorizedError()
    }

    const token = req.headers.authorization.split(' ')[1]

    try {
      const content = await this.verify(token)

      // Log successful authentication
      this.securityLogger.logAuthenticationSuccess(
        content.uuid,
        content.azureEntraUpn,
        req.ip,
        req.headers['user-agent']
      )

      const span: Span | undefined = trace.getSpan(context.active())

      if (span) {
        setAuthContentSpanAttributes(span, content)
      }

      this.authContext.run(content, next)
    } catch (error) {
      // Log authentication failure
      this.securityLogger.logAuthenticationFailure(
        error instanceof Error ? error.message : 'Unknown authentication error',
        req.ip,
        req.headers['user-agent']
      )

      // Don't continue without authentication
      throw new UnauthorizedError()
    }
  }

  public async verify(token: string): Promise<AuthContent> {
    const { payload } = await jwtVerify<TokenContent>(token, this.jwks, {
      issuer: this.configService.getOrThrow('AUTH_ISSUER'),
      audience: this.configService.getOrThrow('AUTH_PROJECT_ID')
    })

    if (payload.externalId == null) {
      throw new UnauthorizedError('error.auth.azure_unauthorized_id')
    }
    if (payload.externalUpn == null) {
      throw new UnauthorizedError('error.auth.azure_unauthorized_upn')
    }
    if (payload.roles == null) {
      throw new UnauthorizedError('error.auth.azure_unauthorized_roles')
    }

    return await this.userAuthService.findOneByAuthPayload(payload)
  }
}
