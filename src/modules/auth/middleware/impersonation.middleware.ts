import { Injectable, type NestMiddleware } from '@nestjs/common'
import { NextFunction, Request, Response } from 'express'
import { Span, context, trace } from '@opentelemetry/api'
import { Permission } from '../../permission/permission.enum.js'
import { AuthContext } from '../auth.context.js'
import { PermissionGuardService } from '../../permission/guards/permission.guard.service.js'
import { UserImpersonateService } from '../../../app/users/services/user-impersonate.service.js'
import { decodeImpersonationToken, ImpersonationTokenPayload } from '../../../app/users/helpers/impersonation-token.helper.js'
import { setAuthContentSpanAttributes } from './auth-span.js'
import { SecurityLoggerService } from '../../../utils/opentelemetry/modules/security-logger.service.js'

@Injectable()
export class ImpersonationMiddleware implements NestMiddleware {
  constructor(
    private readonly authContext: AuthContext,
    private readonly userImpersonateService: UserImpersonateService,
    private readonly permissionService: PermissionGuardService,
    private readonly securityLogger: SecurityLoggerService
  ) { }

  public async use(req: Request, _res: Response, next: NextFunction): Promise<void> {
    if (req.headers['x-impersonate-user'] == null) {
      next()

      return
    }

    const impersonationToken = decodeImpersonationToken(req.headers['x-impersonate-user'] as string)
    const authContent = this.authContext.getAuthOrFail()

    try {
      await this.checkPermission(authContent.uuid)
      await this.checkImpersonateUserAccess(impersonationToken)

      authContent.impersonateUserUuid = impersonationToken.userUuid
      authContent.impersonateAzureEntraId = impersonationToken.azureEntraId
      authContent.impersonateAzureEntraUpn = impersonationToken.azureEntraUpn

      // Log successful impersonation
      this.securityLogger.logImpersonationStarted(
        authContent.uuid,
        impersonationToken.userUuid,
        req.ip
      )
    } catch (error) {
      // Log failed impersonation attempt
      this.securityLogger.logImpersonationFailed(
        authContent.uuid,
        impersonationToken.userUuid,
        error instanceof Error ? error.message : 'Unknown impersonation error',
        req.ip
      )
      throw error
    }

    const span: Span | undefined = trace.getSpan(context.active())

    if (span) {
      setAuthContentSpanAttributes(span, authContent)
    }

    this.authContext.run(authContent, next)
  }

  public async checkPermission(authUserUuid: string): Promise<void> {
    await this.permissionService.hasPermissions(authUserUuid, [Permission.USER_IMPERSONATE])
  }

  public async checkImpersonateUserAccess(
    impersonationToken: ImpersonationTokenPayload
  ): Promise<void> {
    await this.userImpersonateService.checkImpersonateUserAccess(
      impersonationToken.userUuid,
      impersonationToken.azureEntraId,
      impersonationToken.azureEntraUpn
    )
  }
}
