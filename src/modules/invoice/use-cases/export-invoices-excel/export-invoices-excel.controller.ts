import { Controller, Get, Query, Res } from '@nestjs/common'
import { ApiTags, ApiOkResponse, ApiOAuth2 } from '@nestjs/swagger'
import { Response } from 'express'
import { Permissions } from '../../../permission/permission.decorator.js'
import { Permission } from '../../../permission/permission.enum.js'
import { GlobalCustomerRequired } from '../../../auth/decorators/no-customer-required.decorator.js'
import { ExportInvoicesExcelUseCase } from './export-invoices-excel.use-case.js'
import { ExportInvoicesExcelQuery } from './queries/export-invoices-excel.query.js'

@ApiTags('Invoice')
@ApiOAuth2([])
@Controller('invoices/export-excel')
export class ExportInvoicesExcelController {
  constructor (
    private readonly useCase: ExportInvoicesExcelUseCase
  ) { }

  @Get()
  @GlobalCustomerRequired()
  @Permissions(Permission.INVOICE_READ, Permission.INVOICE_MANAGE)
  @ApiOkResponse({ description: 'Invoices exported as Excel' })
  public async exportInvoicesExcel (
    @Query() query: ExportInvoicesExcelQuery,
    @Res() res: Response
  ): Promise<void> {
    await this.useCase.execute(query, res)
  }
}
