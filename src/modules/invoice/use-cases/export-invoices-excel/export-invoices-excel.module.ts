import { Module } from '@nestjs/common'
import { SapModule } from '../../../sap/sap.module.js'
import { ViewInvoiceIndexModule } from '../view-invoice-index/view-invoice-index.module.js'
import { ViewInvoiceIndexUseCase } from '../view-invoice-index/view-invoice-index.use-case.js'
import { ExportInvoicesExcelUseCase } from './export-invoices-excel.use-case.js'
import { ExportInvoicesExcelController } from './export-invoices-excel.controller.js'

@Module({
  imports: [
    SapModule,
    ViewInvoiceIndexModule
  ],
  controllers: [ExportInvoicesExcelController],
  providers: [
    ExportInvoicesExcelUseCase,
    ViewInvoiceIndexUseCase
  ]
})
export class ExportInvoicesExcelModule {}
