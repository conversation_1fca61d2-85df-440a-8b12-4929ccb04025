import { Injectable } from '@nestjs/common'
import { Response } from 'express'
import { plainToInstance } from 'class-transformer'
import dayjs from 'dayjs'
import ExcelJS from 'exceljs'
import { streamFileResponse } from '../../../../utils/streams/stream-file-response.js'
import { MimeType } from '../../../files/enums/mime-type.enum.js'
import { ViewInvoiceIndexUseCase } from '../view-invoice-index/view-invoice-index.use-case.js'
import { InvoiceResponse } from '../view-invoice-index/view-invoice-index.response.js'
import { InvoiceColumnName } from '../../../dynamic-tables/enums/invoice-column-name.enum.js'
import { ExportInvoicesExcelQuery } from './queries/export-invoices-excel.query.js'

@Injectable()
export class ExportInvoicesExcelUseCase {
  constructor (
    private readonly viewInvoiceIndexUseCase: ViewInvoiceIndexUseCase
  ) { }

  async execute (
    query: ExportInvoicesExcelQuery,
    res: Response
  ): Promise<void> {
    const { columns, translatedColumns } = query.filter

    const sapInvoices = await this.getInvoices(query)
    const buffer = await this.mapInvoicesToExcelBuffer(
      sapInvoices,
      columns,
      translatedColumns
    )

    const fileName = `export${dayjs().format('YYYYMMDDHHmmss')}.xlsx`

    return streamFileResponse(
      buffer,
      MimeType.XLSX,
      fileName,
      res
    )
  }

  private async mapInvoicesToExcelBuffer (
    invoices: InvoiceResponse[],
    columns: InvoiceColumnName[],
    translatedColumns: string[]
  ): Promise<Buffer> {
    const workbook = new ExcelJS.Workbook()
    const worksheet = workbook.addWorksheet('Invoices')

    worksheet.addRow(translatedColumns)

    for (const invoice of invoices) {
      const values = columns.map((columnName) => {
        const value = invoice[columnName]
        return value != null ? value : '-'
      })
      worksheet.addRow(values)
    }

    return Buffer.from(await workbook.xlsx.writeBuffer())
  }

  private async getInvoices (
    query: ExportInvoicesExcelQuery
  ): Promise<InvoiceResponse[]> {
    const invoices: InvoiceResponse[] = []

    let offset: undefined | number = 0
    do {
      const response = await this.viewInvoiceIndexUseCase.execute(
        plainToInstance(ExportInvoicesExcelQuery, {
          filter: query.filter,
          pagination: {
            limit: 100,
            offset: offset
          }
        }))

      invoices.push(...response.items)

      const total = response.meta.offset + response.meta.limit
      offset = total >= response.meta.total
        ? undefined
        : (offset + response.meta.limit)
    } while (offset !== undefined)

    return invoices
  }
}
