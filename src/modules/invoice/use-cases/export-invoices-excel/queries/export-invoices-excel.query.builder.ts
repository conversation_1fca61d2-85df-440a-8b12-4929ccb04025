import { ExportInvoicesExcelQuery } from './export-invoices-excel.query.js'
import { ExportInvoicesExcelFilterQueryBuilder } from './export-invoices-excel-filter.query.builder.js'
import { ExportInvoicesExcelFilterQuery } from './export-invoices-excel.filter.query.js'

export class ExportInvoicesExcelQueryBuilder {
  private query: ExportInvoicesExcelQuery

  constructor () {
    this.reset()

    this.query.filter = new ExportInvoicesExcelFilterQueryBuilder().build()
  }

  private reset (): this {
    this.query = new ExportInvoicesExcelQuery()

    return this
  }

  withFilter (filter: ExportInvoicesExcelFilterQuery): this {
    this.query.filter = filter

    return this
  }

  public build (): ExportInvoicesExcelQuery {
    const result = this.query

    this.reset()

    return result
  }
}
