import { InvoiceColumnName } from '../../../../dynamic-tables/enums/invoice-column-name.enum.js'
import { InvoiceStatus } from '../../../enums/invoice-status.enum.js'
import { ExportInvoicesExcelFilterQuery } from './export-invoices-excel.filter.query.js'

export class ExportInvoicesExcelFilterQueryBuilder {
  private filterQuery: ExportInvoicesExcelFilterQuery

  constructor () {
    this.reset()
  }

  private reset (): this {
    this.filterQuery = new ExportInvoicesExcelFilterQuery()
    this.filterQuery.statuses = [InvoiceStatus.OUTSTANDING]
    this.filterQuery.columns = [InvoiceColumnName.INVOICE_NUMBER]
    this.filterQuery.translatedColumns = ['Invoice No.']

    return this
  }

  withStatuses (statuses: InvoiceStatus[]): this {
    this.filterQuery.statuses = statuses

    return this
  }

  withColumns (columns: InvoiceColumnName[]): this {
    this.filterQuery.columns = columns

    return this
  }

  withTranslatedColumns (translatedColumns: string[]): this {
    this.filterQuery.translatedColumns = translatedColumns

    return this
  }

  public build (): ExportInvoicesExcelFilterQuery {
    const result = this.filterQuery

    this.reset()

    return result
  }
}
