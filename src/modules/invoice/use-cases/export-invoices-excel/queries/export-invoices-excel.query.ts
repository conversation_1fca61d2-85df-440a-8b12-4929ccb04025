import { ApiProperty } from '@nestjs/swagger'
import { IsObject, ValidateNested } from 'class-validator'
import { Type } from 'class-transformer'
import { ViewInvoiceIndexQuery } from '../../view-invoice-index/queries/view-invoice-index.query.js'
import { ExportInvoicesExcelFilterQuery } from './export-invoices-excel.filter.query.js'

export class ExportInvoicesExcelQuery extends ViewInvoiceIndexQuery {
  @ApiProperty({ type: ExportInvoicesExcelFilterQuery })
  @Type(() => ExportInvoicesExcelFilterQuery)
  @ValidateNested()
  @IsObject()
  declare filter: ExportInvoicesExcelFilterQuery
}
