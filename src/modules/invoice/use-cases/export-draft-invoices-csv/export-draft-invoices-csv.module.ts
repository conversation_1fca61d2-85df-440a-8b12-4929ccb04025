import { Module } from '@nestjs/common'
import { SapModule } from '../../../sap/sap.module.js'
import { ViewDraftInvoiceIndexModule } from '../view-draft-invoice-index/view-draft-invoice-index.module.js'
import { ViewDraftInvoiceIndexUseCase } from '../view-draft-invoice-index/view-draft-invoice-index.use-case.js'
import { ExportDraftInvoicesCsvUseCase } from './export-draft-invoices-csv.use-case.js'
import { ExportDraftInvoicesCsvController } from './export-draft-invoices-csv.controller.js'

@Module({
  imports: [
    SapModule,
    ViewDraftInvoiceIndexModule
  ],
  controllers: [ExportDraftInvoicesCsvController],
  providers: [
    ExportDraftInvoicesCsvUseCase,
    ViewDraftInvoiceIndexUseCase
  ]
})
export class ExportDraftInvoicesCsvModule {}
