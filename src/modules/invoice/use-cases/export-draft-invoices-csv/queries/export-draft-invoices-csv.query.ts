import { ApiProperty } from '@nestjs/swagger'
import { IsObject, ValidateNested } from 'class-validator'
import { Type } from 'class-transformer'
import { ViewDraftInvoiceIndexQuery } from '../../view-draft-invoice-index/queries/view-draft-invoice-index.query.js'
import { ExportDraftInvoicesCsvFilterQuery } from './export-draft-invoices-csv.filter.query.js'

export class ExportDraftInvoicesCsvQuery extends ViewDraftInvoiceIndexQuery {
  @ApiProperty({ type: ExportDraftInvoicesCsvFilterQuery })
  @Type(() => ExportDraftInvoicesCsvFilterQuery)
  @ValidateNested()
  @IsObject()
  declare filter: ExportDraftInvoicesCsvFilterQuery
}
