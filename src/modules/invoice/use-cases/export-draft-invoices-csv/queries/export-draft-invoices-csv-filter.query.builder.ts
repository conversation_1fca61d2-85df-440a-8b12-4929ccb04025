import { DraftInvoiceColumnName } from '../../../../dynamic-tables/enums/draft-invoice-column-name.enum.js'
import { DraftInvoiceStatus } from '../../../enums/draft-invoice-status.enum.js'
import { ExportDraftInvoicesCsvFilterQuery } from './export-draft-invoices-csv.filter.query.js'

export class ExportDraftInvoicesCsvFilterQueryBuilder {
  private filterQuery: ExportDraftInvoicesCsvFilterQuery

  constructor () {
    this.reset()
  }

  private reset (): this {
    this.filterQuery = new ExportDraftInvoicesCsvFilterQuery()
    this.filterQuery.statuses = [DraftInvoiceStatus.APPROVED_BY_CUSTOMER]
    this.filterQuery.columns = [DraftInvoiceColumnName.INVOICE_NUMBER]
    this.filterQuery.translatedColumns = ['Invoice No.']

    return this
  }

  withStatuses (statuses: DraftInvoiceStatus[]): this {
    this.filterQuery.statuses = statuses

    return this
  }

  withColumns (columns: DraftInvoiceColumnName[]): this {
    this.filterQuery.columns = columns

    return this
  }

  withTranslatedColumns (translatedColumns: string[]): this {
    this.filterQuery.translatedColumns = translatedColumns

    return this
  }

  public build (): ExportDraftInvoicesCsvFilterQuery {
    const result = this.filterQuery

    this.reset()

    return result
  }
}
