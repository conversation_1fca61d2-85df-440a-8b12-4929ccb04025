import { FilterQuery } from '@wisemen/pagination'
import { ArrayMinSize, ArrayUnique, IsArray, IsEnum, IsNotEmpty, IsString } from 'class-validator'
import { ApiProperty } from '@nestjs/swagger'
import { InvoiceStatusApiProperty } from '../../../enums/invoice-status.enum.js'
import { InvoiceColumnNameApiProperty } from '../../../../dynamic-tables/enums/invoice-column-name.enum.js'
import { DraftInvoiceStatus } from '../../../enums/draft-invoice-status.enum.js'
import { DraftInvoiceColumnName } from '../../../../dynamic-tables/enums/draft-invoice-column-name.enum.js'

export class ExportDraftInvoicesCsvFilterQuery extends FilterQuery {
  @InvoiceStatusApiProperty({ isArray: true })
  @IsArray()
  @ArrayUnique()
  @ArrayMinSize(1)
  @IsEnum(DraftInvoiceStatus, { each: true })
  statuses: DraftInvoiceStatus[]

  @InvoiceColumnNameApiProperty({ isArray: true })
  @IsArray()
  @ArrayUnique()
  @ArrayMinSize(1)
  @IsEnum(DraftInvoiceColumnName, { each: true })
  columns: DraftInvoiceColumnName[]

  @ApiProperty({ type: [String], description: 'Translated column names for CSV header', example: ['Invoice No.'], isArray: true })
  @IsArray()
  @ArrayUnique()
  @ArrayMinSize(1)
  @IsNotEmpty({ each: true })
  @IsString({ each: true })
  translatedColumns: string[]
}
