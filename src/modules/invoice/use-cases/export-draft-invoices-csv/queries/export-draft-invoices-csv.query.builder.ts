import { ExportDraftInvoicesCsvFilterQueryBuilder } from './export-draft-invoices-csv-filter.query.builder.js'
import { ExportDraftInvoicesCsvFilterQuery } from './export-draft-invoices-csv.filter.query.js'
import { ExportDraftInvoicesCsvQuery } from './export-draft-invoices-csv.query.js'

export class ExportDraftInvoicesCsvQueryBuilder {
  private query: ExportDraftInvoicesCsvQuery

  constructor () {
    this.reset()

    this.query.filter = new ExportDraftInvoicesCsvFilterQueryBuilder().build()
  }

  private reset (): this {
    this.query = new ExportDraftInvoicesCsvQuery()

    return this
  }

  withFilter (filter: ExportDraftInvoicesCsvFilterQuery): this {
    this.query.filter = filter

    return this
  }

  public build (): ExportDraftInvoicesCsvQuery {
    const result = this.query

    this.reset()

    return result
  }
}
