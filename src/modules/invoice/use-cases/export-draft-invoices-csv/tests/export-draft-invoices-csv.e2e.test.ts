import { after, before, describe, it } from 'node:test'
import request from 'supertest'
import { expect } from 'expect'
import { HttpStatus } from '@nestjs/common'
import qs from 'qs'
import { EndToEndTestSetup } from '../../../../../../test/setup/end-to-end-test-setup.js'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { Permission } from '../../../../permission/permission.enum.js'
import { ExportDraftInvoicesCsvQueryBuilder } from '../queries/export-draft-invoices-csv.query.builder.js'

describe('Export draft invoice CSV - E2E Tests', () => {
  let setup: EndToEndTestSetup

  before(async () => {
    setup = await TestBench.setupEndToEndTest()
  })

  after(async () => await setup.teardown())

  it('exports the draft invoices as csv', async () => {
    const user = await setup.authContext.getUser([Permission.INVOICE_READ])
    const query = new ExportDraftInvoicesCsvQueryBuilder().build()

    const response = await request(setup.httpServer)
      .get(`/draft-invoices/export-csv`)
      .set('Authorization', `Bearer ${user.token}`)
      .query(qs.stringify(query))

    expect(response).toHaveStatus(HttpStatus.OK)
  })
})
