import { Injectable } from '@nestjs/common'
import { Response } from 'express'
import { plainToInstance } from 'class-transformer'
import dayjs from 'dayjs'
import { streamFileResponse } from '../../../../utils/streams/stream-file-response.js'
import { MimeType } from '../../../files/enums/mime-type.enum.js'
import { ViewDraftInvoiceIndexUseCase } from '../view-draft-invoice-index/view-draft-invoice-index.use-case.js'
import { DraftInvoiceResponse } from '../view-draft-invoice-index/view-draft-invoice-index.response.js'
import { DraftInvoiceColumnName } from '../../../dynamic-tables/enums/draft-invoice-column-name.enum.js'
import { ExportDraftInvoicesCsvQuery } from './queries/export-draft-invoices-csv.query.js'

@Injectable()
export class ExportDraftInvoicesCsvUseCase {
  constructor (
    private readonly viewDraftInvoiceIndexUseCase: ViewDraftInvoiceIndexUseCase
  ) { }

  async execute (
    query: ExportDraftInvoicesCsvQuery,
    res: Response
  ): Promise<void> {
    const { columns, translatedColumns } = query.filter

    const sapInvoices = await this.getDraftInvoices(query)
    const buffer = this.mapInvoicesToCSVBuffer(
      sapInvoices,
      columns,
      translatedColumns
    )

    const fileName = `export${dayjs().format('YYYYMMDDHHmmss')}.csv`

    return streamFileResponse(
      buffer,
      MimeType.CSV,
      fileName,
      res
    )
  }

  private mapInvoicesToCSVBuffer (
    invoices: DraftInvoiceResponse[],
    columns: DraftInvoiceColumnName[],
    translatedColumns: string[]
  ): Buffer {
    const csvLines = [translatedColumns.join(',')]

    for (const invoice of invoices) {
      const values = columns.map((columnName) => {
        const value = invoice[columnName]
        return value != null ? `"${String(value).replace(/"/g, '""')}"` : '-'
      })
      csvLines.push(values.join(','))
    }

    return Buffer.from(csvLines.join('\n'))
  }

  private async getDraftInvoices (
    query: ExportDraftInvoicesCsvQuery
  ): Promise<DraftInvoiceResponse[]> {
    const draftInvoices: DraftInvoiceResponse[] = []

    let offset: undefined | number = 0
    do {
      const response = await this.viewDraftInvoiceIndexUseCase.execute(
        plainToInstance(ExportDraftInvoicesCsvQuery, {
          filter: query.filter,
          pagination: {
            limit: 100,
            offset: offset
          }
        }))

      draftInvoices.push(...response.items)

      const total = response.meta.offset + response.meta.limit
      offset = total >= response.meta.total
        ? undefined
        : (offset + response.meta.limit)
    } while (offset !== undefined)

    return draftInvoices
  }
}
