import { Controller, Get, Query, Res } from '@nestjs/common'
import { ApiTags, ApiOkResponse, ApiOAuth2 } from '@nestjs/swagger'
import { Response } from 'express'
import { Permissions } from '../../../permission/permission.decorator.js'
import { Permission } from '../../../permission/permission.enum.js'
import { GlobalCustomerRequired } from '../../../auth/decorators/no-customer-required.decorator.js'
import { ExportDraftInvoicesCsvQuery } from './queries/export-draft-invoices-csv.query.js'
import { ExportDraftInvoicesCsvUseCase } from './export-draft-invoices-csv.use-case.js'

@ApiTags('Draft invoice')
@ApiOAuth2([])
@Controller('draft-invoices/export-csv')
export class ExportDraftInvoicesCsvController {
  constructor (
    private readonly useCase: ExportDraftInvoicesCsvUseCase
  ) { }

  @Get()
  @GlobalCustomerRequired()
  @Permissions(Permission.INVOICE_READ, Permission.INVOICE_MANAGE)
  @ApiOkResponse({ description: 'draft invoices exported as CSV' })
  public async exportInvoicesCsv (
    @Query() query: ExportDraftInvoicesCsvQuery,
    @Res() res: Response
  ): Promise<void> {
    await this.useCase.execute(query, res)
  }
}
