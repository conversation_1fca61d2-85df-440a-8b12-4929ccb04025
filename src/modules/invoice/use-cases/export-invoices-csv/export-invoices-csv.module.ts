import { Module } from '@nestjs/common'
import { SapModule } from '../../../sap/sap.module.js'
import { ViewInvoiceIndexModule } from '../view-invoice-index/view-invoice-index.module.js'
import { ViewInvoiceIndexUseCase } from '../view-invoice-index/view-invoice-index.use-case.js'
import { ExportInvoicesCsvUseCase } from './export-invoices-csv.use-case.js'
import { ExportInvoicesCsvController } from './export-invoices-csv.controller.js'

@Module({
  imports: [
    SapModule,
    ViewInvoiceIndexModule
  ],
  controllers: [ExportInvoicesCsvController],
  providers: [
    ExportInvoicesCsvUseCase,
    ViewInvoiceIndexUseCase
  ]
})
export class ExportInvoicesCsvModule {}
