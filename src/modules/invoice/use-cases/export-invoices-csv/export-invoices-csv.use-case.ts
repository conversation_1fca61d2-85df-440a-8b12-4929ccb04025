import { Injectable } from '@nestjs/common'
import { Response } from 'express'
import { plainToInstance } from 'class-transformer'
import dayjs from 'dayjs'
import { streamFileResponse } from '../../../../utils/streams/stream-file-response.js'
import { MimeType } from '../../../files/enums/mime-type.enum.js'
import { ViewInvoiceIndexUseCase } from '../view-invoice-index/view-invoice-index.use-case.js'
import { InvoiceResponse } from '../view-invoice-index/view-invoice-index.response.js'
import { InvoiceColumnName } from '../../../dynamic-tables/enums/invoice-column-name.enum.js'
import { ExportInvoicesCsvQuery } from './queries/export-invoices-csv.query.js'

@Injectable()
export class ExportInvoicesCsvUseCase {
  constructor (
    private readonly viewInvoiceIndexUseCase: ViewInvoiceIndexUseCase
  ) { }

  async execute (
    query: ExportInvoicesCsvQuery,
    res: Response
  ): Promise<void> {
    const { columns, translatedColumns } = query.filter

    const sapInvoices = await this.getInvoices(query)
    const buffer = this.mapInvoicesToCSVBuffer(
      sapInvoices,
      columns,
      translatedColumns
    )

    const fileName = `export${dayjs().format('YYYYMMDDHHmmss')}.csv`

    return streamFileResponse(
      buffer,
      MimeType.CSV,
      fileName,
      res
    )
  }

  private mapInvoicesToCSVBuffer (
    invoices: InvoiceResponse[],
    columns: InvoiceColumnName[],
    translatedColumns: string[]
  ): Buffer {
    const csvLines = [translatedColumns.join(',')]

    for (const invoice of invoices) {
      const values = columns.map((columnName) => {
        const value = invoice[columnName]
        return value != null ? `"${String(value).replace(/"/g, '""')}"` : '-'
      })
      csvLines.push(values.join(','))
    }

    return Buffer.from(csvLines.join('\n'))
  }

  private async getInvoices (
    query: ExportInvoicesCsvQuery
  ): Promise<InvoiceResponse[]> {
    const invoices: InvoiceResponse[] = []

    let offset: undefined | number = 0
    do {
      const response = await this.viewInvoiceIndexUseCase.execute(
        plainToInstance(ExportInvoicesCsvQuery, {
          filter: query.filter,
          pagination: {
            limit: 100,
            offset: offset
          }
        }))

      invoices.push(...response.items)

      const total = response.meta.offset + response.meta.limit
      offset = total >= response.meta.total
        ? undefined
        : (offset + response.meta.limit)
    } while (offset !== undefined)

    return invoices
  }
}
