import { Controller, Get, Query, Res } from '@nestjs/common'
import { ApiTags, ApiOkResponse, ApiOAuth2 } from '@nestjs/swagger'
import { Response } from 'express'
import { Permissions } from '../../../permission/permission.decorator.js'
import { Permission } from '../../../permission/permission.enum.js'
import { GlobalCustomerRequired } from '../../../auth/decorators/no-customer-required.decorator.js'
import { ExportInvoicesCsvUseCase } from './export-invoices-csv.use-case.js'
import { ExportInvoicesCsvQuery } from './queries/export-invoices-csv.query.js'

@ApiTags('Invoice')
@ApiOAuth2([])
@Controller('invoices/export-csv')
export class ExportInvoicesCsvController {
  constructor (
    private readonly useCase: ExportInvoicesCsvUseCase
  ) { }

  @Get()
  @GlobalCustomerRequired()
  @Permissions(Permission.INVOICE_READ, Permission.INVOICE_MANAGE)
  @ApiOkResponse({ description: 'Invoices exported as CSV' })
  public async exportInvoicesCsv (
    @Query() query: ExportInvoicesCsvQuery,
    @Res() res: Response
  ): Promise<void> {
    await this.useCase.execute(query, res)
  }
}
