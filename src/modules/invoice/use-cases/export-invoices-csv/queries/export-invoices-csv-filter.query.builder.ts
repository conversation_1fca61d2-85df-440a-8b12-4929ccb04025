import { InvoiceColumnName } from '../../../../dynamic-tables/enums/invoice-column-name.enum.js'
import { InvoiceStatus } from '../../../enums/invoice-status.enum.js'
import { ExportInvoicesCsvFilterQuery } from './export-invoices-csv.filter.query.js'

export class ExportInvoicesCsvFilterQueryBuilder {
  private filterQuery: ExportInvoicesCsvFilterQuery

  constructor () {
    this.reset()
  }

  private reset (): this {
    this.filterQuery = new ExportInvoicesCsvFilterQuery()
    this.filterQuery.statuses = [InvoiceStatus.OUTSTANDING]
    this.filterQuery.columns = [InvoiceColumnName.INVOICE_NUMBER]
    this.filterQuery.translatedColumns = ['Invoice No.']

    return this
  }

  withStatuses (statuses: InvoiceStatus[]): this {
    this.filterQuery.statuses = statuses

    return this
  }

  withColumns (columns: InvoiceColumnName[]): this {
    this.filterQuery.columns = columns

    return this
  }

  withTranslatedColumns (translatedColumns: string[]): this {
    this.filterQuery.translatedColumns = translatedColumns

    return this
  }

  public build (): ExportInvoicesCsvFilterQuery {
    const result = this.filterQuery

    this.reset()

    return result
  }
}
