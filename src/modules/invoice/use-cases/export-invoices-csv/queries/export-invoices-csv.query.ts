import { ApiProperty } from '@nestjs/swagger'
import { IsObject, ValidateNested } from 'class-validator'
import { Type } from 'class-transformer'
import { ViewInvoiceIndexQuery } from '../../view-invoice-index/queries/view-invoice-index.query.js'
import { ExportInvoicesCsvFilterQuery } from './export-invoices-csv.filter.query.js'

export class ExportInvoicesCsvQuery extends ViewInvoiceIndexQuery {
  @ApiProperty({ type: ExportInvoicesCsvFilterQuery })
  @Type(() => ExportInvoicesCsvFilterQuery)
  @ValidateNested()
  @IsObject()
  declare filter: ExportInvoicesCsvFilterQuery
}
