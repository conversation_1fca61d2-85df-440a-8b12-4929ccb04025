import { ExportInvoicesCsvQuery } from './export-invoices-csv.query.js'
import { ExportInvoicesCsvFilterQueryBuilder } from './export-invoices-csv-filter.query.builder.js'
import { ExportInvoicesCsvFilterQuery } from './export-invoices-csv.filter.query.js'

export class ExportInvoicesCsvQueryBuilder {
  private query: ExportInvoicesCsvQuery

  constructor () {
    this.reset()

    this.query.filter = new ExportInvoicesCsvFilterQueryBuilder().build()
  }

  private reset (): this {
    this.query = new ExportInvoicesCsvQuery()

    return this
  }

  withFilter (filter: ExportInvoicesCsvFilterQuery): this {
    this.query.filter = filter

    return this
  }

  public build (): ExportInvoicesCsvQuery {
    const result = this.query

    this.reset()

    return result
  }
}
