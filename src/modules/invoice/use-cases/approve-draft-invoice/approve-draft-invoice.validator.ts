import { ForbiddenException, Injectable } from '@nestjs/common'
import { AuthContext } from '../../../auth/auth.context.js'
import { UserCustomerAuthService } from '../../../auth/services/user-customer-auth.service.js'
import { APPROVABLE_DRAFT_INVOICE_STATUSES, INTERNAL_APPROVABLE_STATUSES } from '../../enums/draft-invoice-status.enum.js'
import { NonApproveOrRejectableDraftInvoiceError } from '../../errors/non-approve-or-rejectable-draft-invoice.error.js'
import { SapGetDraftInvoiceIndexUseCase } from '../../../sap/use-cases/get-draft-invoice-index/get-draft-invoice-index.use-case.js'
import { DraftInvoice } from '../../types/draft-invoice-type.js'
import { SapGetDraftInvoiceIndexResponse } from '../../../sap/use-cases/get-draft-invoice-index/get-draft-invoice-index.response.js'
import { SapQuery } from '../../../sap/query/sap-query.js'
import { InvoiceNotFoundError } from '../../errors/invoice-not-found.error.js'
import { DraftInvoiceMapper } from '../../mappers/draft-invoice.mapper.js'

@Injectable()
export class ApproveDraftInvoiceValidator {
  constructor (
    private readonly sapGetDraftInvoiceIndexUseCase: SapGetDraftInvoiceIndexUseCase,
    private readonly authContext: AuthContext,
    private readonly userCustomerAuthService: UserCustomerAuthService
  ) {}

  async validate (
    invoiceNumber: string
  ): Promise<void> {
    const invoice = await this.getInvoiceOrFail(invoiceNumber)

    const canAccessCustomer = await this.userCustomerAuthService.canUserAccessCustomer(
      this.authContext.getAzureEntraUpn(),
      invoice.payerId
    )

    if (!canAccessCustomer) {
      throw new ForbiddenException()
    }

    if (!APPROVABLE_DRAFT_INVOICE_STATUSES.includes(invoice.status)) {
      throw new NonApproveOrRejectableDraftInvoiceError()
    }

    const isInternalApproval = INTERNAL_APPROVABLE_STATUSES.includes(invoice.status)

    const isInternalUser = this.authContext.isInternalUser()
    if (isInternalApproval !== isInternalUser) {
      throw new ForbiddenException()
    }
  }

  private async getInvoiceOrFail (
    invoiceNumber: string
  ): Promise<DraftInvoice> {
    const sapQuery = new SapQuery<SapGetDraftInvoiceIndexResponse>()
      .where('Vbeln', invoiceNumber)
      .setTop(1)

    const result = await this.sapGetDraftInvoiceIndexUseCase.execute(sapQuery)

    if (result.length === 0) {
      throw new InvoiceNotFoundError({ invoiceNumber })
    }

    return DraftInvoiceMapper.fromSapResponse(result[0])
  }
}
