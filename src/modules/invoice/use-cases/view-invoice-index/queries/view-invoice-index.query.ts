import { ApiProperty } from '@nestjs/swagger'
import { PaginatedOffsetSearchQuery } from '@wisemen/pagination'
import { ArrayMinSize, ArrayUnique, IsObject, IsOptional, IsString, ValidateNested } from 'class-validator'
import { Type } from 'class-transformer'
import { IsUndefinable } from '@wisemen/validators'
import { ViewInvoiceIndexFilterQuery } from './view-invoice-index.filter.query.js'
import { ViewInvoiceIndexSortQuery } from './view-invoice-index-sort.query.js'

export class ViewInvoiceIndexQuery extends PaginatedOffsetSearchQuery {
  @ApiProperty({ type: ViewInvoiceIndexSortQuery, isArray: true, required: false })
  @IsUndefinable()
  @ArrayMinSize(1)
  @ArrayUnique()
  @Type(() => ViewInvoiceIndexSortQuery)
  @ValidateNested({ each: true })
  sort?: ViewInvoiceIndexSortQuery[]

  @ApiProperty({ type: ViewInvoiceIndexFilterQuery })
  @Type(() => ViewInvoiceIndexFilterQuery)
  @ValidateNested()
  @IsObject()
  filter: ViewInvoiceIndexFilterQuery

  @ApiProperty({ type: String, required: false })
  @IsOptional()
  @IsString()
  search?: string
}
