import { ApiProperty } from '@nestjs/swagger'
import { PaginatedOffsetSearchQuery } from '@wisemen/pagination'
import { Equals, IsObject, ValidateNested } from 'class-validator'
import { Type } from 'class-transformer'
import { ViewInvoiceIndexFilterQuery } from './view-invoice-index.filter.query.js'

export class ViewInvoiceIndexQuery extends PaginatedOffsetSearchQuery {
  @Equals(undefined)
  sort?: never

  @ApiProperty({ type: ViewInvoiceIndexFilterQuery })
  @Type(() => ViewInvoiceIndexFilterQuery)
  @ValidateNested()
  @IsObject()
  filter: ViewInvoiceIndexFilterQuery

  @Equals(undefined)
  search?: never
}
