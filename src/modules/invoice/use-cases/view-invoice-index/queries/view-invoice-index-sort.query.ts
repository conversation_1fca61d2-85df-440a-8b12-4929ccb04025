import { ApiProperty } from '@nestjs/swagger'
import { SortDirection, SortDirectionApiProperty, SortQuery } from '@wisemen/pagination'
import { IsEnum } from 'class-validator'

export enum ViewInvoiceIndexSortKey {
  INVOICE_NUMBER = 'invoice_number',
  CUSTOMER_REFERENCE = 'customer_reference',
  ISSUE_DATE = 'issue_date',
  PAYER_NAME = 'payer_name',
  CUSTOMER_NAME = 'customer_name',
  ACCOUNT_DOCUMENT_NUMBER = 'account_document_number',
  DUE_DATE = 'due_date',
  NET_AMOUNT = 'net_amount',
  COMPANY_NAME = 'company_name'
}

export class ViewInvoiceIndexSortQuery extends SortQuery {
  @ApiProperty({ enum: ViewInvoiceIndexSortKey, enumName: 'ViewInvoiceIndexSortKey' })
  @IsEnum(ViewInvoiceIndexSortKey)
  key: ViewInvoiceIndexSortKey

  @SortDirectionApiProperty()
  @IsEnum(SortDirection)
  order: SortDirection
}
