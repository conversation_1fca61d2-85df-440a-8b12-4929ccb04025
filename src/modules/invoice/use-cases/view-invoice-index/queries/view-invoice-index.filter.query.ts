import { FilterQuery } from '@wisemen/pagination'
import { ArrayMinSize, ArrayUnique, IsArray, IsEnum, IsNotEmpty, IsObject, IsString, ValidateNested } from 'class-validator'
import { ApiProperty } from '@nestjs/swagger'
import { IsUndefinable } from '@wisemen/validators'
import { Type } from 'class-transformer'
import { InvoiceStatus, InvoiceStatusApiProperty } from '../../../enums/invoice-status.enum.js'
import { DateRange } from '../../../../../utils/types/date-range.js'

export class ViewInvoiceIndexFilterQuery extends FilterQuery {
  @InvoiceStatusApiProperty({ isArray: true })
  @IsArray()
  @ArrayUnique()
  @ArrayMinSize(1)
  @IsEnum(InvoiceStatus, { each: true })
  statuses: InvoiceStatus[]

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsString()
  @IsNotEmpty()
  invoiceNumber?: string

  @ApiProperty({ type: DateRange, required: false })
  @IsUndefinable()
  @Type(() => DateRange)
  @ValidateNested()
  @IsObject()
  issueDate?: DateRange

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsString()
  @IsNotEmpty()
  customerReference?: string

  @ApiProperty({ type: String, required: false, isArray: true })
  @IsUndefinable()
  @IsArray()
  @IsString({ each: true })
  @IsNotEmpty({ each: true })
  @ArrayUnique()
  @ArrayMinSize(1)
  payerIds?: string[]

  @ApiProperty({ type: String, required: false, isArray: true })
  @IsUndefinable()
  @IsArray()
  @IsString({ each: true })
  @IsNotEmpty({ each: true })
  @ArrayUnique()
  @ArrayMinSize(1)
  customerIds?: string[]

  @ApiProperty({ type: DateRange, required: false })
  @IsUndefinable()
  @Type(() => DateRange)
  @ValidateNested()
  @IsObject()
  dueDate?: DateRange

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsString()
  @IsNotEmpty()
  accountDocumentNumber?: string

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsString()
  @IsNotEmpty()
  accountManagerName?: string

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsString()
  @IsNotEmpty()
  companyName?: string
}
