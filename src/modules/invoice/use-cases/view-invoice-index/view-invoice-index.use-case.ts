import { Injectable } from '@nestjs/common'
import { typeormPagination } from '@wisemen/pagination'
import dayjs from 'dayjs'
import { SapGetInvoiceIndexUseCase } from '../../../sap/use-cases/get-invoice-index/get-invoice-index.use-case.js'
import { SapQuery } from '../../../sap/query/sap-query.js'
import { SapGetInvoiceIndexResponse } from '../../../sap/use-cases/get-invoice-index/get-invoice-index.response.js'
import { InvoiceStatus } from '../../enums/invoice-status.enum.js'
import { InvoiceMapper } from '../../mappers/invoice.mapper.js'
import { exhaustiveCheck } from '../../../../utils/helpers/exhaustive-check.helper.js'
import { SapFilterGroup } from '../../../sap/query/types/sap-filter-group.js'
import { FilterOperator } from '../../../sap/enums/odata-filter-operator.enum.js'
import { SapDateFormatterService } from '../../../sap/services/sap-date-formatter.service.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { SAP_INVOICE_TYPE_TO_FILTER } from '../../enums/invoice-type.enum.js'
import { ViewInvoiceIndexQuery } from './queries/view-invoice-index.query.js'
import { ViewInvoiceIndexResponse } from './view-invoice-index.response.js'
import { ViewInvoiceIndexSortKey } from './queries/view-invoice-index-sort.query.js'
import { ViewInvoiceIndexFilterQuery } from './queries/view-invoice-index.filter.query.js'

@Injectable()
export class ViewInvoiceIndexUseCase {
  constructor (
    private readonly authContext: AuthContext,
    private readonly sapGetInvoiceIndexUseCase: SapGetInvoiceIndexUseCase
  ) { }

  async execute (
    query: ViewInvoiceIndexQuery
  ): Promise<ViewInvoiceIndexResponse> {
    const sapQuery = this.getSapQuery(query)
    const results = await this.sapGetInvoiceIndexUseCase.execute(sapQuery)
    const invoices = InvoiceMapper.fromSapResponses(results)
    const pagination = typeormPagination(query.pagination)

    return new ViewInvoiceIndexResponse(invoices, null, pagination.take, pagination.skip)
  }

  private getSapQuery (query: ViewInvoiceIndexQuery): SapQuery<SapGetInvoiceIndexResponse> {
    const sapQuery = new SapQuery<SapGetInvoiceIndexResponse>(query, {
      defaultOrderBy: {
        column: 'Vbeln',
        direction: 'asc'
      },
      keyMapper: (key: string) => this.mapSortKeyToSapField(key as ViewInvoiceIndexSortKey)
    })

    for (const [index, type] of SAP_INVOICE_TYPE_TO_FILTER.entries()) {
      if (index === 0) {
        sapQuery.where('Vbtyp', type)
      } else {
        sapQuery.orWhere('Vbtyp', type)
      }
    }

    this.applyCustomerFilter(sapQuery)
    this.applyStatusFilter(query, sapQuery)
    this.applySapFilters(query.filter, sapQuery)

    return sapQuery
  }

  private applySapFilters (
    queryFilters: ViewInvoiceIndexFilterQuery,
    sapQuery: SapQuery<SapGetInvoiceIndexResponse>
  ): void {
    if (queryFilters.invoiceNumber !== undefined) {
      sapQuery.andWhere('Vbeln', queryFilters.invoiceNumber)
    }

    if (queryFilters.issueDate !== undefined) {
      const from = dayjs(queryFilters.issueDate.from).startOf('day').toDate()
      const to = dayjs(queryFilters.issueDate.to).endOf('day').toDate()
      sapQuery.andWhere((qb) => {
        return qb.andWhere('Fkdat', SapDateFormatterService.toSapFilterDate(from), FilterOperator.GREATER_THAN_OR_EQUAL)
          .andWhere('Fkdat', SapDateFormatterService.toSapFilterDate(to), FilterOperator.LESS_THAN_OR_EQUAL)
      })
    }

    if (queryFilters.customerReference !== undefined) {
      sapQuery.andWhere('Bstkd', queryFilters.customerReference, FilterOperator.SUBSTRING_OF)
    }

    if (queryFilters.payerIds !== undefined && queryFilters.payerIds.length > 0) {
      sapQuery.andWhere('Kunrg', queryFilters.payerIds[0])

      for (let i = 1; i < queryFilters.payerIds.length; i++) {
        sapQuery.orWhere('Kunrg', queryFilters.payerIds[i])
      }
    }

    if (queryFilters.customerIds !== undefined && queryFilters.customerIds.length > 0) {
      sapQuery.andWhere('Kunag', queryFilters.customerIds[0])

      for (let i = 1; i < queryFilters.customerIds.length; i++) {
        sapQuery.orWhere('Kunag', queryFilters.customerIds[i])
      }
    }

    if (queryFilters.dueDate !== undefined) {
      const from = dayjs(queryFilters.dueDate.from).startOf('day').toDate()
      const to = dayjs(queryFilters.dueDate.to).endOf('day').toDate()
      sapQuery.andWhere((qb) => {
        return qb.andWhere('Duedate', SapDateFormatterService.toSapFilterDate(from), FilterOperator.GREATER_THAN_OR_EQUAL)
          .andWhere('Duedate', SapDateFormatterService.toSapFilterDate(to), FilterOperator.LESS_THAN_OR_EQUAL)
      })
    }

    if (queryFilters.accountDocumentNumber !== undefined) {
      sapQuery.andWhere('Belnr', queryFilters.accountDocumentNumber)
    }

    if (queryFilters.accountManagerName !== undefined) {
      sapQuery.andWhere('SalesRep', queryFilters.accountManagerName, FilterOperator.SUBSTRING_OF)
    }

    if (queryFilters.companyName !== undefined) {
      sapQuery.andWhere('Butxt', queryFilters.companyName, FilterOperator.SUBSTRING_OF)
    }
  }

  private applyStatusFilter (
    query: ViewInvoiceIndexQuery,
    sapQuery: SapQuery<SapGetInvoiceIndexResponse>
  ): void {
    if (
      query.filter.statuses !== undefined
      && query.filter.statuses.length > 0
      && query.filter.statuses.length !== Object.values(InvoiceStatus).length
    ) {
      sapQuery.where((qb) => {
        for (const [index, status] of query.filter.statuses.entries()) {
          if (index === 0) {
            qb.where(qb1 => this.addStatusQuery(status, qb1))
          } else {
            qb.orWhere(qb1 => this.addStatusQuery(status, qb1))
          }
        }

        return qb
      })
    }
  }

  private applyCustomerFilter (
    sapQuery: SapQuery<SapGetInvoiceIndexResponse>
  ): void {
    const selectedCustomerId = this.authContext.getSelectedCustomerId()

    if (selectedCustomerId != null) {
      sapQuery.andWhere('Kunrg', selectedCustomerId)
    }
  }

  private addStatusQuery (
    status: InvoiceStatus,
    filterGroup: SapFilterGroup<SapGetInvoiceIndexResponse>
  ): SapFilterGroup<SapGetInvoiceIndexResponse> {
    switch (status) {
      case InvoiceStatus.CLEARED:
        return filterGroup.where('Augbl', '', FilterOperator.NOT_EQUAL)

      case InvoiceStatus.OUTSTANDING:
        return filterGroup
          .where('Augbl', '')
          .andWhere('Duedate', SapDateFormatterService.toSapFilterDate(), FilterOperator.GREATER_THAN)

      case InvoiceStatus.OVERDUE:

        return filterGroup
          .where('Augbl', '')
          .andWhere('Duedate', SapDateFormatterService.toSapFilterDate(), FilterOperator.LESS_THAN_OR_EQUAL)
          .andWhere('Duedate', SapDateFormatterService.toSapFilterDate(new Date('1970-01-01T00:00:00Z')), FilterOperator.GREATER_THAN)
      default:
        exhaustiveCheck(status)
    }
  }

  private mapSortKeyToSapField (
    sortKey: ViewInvoiceIndexSortKey
  ): keyof SapGetInvoiceIndexResponse {
    switch (sortKey) {
      case ViewInvoiceIndexSortKey.INVOICE_NUMBER:
        return 'Vbeln'
      case ViewInvoiceIndexSortKey.CUSTOMER_REFERENCE:
        return 'Bstkd'
      case ViewInvoiceIndexSortKey.ISSUE_DATE:
        return 'Fkdat'
      case ViewInvoiceIndexSortKey.PAYER_NAME:
        return 'KunrgName1'
      case ViewInvoiceIndexSortKey.CUSTOMER_NAME:
        return 'KunagName1'
      case ViewInvoiceIndexSortKey.ACCOUNT_DOCUMENT_NUMBER:
        return 'Belnr'
      case ViewInvoiceIndexSortKey.DUE_DATE:
        return 'Duedate'
      case ViewInvoiceIndexSortKey.NET_AMOUNT:
        return 'Netwr'
      case ViewInvoiceIndexSortKey.COMPANY_NAME:
        return 'Butxt'
      default:
        exhaustiveCheck(sortKey)
    }
  }
}
