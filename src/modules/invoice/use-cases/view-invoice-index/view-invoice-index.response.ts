import { ApiProperty } from '@nestjs/swagger'
import { PaginatedOffsetResponse } from '@wisemen/pagination'
import { Invoice } from '../../types/invoice.type.js'
import { InvoiceStatus, InvoiceStatusApiProperty } from '../../enums/invoice-status.enum.js'
import { InvoiceDynamicTableFields } from '../../types/invoice.dynamic-table-fields.type.js'
import { InvoiceType, InvoiceTypeApiProperty } from '../../enums/invoice-type.enum.js'
import { calculatePageSize } from '../../../../utils/helpers/unknown-page-size.helper.js'

export class InvoiceResponse implements InvoiceDynamicTableFields {
  @ApiProperty({ type: String })
  invoiceNumber: string

  @InvoiceStatusApiProperty()
  status: InvoiceStatus

  @ApiProperty({ type: String, format: 'date' })
  issuedOn: string

  @ApiProperty({ type: String, format: 'date', nullable: true })
  dueOn: string | null

  @ApiProperty({ type: String })
  customerName: string

  @ApiProperty({ type: String, nullable: true })
  customerReference: string | null

  @InvoiceTypeApiProperty()
  type: InvoiceType

  @ApiProperty({ type: String })
  payerId: string

  @ApiProperty({ type: String })
  payerName: string

  @ApiProperty({ type: String })
  netAmount: string

  @ApiProperty({ type: String })
  vatAmount: string

  @ApiProperty({ type: String })
  currency: string

  @ApiProperty({ type: String, nullable: true })
  accountDocumentNumber: string | null

  @ApiProperty({ type: String, nullable: true })
  accountManagerName: string | null

  @ApiProperty({ type: String })
  companyName: string

  constructor (invoice: Invoice) {
    this.invoiceNumber = invoice.invoiceNumber
    this.status = invoice.status
    this.issuedOn = invoice.issueDate
    this.dueOn = invoice.dueDate
    this.customerName = invoice.customerName
    this.customerReference = invoice.customerReference ?? null
    this.type = invoice.type
    this.payerId = invoice.payerId
    this.payerName = invoice.payerName
    this.netAmount = invoice.netAmount
    this.vatAmount = invoice.vatAmount
    this.currency = invoice.currency
    this.accountDocumentNumber = invoice.accountDocumentNumber ?? null
    this.accountManagerName = invoice.accountManagerName ?? null
    this.companyName = invoice.companyName
  }
}

export class ViewInvoiceIndexResponse extends PaginatedOffsetResponse<InvoiceResponse> {
  @ApiProperty({ type: InvoiceResponse, isArray: true })
  declare items: InvoiceResponse[]

  constructor (
    items: Invoice[],
    total: number | null,
    limit: number,
    offset: number
  ) {
    const result = items.map(invoice => new InvoiceResponse(invoice))

    if (total === null) {
      total = calculatePageSize(items, offset, limit)
    }

    super(result, total, limit, offset)
  }
}
