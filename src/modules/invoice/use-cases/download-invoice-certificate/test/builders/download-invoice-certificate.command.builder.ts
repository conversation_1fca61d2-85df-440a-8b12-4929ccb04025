import { DownloadInvoiceCertificateCommand } from '../../download-invoice-certificate.command.js'

export class DownloadInvoiceCertificateCommandBuilder {
  private command: DownloadInvoiceCertificateCommand

  constructor () {
    this.command = new DownloadInvoiceCertificateCommand()
    this.command.fileName = 'example.pdf'
  }

  withFileName (fileName: string): this {
    this.command.fileName = fileName
    return this
  }

  build (): DownloadInvoiceCertificateCommand {
    return this.command
  }
}
