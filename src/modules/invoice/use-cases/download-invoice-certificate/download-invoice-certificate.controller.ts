import { Body, Controller, HttpCode, HttpStatus, Post, Res } from '@nestjs/common'
import { ApiTags, ApiOkResponse, ApiOAuth2 } from '@nestjs/swagger'
import { Response } from 'express'
import { Permissions } from '../../../../modules/permission/permission.decorator.js'
import { Permission } from '../../../../modules/permission/permission.enum.js'
import { GlobalCustomerRequired } from '../../../auth/decorators/no-customer-required.decorator.js'
import { DownloadInvoiceCertificateUseCase } from './download-invoice-certificate.use-case.js'
import { DownloadInvoiceCertificateCommand } from './download-invoice-certificate.command.js'

@ApiTags('Invoice')
@ApiOAuth2([])
@Controller('invoices/certificates/download')
export class DownloadInvoiceCertificateController {
  constructor (
    private readonly useCase: DownloadInvoiceCertificateUseCase
  ) { }

  @Post()
  @GlobalCustomerRequired()
  @HttpCode(HttpStatus.OK)
  @Permissions(Permission.INVOICE_READ, Permission.INVOICE_MANAGE)
  @ApiOkResponse()
  public async downloadInvoiceCertificate (
    @Res() response: Response,
    @Body() command: DownloadInvoiceCertificateCommand
  ): Promise<void> {
    await this.useCase.execute(response, command)
  }
}
