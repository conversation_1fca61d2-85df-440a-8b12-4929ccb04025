import { HttpStatus, Injectable } from '@nestjs/common'
import { AxiosError } from 'axios'
import { Response } from 'express'
import { SharepointDownloadDocumentUseCase } from '../../../sharepoint/use-cases/download-document/download-document.use-case.js'
import { SharepointCommandBuilder } from '../../../sharepoint/builders/sharepoint-command.builder.js'
import { SHAREPOINT_LIBRARY } from '../../../sharepoint/constants/sharepoint-library.constant.js'
import { SharepointLibraryNameEnvMapper } from '../../../sharepoint/mappers/sharepoint-library.mapper.js'
import { DEFAULT_SHAREPOINT_SITE_TITLE } from '../../../sharepoint/constants/sharepoint-site-title.constant.js'
import { streamFileResponse } from '../../../../utils/streams/stream-file-response.js'
import { CertificateFileNotFoundError } from '../../../certificate/errors/certificate-file-not-found.error.js'
import { DownloadInvoiceCertificateCommand } from './download-invoice-certificate.command.js'

@Injectable()
export class DownloadInvoiceCertificateUseCase {
  constructor (
    private sharepointDownloadUseCase: SharepointDownloadDocumentUseCase
  ) {}

  async execute (
    response: Response,
    command: DownloadInvoiceCertificateCommand
  ): Promise<void> {
    const libraryName = SharepointLibraryNameEnvMapper
      .toSharepointLibraryName(SHAREPOINT_LIBRARY.INVOICE)

    const sharepointCommand = new SharepointCommandBuilder()
      .addFileName(command.fileName)
      .addLibraryName(libraryName)
      .addSiteTitle(DEFAULT_SHAREPOINT_SITE_TITLE)
      .build()

    try {
      const result = await this.sharepointDownloadUseCase.execute(sharepointCommand)
      return streamFileResponse(
        result.buffer,
        result.mimeType,
        result.fileName,
        response
      )
    } catch (error) {
      const axiosError = error as AxiosError

      if (axiosError.status === HttpStatus.NOT_FOUND) {
        throw new CertificateFileNotFoundError()
      }

      throw error
    }
  }
}
