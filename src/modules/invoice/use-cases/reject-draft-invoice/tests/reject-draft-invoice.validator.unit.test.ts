import { before, afterEach, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import { createStubInstance, SinonStubbedInstance } from 'sinon'
import { expect } from 'expect'
import { ForbiddenException } from '@nestjs/common'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { AuthContext } from '../../../../auth/auth.context.js'
import { UserCustomerAuthService } from '../../../../auth/services/user-customer-auth.service.js'
import { RejectDraftInvoiceValidator } from '../reject-draft-invoice.validator.js'
import { SapGetDraftInvoiceIndexUseCase } from '../../../../sap/use-cases/get-draft-invoice-index/get-draft-invoice-index.use-case.js'
import { InvoiceNotFoundError } from '../../../errors/invoice-not-found.error.js'
import { SapGetDraftInvoiceIndexResponseBuilder } from '../../../../sap/use-cases/get-draft-invoice-index/tests/builders/get-draft-invoice-index.response.builder.js'
import { NonApproveOrRejectableDraftInvoiceError } from '../../../errors/non-approve-or-rejectable-draft-invoice.error.js'

describe('RejectDraftInvoiceValidator - Unit Tests', () => {
  let validator: RejectDraftInvoiceValidator

  let sapGetDraftInvoiceIndexUseCase: SinonStubbedInstance<SapGetDraftInvoiceIndexUseCase>
  let authContext: SinonStubbedInstance<AuthContext>
  let userCustomerAuthService: SinonStubbedInstance<UserCustomerAuthService>

  before(() => {
    TestBench.setupUnitTest()

    sapGetDraftInvoiceIndexUseCase = createStubInstance(SapGetDraftInvoiceIndexUseCase)
    authContext = createStubInstance(AuthContext)
    userCustomerAuthService = createStubInstance(UserCustomerAuthService)

    validator = new RejectDraftInvoiceValidator(
      sapGetDraftInvoiceIndexUseCase,
      authContext,
      userCustomerAuthService
    )

    mockMethods()
  })

  afterEach(() => {
    mockMethods()
  })

  function mockMethods () {
    sapGetDraftInvoiceIndexUseCase.execute.resolves([
      new SapGetDraftInvoiceIndexResponseBuilder()
        .withStatus2('2')
        .withStatus3('')
        .build()
    ])
    authContext.getAzureEntraUpn.returns(randomUUID())
    authContext.isInternalUser.returns(false)
    userCustomerAuthService.canUserAccessCustomer.resolves(true)
  }

  it('validates whether draft invoice can be rejected by customer', () => {
    expect(validator.validate(randomUUID())).resolves.not.toThrow(ForbiddenException)
  })

  it('validates whether draft invoice can be rejected by internal user', () => {
    sapGetDraftInvoiceIndexUseCase.execute.resolves([
      new SapGetDraftInvoiceIndexResponseBuilder()
        .withStatus2('')
        .withKatr6('A1')
        .build()
    ])
    authContext.isInternalUser.returns(true)

    expect(validator.validate(randomUUID())).resolves.not.toThrow(ForbiddenException)
  })

  it('throws InvoiceNotFoundError when invoice not found', () => {
    sapGetDraftInvoiceIndexUseCase.execute.resolves([])

    expect(validator.validate(randomUUID())).rejects.toThrow(InvoiceNotFoundError)
  })

  it('throws ForbiddenException when user has no access to the invoice', () => {
    userCustomerAuthService.canUserAccessCustomer.resolves(false)

    expect(validator.validate(randomUUID())).rejects.toThrow(ForbiddenException)
  })

  it('throws NonApprovableDraftInvoiceError when invoice is not approvable', () => {
    sapGetDraftInvoiceIndexUseCase.execute.resolves([
      new SapGetDraftInvoiceIndexResponseBuilder()
        .withStatus2('2')
        .withStatus3('2')
        .withKatr6('A1')
        .build()
    ])

    expect(validator.validate(randomUUID()))
      .rejects
      .toThrow(NonApproveOrRejectableDraftInvoiceError)
  })

  it('throws ForbiddenException when invoice needs internal approval and user is not internal', () => {
    sapGetDraftInvoiceIndexUseCase.execute.resolves([
      new SapGetDraftInvoiceIndexResponseBuilder()
        .withStatus2('')
        .withKatr6('A3')
        .build()
    ])

    userCustomerAuthService.canUserAccessCustomer.resolves(true)
    authContext.isInternalUser.returns(false)

    expect(validator.validate(randomUUID())).rejects.toThrow(ForbiddenException)
  })
})
