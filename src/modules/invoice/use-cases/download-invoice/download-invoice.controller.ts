import { <PERSON>, <PERSON>, Param, <PERSON><PERSON> } from '@nestjs/common'
import { ApiTags, ApiOAuth2, ApiOkResponse } from '@nestjs/swagger'
import { Response } from 'express'
import { Permissions } from '../../../../modules/permission/permission.decorator.js'
import { Permission } from '../../../../modules/permission/permission.enum.js'
import { ApiNotFoundErrorResponse } from '../../../exceptions/api-errors/api-error-response.decorator.js'
import { InvoiceNotFoundError } from '../../errors/invoice-not-found.error.js'
import { InvoiceDocumentNotFoundError } from '../../errors/invoice-document-not-found.error.js'
import { FileStreamUtil } from '../../../../utils/streams/file-stream.util.js'
import { DownloadInvoiceUseCase } from './download-invoice.use-case.js'

@ApiTags('Invoice')
@ApiOAuth2([])
@Controller('invoices/:invoiceNumber/download')
export class DownloadInvoiceController {
  constructor (
    private readonly useCase: DownloadInvoiceUseCase
  ) { }

  @Get()
  @Permissions(Permission.INVOICE_READ, Permission.INVOICE_MANAGE)
  @ApiOkResponse()
  @ApiNotFoundErrorResponse(
    InvoiceDocumentNotFoundError,
    InvoiceNotFoundError
  )
  public async downloadInvoiceSap (
    @Param('invoiceNumber') invoiceNumber: string,
    @Res() res: Response
  ): Promise<void> {
    const url = await this.useCase.execute(invoiceNumber)
    await FileStreamUtil.pipeFileResponse(res, url)
  }
}
