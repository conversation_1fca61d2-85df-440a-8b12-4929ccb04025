import { before, describe, it, beforeEach } from 'node:test'
import { randomUUID } from 'crypto'
import { createStubInstance, SinonStubbedInstance } from 'sinon'
import { expect } from 'expect'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { DownloadInvoiceUseCase } from '../download-invoice.use-case.js'
import { SapGetInvoiceIndexUseCase } from '../../../../sap/use-cases/get-invoice-index/get-invoice-index.use-case.js'
import { SapGetDocumentsIndexUseCase } from '../../../../sap/use-cases/get-documents-index/get-documents-index.use-case.js'
import { AuthContext } from '../../../../auth/auth.context.js'
import { InvoiceNotFoundError } from '../../../errors/invoice-not-found.error.js'
import { InvoiceDocumentNotFoundError } from '../../../errors/invoice-document-not-found.error.js'
import { SapGetInvoiceIndexResponseBuilder } from '../../../../sap/use-cases/get-invoice-index/tests/builders/get-invoice-index.response.builder.js'
import { SapGetDocumentIndexResponseBuilder } from '../../../../sap/use-cases/get-documents-index/tests/get-documents-index.response.builder.js'

describe('Download invoice unit test', () => {
  let useCase: DownloadInvoiceUseCase
  let invoiceNumber: string
  let sapGetInvoiceUseCase: SinonStubbedInstance<SapGetInvoiceIndexUseCase>
  let sapDocumentIndexUseCase: SinonStubbedInstance<SapGetDocumentsIndexUseCase>

  before(() => {
    TestBench.setupUnitTest()
    invoiceNumber = randomUUID()

    sapGetInvoiceUseCase = createStubInstance(SapGetInvoiceIndexUseCase)
    sapDocumentIndexUseCase = createStubInstance(SapGetDocumentsIndexUseCase)

    const authContext = createStubInstance(AuthContext, {
      getSelectedCustomerId: randomUUID()
    })

    useCase = new DownloadInvoiceUseCase(
      authContext,
      sapGetInvoiceUseCase,
      sapDocumentIndexUseCase
    )
  })

  beforeEach(() => {
    sapGetInvoiceUseCase.execute.resolves([])
    sapDocumentIndexUseCase.execute.resolves([])
  })

  it('throws InvoiceNotFoundError when the invoice does not exist', async () => {
    await expect(useCase.execute(invoiceNumber)).rejects.toThrow(InvoiceNotFoundError)
  })

  it('throws InvoiceDocumentNotFoundError when SAP returns no document results', async () => {
    sapGetInvoiceUseCase.execute.resolves([
      new SapGetInvoiceIndexResponseBuilder()
        .build()
    ])
    sapDocumentIndexUseCase.execute.resolves([])
    await expect(useCase.execute(invoiceNumber)).rejects.toThrow(InvoiceDocumentNotFoundError)
  })

  it('throws InvoiceDocumentNotFoundError when SAP returns no matching documents because of ArObject mismatch', async () => {
    sapGetInvoiceUseCase.execute.resolves([
      new SapGetInvoiceIndexResponseBuilder()
        .build()
    ])
    sapDocumentIndexUseCase.execute.resolves([
      new SapGetDocumentIndexResponseBuilder()
        .withArObject('ZCI_SD_ALL')
        .build()
    ])
    await expect(useCase.execute(invoiceNumber)).rejects.toThrow(InvoiceDocumentNotFoundError)
  })

  it('returns the url of the matching invoice document', async () => {
    sapGetInvoiceUseCase.execute.resolves([
      new SapGetInvoiceIndexResponseBuilder()
        .build()
    ])

    const sapDocumentResponse = new SapGetDocumentIndexResponseBuilder()
      .withArObject('ZSI_O')
      .build()

    sapDocumentIndexUseCase.execute.resolves([
      sapDocumentResponse
    ])

    const url = await useCase.execute(invoiceNumber)
    expect(url).toBe(sapDocumentResponse.ArUrl)
  })
})
