import { Module } from '@nestjs/common'
import { SapModule } from '../../../sap/sap.module.js'
import { ViewDraftInvoiceIndexModule } from '../view-draft-invoice-index/view-draft-invoice-index.module.js'
import { ViewDraftInvoiceIndexUseCase } from '../view-draft-invoice-index/view-draft-invoice-index.use-case.js'
import { ExportDraftInvoicesExcelUseCase } from './export-draft-invoices-excel.use-case.js'
import { ExportDraftInvoicesExcelController } from './export-draft-invoices-excel.controller.js'

@Module({
  imports: [
    SapModule,
    ViewDraftInvoiceIndexModule
  ],
  controllers: [ExportDraftInvoicesExcelController],
  providers: [
    ExportDraftInvoicesExcelUseCase,
    ViewDraftInvoiceIndexUseCase
  ]
})
export class ExportDraftInvoicesExcelModule {}
