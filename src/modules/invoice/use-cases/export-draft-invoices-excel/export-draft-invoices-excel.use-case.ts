import { Injectable } from '@nestjs/common'
import { Response } from 'express'
import { plainToInstance } from 'class-transformer'
import dayjs from 'dayjs'
import ExcelJS from 'exceljs'
import { streamFileResponse } from '../../../../utils/streams/stream-file-response.js'
import { MimeType } from '../../../files/enums/mime-type.enum.js'
import { ViewDraftInvoiceIndexUseCase } from '../view-draft-invoice-index/view-draft-invoice-index.use-case.js'
import { DraftInvoiceResponse } from '../view-draft-invoice-index/view-draft-invoice-index.response.js'
import { DraftInvoiceColumnName } from '../../../dynamic-tables/enums/draft-invoice-column-name.enum.js'
import { ExportDraftInvoicesExcelQuery } from './queries/export-draft-invoices-excel.query.js'

@Injectable()
export class ExportDraftInvoicesExcelUseCase {
  constructor (
    private readonly viewDraftInvoiceIndexUseCase: ViewDraftInvoiceIndexUseCase
  ) { }

  async execute (
    query: ExportDraftInvoicesExcelQuery,
    res: Response
  ): Promise<void> {
    const { columns, translatedColumns } = query.filter

    const sapInvoices = await this.getDraftInvoices(query)
    const buffer = await this.mapInvoicesToExcelBuffer(
      sapInvoices,
      columns,
      translatedColumns
    )

    const fileName = `export${dayjs().format('YYYYMMDDHHmmss')}.xlsx`

    return streamFileResponse(
      buffer,
      MimeType.XLSX,
      fileName,
      res
    )
  }

  private async mapInvoicesToExcelBuffer (
    invoices: DraftInvoiceResponse[],
    columns: DraftInvoiceColumnName[],
    translatedColumns: string[]
  ): Promise<Buffer> {
    const workbook = new ExcelJS.Workbook()
    const worksheet = workbook.addWorksheet('Draft Invoices')

    worksheet.addRow(translatedColumns)

    for (const invoice of invoices) {
      const values = columns.map((columnName) => {
        const value = invoice[columnName]
        return value != null ? value : '-'
      })
      worksheet.addRow(values)
    }

    return Buffer.from(await workbook.xlsx.writeBuffer())
  }

  private async getDraftInvoices (
    query: ExportDraftInvoicesExcelQuery
  ): Promise<DraftInvoiceResponse[]> {
    const draftInvoices: DraftInvoiceResponse[] = []

    let offset: undefined | number = 0
    do {
      const response = await this.viewDraftInvoiceIndexUseCase.execute(
        plainToInstance(ExportDraftInvoicesExcelQuery, {
          filter: query.filter,
          pagination: {
            limit: 100,
            offset: offset
          }
        }))

      draftInvoices.push(...response.items)

      const total = response.meta.offset + response.meta.limit
      offset = total >= response.meta.total
        ? undefined
        : (offset + response.meta.limit)
    } while (offset !== undefined)

    return draftInvoices
  }
}
