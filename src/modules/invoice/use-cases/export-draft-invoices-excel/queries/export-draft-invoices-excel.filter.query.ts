import { FilterQuery } from '@wisemen/pagination'
import { ArrayMinSize, ArrayUnique, IsArray, IsEnum, IsNotEmpty, IsString } from 'class-validator'
import { ApiProperty } from '@nestjs/swagger'
import { InvoiceColumnNameApiProperty } from '../../../../dynamic-tables/enums/invoice-column-name.enum.js'
import { DraftInvoiceFilterStatus, DraftInvoiceFilterStatusApiProperty } from '../../../enums/draft-invoice-status.enum.js'
import { DraftInvoiceColumnName } from '../../../../dynamic-tables/enums/draft-invoice-column-name.enum.js'

export class ExportDraftInvoicesExcelFilterQuery extends FilterQuery {
  @DraftInvoiceFilterStatusApiProperty({ isArray: true })
  @IsArray()
  @ArrayUnique()
  @ArrayMinSize(1)
  @IsEnum(DraftInvoiceFilterStatus, { each: true })
  statuses: DraftInvoiceFilterStatus[]

  @InvoiceColumnNameApiProperty({ isArray: true })
  @IsArray()
  @ArrayUnique()
  @ArrayMinSize(1)
  @IsEnum(DraftInvoiceColumnName, { each: true })
  columns: DraftInvoiceColumnName[]

  @ApiProperty({ type: String, description: 'Translated column names for CSV header', example: ['Invoice No.'], isArray: true })
  @IsArray()
  @ArrayUnique()
  @ArrayMinSize(1)
  @IsNotEmpty({ each: true })
  @IsString({ each: true })
  translatedColumns: string[]
}
