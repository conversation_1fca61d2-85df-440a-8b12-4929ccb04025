import { DraftInvoiceColumnName } from '../../../../dynamic-tables/enums/draft-invoice-column-name.enum.js'
import { DraftInvoiceFilterStatus } from '../../../enums/draft-invoice-status.enum.js'
import { ExportDraftInvoicesExcelFilterQuery } from './export-draft-invoices-excel.filter.query.js'

export class ExportDraftInvoicesExcelFilterQueryBuilder {
  private filterQuery: ExportDraftInvoicesExcelFilterQuery

  constructor () {
    this.reset()
  }

  private reset (): this {
    this.filterQuery = new ExportDraftInvoicesExcelFilterQuery()
    this.filterQuery.statuses = [DraftInvoiceFilterStatus.APPROVED]
    this.filterQuery.columns = [DraftInvoiceColumnName.INVOICE_NUMBER]
    this.filterQuery.translatedColumns = ['Invoice No.']

    return this
  }

  withStatuses (statuses: DraftInvoiceFilterStatus[]): this {
    this.filterQuery.statuses = statuses

    return this
  }

  withColumns (columns: DraftInvoiceColumnName[]): this {
    this.filterQuery.columns = columns

    return this
  }

  withTranslatedColumns (translatedColumns: string[]): this {
    this.filterQuery.translatedColumns = translatedColumns

    return this
  }

  public build (): ExportDraftInvoicesExcelFilterQuery {
    const result = this.filterQuery

    this.reset()

    return result
  }
}
