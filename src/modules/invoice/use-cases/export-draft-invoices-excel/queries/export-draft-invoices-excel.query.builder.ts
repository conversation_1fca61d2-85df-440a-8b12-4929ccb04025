import { ExportDraftInvoicesExcelFilterQueryBuilder } from './export-draft-invoices-excel-filter.query.builder.js'
import { ExportDraftInvoicesExcelFilterQuery } from './export-draft-invoices-excel.filter.query.js'
import { ExportDraftInvoicesExcelQuery } from './export-draft-invoices-excel.query.js'

export class ExportDraftInvoicesExcelQueryBuilder {
  private query: ExportDraftInvoicesExcelQuery

  constructor () {
    this.reset()

    this.query.filter = new ExportDraftInvoicesExcelFilterQueryBuilder().build()
  }

  private reset (): this {
    this.query = new ExportDraftInvoicesExcelQuery()

    return this
  }

  withFilter (filter: ExportDraftInvoicesExcelFilterQuery): this {
    this.query.filter = filter

    return this
  }

  public build (): ExportDraftInvoicesExcelQuery {
    const result = this.query

    this.reset()

    return result
  }
}
