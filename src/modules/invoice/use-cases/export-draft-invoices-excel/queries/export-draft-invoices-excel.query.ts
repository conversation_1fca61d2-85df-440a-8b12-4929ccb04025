import { ApiProperty } from '@nestjs/swagger'
import { IsObject, ValidateNested } from 'class-validator'
import { Type } from 'class-transformer'
import { ViewDraftInvoiceIndexQuery } from '../../view-draft-invoice-index/queries/view-draft-invoice-index.query.js'
import { ExportDraftInvoicesExcelFilterQuery } from './export-draft-invoices-excel.filter.query.js'

export class ExportDraftInvoicesExcelQuery extends ViewDraftInvoiceIndexQuery {
  @ApiProperty({ type: ExportDraftInvoicesExcelFilterQuery })
  @Type(() => ExportDraftInvoicesExcelFilterQuery)
  @ValidateNested()
  @IsObject()
  declare filter: ExportDraftInvoicesExcelFilterQuery
}
