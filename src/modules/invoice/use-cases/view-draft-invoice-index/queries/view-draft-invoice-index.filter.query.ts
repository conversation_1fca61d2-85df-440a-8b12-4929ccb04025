import { FilterQuery } from '@wisemen/pagination'
import { ArrayMinSize, ArrayUnique, IsArray, IsEnum } from 'class-validator'
import { DraftInvoiceFilterStatus, DraftInvoiceFilterStatusApiProperty } from '../../../enums/draft-invoice-status.enum.js'

export class ViewDraftInvoiceIndexFilterQuery extends FilterQuery {
  @DraftInvoiceFilterStatusApiProperty({ isArray: true, minItems: 1 })
  @IsArray()
  @ArrayUnique()
  @ArrayMinSize(1)
  @IsEnum(DraftInvoiceFilterStatus, { each: true })
  statuses: DraftInvoiceFilterStatus[]
}
