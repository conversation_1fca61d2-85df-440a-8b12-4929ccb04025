import { ApiProperty } from '@nestjs/swagger'
import { PaginatedOffsetSearchQuery } from '@wisemen/pagination'
import { Equals, IsObject, ValidateNested } from 'class-validator'
import { Type } from 'class-transformer'
import { ViewDraftInvoiceIndexFilterQuery } from './view-draft-invoice-index.filter.query.js'

export class ViewDraftInvoiceIndexQuery extends PaginatedOffsetSearchQuery {
  @Equals(undefined)
  sort?: never

  @ApiProperty({ type: ViewDraftInvoiceIndexFilterQuery })
  @Type(() => ViewDraftInvoiceIndexFilterQuery)
  @ValidateNested()
  @IsObject()
  filter: ViewDraftInvoiceIndexFilterQuery

  @Equals(undefined)
  search?: never
}
