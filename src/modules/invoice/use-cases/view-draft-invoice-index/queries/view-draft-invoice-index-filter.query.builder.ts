import { DraftInvoiceFilterStatus } from '../../../enums/draft-invoice-status.enum.js'
import { ViewDraftInvoiceIndexFilterQuery } from './view-draft-invoice-index.filter.query.js'

export class ViewDraftInvoiceIndexFilterQueryBuilder {
  private filterQuery: ViewDraftInvoiceIndexFilterQuery

  constructor () {
    this.reset()
  }

  private reset (): this {
    this.filterQuery = new ViewDraftInvoiceIndexFilterQuery()

    this.filterQuery.statuses = [DraftInvoiceFilterStatus.TO_BE_APPROVED]

    return this
  }

  withStatuses (status: DraftInvoiceFilterStatus[]): this {
    this.filterQuery.statuses = status

    return this
  }

  public build (): ViewDraftInvoiceIndexFilterQuery {
    const result = this.filterQuery

    this.reset()

    return result
  }
}
