import { after, before, describe, it } from 'node:test'
import request from 'supertest'
import { expect } from 'expect'
import { HttpStatus } from '@nestjs/common'
import qs from 'qs'
import { EndToEndTestSetup } from '../../../../../../test/setup/end-to-end-test-setup.js'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { Permission } from '../../../../permission/permission.enum.js'
import { ViewDraftInvoiceIndexQueryBuilder } from '../queries/view-draft-invoice-index.query.builder.js'
import { ViewDraftInvoiceIndexFilterQueryBuilder } from '../queries/view-draft-invoice-index-filter.query.builder.js'
import { DraftInvoiceFilterStatus } from '../../../enums/draft-invoice-status.enum.js'

describe('View Draft Invoice index - E2E Tests', () => {
  let setup: EndToEndTestSetup

  before(async () => {
    setup = await TestBench.setupEndToEndTest()
  })

  after(async () => await setup.teardown())

  it('retrieves the draft invoices', async () => {
    const user = await setup.authContext.getUser([Permission.INVOICE_MANAGE])
    const query = new ViewDraftInvoiceIndexQueryBuilder()
      .withFilter(
        new ViewDraftInvoiceIndexFilterQueryBuilder()
          .withStatuses([DraftInvoiceFilterStatus.REJECTED])
          .build()
      )
      .build()

    const response = await request(setup.httpServer)
      .get(`/draft-invoices`)
      .set('Authorization', `Bearer ${user.token}`)
      .query(qs.stringify(query))

    expect(response).toHaveStatus(HttpStatus.OK)
  })
})
