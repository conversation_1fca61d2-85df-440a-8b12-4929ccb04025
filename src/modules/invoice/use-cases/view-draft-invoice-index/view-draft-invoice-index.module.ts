import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { SapModule } from '../../../sap/sap.module.js'
import { User } from '../../../../app/users/entities/user.entity.js'
import { ViewDraftInvoiceIndexController } from './view-draft-invoice-index.controller.js'
import { ViewDraftInvoiceIndexUseCase } from './view-draft-invoice-index.use-case.js'
import { ViewDraftInvoiceIndexValidator } from './view-draft-invoice-index.validator.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([User]),
    SapModule
  ],
  controllers: [
    ViewDraftInvoiceIndexController
  ],
  providers: [
    ViewDraftInvoiceIndexUseCase,
    ViewDraftInvoiceIndexValidator
  ],
  exports: [
    ViewDraftInvoiceIndexUseCase,
    ViewDraftInvoiceIndexValidator
  ]
})
export class ViewDraftInvoiceIndexModule {}
