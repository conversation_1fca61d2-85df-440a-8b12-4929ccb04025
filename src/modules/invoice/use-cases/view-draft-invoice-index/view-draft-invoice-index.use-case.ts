import { Injectable } from '@nestjs/common'
import { typeormPagination } from '@wisemen/pagination'
import { SapQuery } from '../../../sap/query/sap-query.js'
import { exhaustiveCheck } from '../../../../utils/helpers/exhaustive-check.helper.js'
import { SapFilterGroup } from '../../../sap/query/types/sap-filter-group.js'
import { SapGetDraftInvoiceIndexUseCase } from '../../../sap/use-cases/get-draft-invoice-index/get-draft-invoice-index.use-case.js'
import { DraftInvoiceMapper } from '../../mappers/draft-invoice.mapper.js'
import { DraftInvoice } from '../../types/draft-invoice-type.js'
import { SapGetDraftInvoiceIndexResponse } from '../../../sap/use-cases/get-draft-invoice-index/get-draft-invoice-index.response.js'
import { DraftInvoiceFilterStatus } from '../../enums/draft-invoice-status.enum.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { ViewDraftInvoiceIndexQuery } from './queries/view-draft-invoice-index.query.js'
import { ViewDraftInvoiceIndexResponse } from './view-draft-invoice-index.response.js'

@Injectable()
export class ViewDraftInvoiceIndexUseCase {
  constructor (
    private readonly authContext: AuthContext,
    private readonly sapGetDraftInvoiceIndexUseCase: SapGetDraftInvoiceIndexUseCase
  ) { }

  async execute (
    query: ViewDraftInvoiceIndexQuery
  ): Promise<ViewDraftInvoiceIndexResponse> {
    const invoices = await this.getInvoices(query)

    const pagination = typeormPagination(query.pagination)

    return new ViewDraftInvoiceIndexResponse(invoices, null, pagination.take, pagination.skip)
  }

  private async getInvoices (
    query: ViewDraftInvoiceIndexQuery
  ): Promise<DraftInvoice[]> {
    const sapQuery = this.getInvoiceQuery(query)
    const responses = await this.sapGetDraftInvoiceIndexUseCase.execute(sapQuery)

    return DraftInvoiceMapper.fromSapResponses(responses)
  }

  private getInvoiceQuery (
    query: ViewDraftInvoiceIndexQuery
  ): SapQuery<SapGetDraftInvoiceIndexResponse> {
    const sapQuery = new SapQuery<SapGetDraftInvoiceIndexResponse>(query)
      .addOrderBy('Vbeln')

    this.applyStatusFilter(query, sapQuery)
    this.applyCustomerFilter(sapQuery)

    return sapQuery
  }

  private applyCustomerFilter (
    sapQuery: SapQuery<SapGetDraftInvoiceIndexResponse>
  ): void {
    const selectedCustomerId = this.authContext.getSelectedCustomerId()

    if (selectedCustomerId != null) {
      sapQuery.andWhere('Kunrg', selectedCustomerId)
    }
  }

  private applyStatusFilter (
    query: ViewDraftInvoiceIndexQuery,
    sapQuery: SapQuery<SapGetDraftInvoiceIndexResponse>
  ): void {
    if (
      query.filter.statuses !== undefined
      && query.filter.statuses.length > 0
    ) {
      const isInternalUser = this.authContext.isInternalUser()
      sapQuery.where((qb) => {
        for (const [index, status] of query.filter.statuses.entries()) {
          if (index === 0) {
            qb.where(qb1 => this.addStatusQuery(status, qb1, isInternalUser))
          } else {
            qb.orWhere(qb1 => this.addStatusQuery(status, qb1, isInternalUser))
          }
        }

        return qb
      })
    }
  }

  private addStatusQuery (
    status: DraftInvoiceFilterStatus,
    filterGroup: SapFilterGroup<SapGetDraftInvoiceIndexResponse>,
    isInternalUser: boolean
  ): SapFilterGroup<SapGetDraftInvoiceIndexResponse> {
    switch (status) {
      case (DraftInvoiceFilterStatus.TO_BE_APPROVED):
        return isInternalUser
          ? filterGroup.where('Salestoberel', true)
          : filterGroup.where('Custtoberel', true)

      case (DraftInvoiceFilterStatus.APPROVED):
        return isInternalUser
          ? filterGroup.where('Salesapprove', true)
          : filterGroup.where('Custapprove', true)

      case (DraftInvoiceFilterStatus.REJECTED):
        return isInternalUser
          ? filterGroup.where('Salesreject', true)
          : filterGroup.where('Custreject', true)
      default:
        exhaustiveCheck(status)
    }
  }
}
