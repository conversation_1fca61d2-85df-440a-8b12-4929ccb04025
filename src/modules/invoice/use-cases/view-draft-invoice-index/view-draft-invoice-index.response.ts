import { ApiProperty } from '@nestjs/swagger'
import { PaginatedOffsetResponse } from '@wisemen/pagination'
import { DraftInvoice } from '../../types/draft-invoice-type.js'
import { DraftInvoiceDynamicTableFields } from '../../types/draft-invoice.dynamic-table-fields.type.js'
import { DraftInvoiceStatus, DraftInvoiceStatusApiProperty } from '../../enums/draft-invoice-status.enum.js'
import { MailStatus, MailStatusApiProperty } from '../../enums/mail-status.enum.js'
import { calculatePageSize } from '../../../../utils/helpers/unknown-page-size.helper.js'

export class DraftInvoiceResponse implements DraftInvoiceDynamicTableFields {
  @ApiProperty({ type: String })
  invoiceNumber: string

  @DraftInvoiceStatusApiProperty()
  status: DraftInvoiceStatus

  @ApiProperty({ type: String })
  payerId: string

  @ApiProperty({ type: String })
  payerName: string

  @ApiProperty({ type: String, format: 'date' })
  issuedOn: string

  @MailStatusApiProperty()
  firstReminderMailStatus: MailStatus

  @ApiProperty({ type: String, format: 'date', nullable: true })
  firstReminderOn: string | null

  @MailStatusApiProperty()
  secondReminderMailStatus: MailStatus

  @ApiProperty({ type: String, format: 'date', nullable: true })
  secondReminderOn: string | null

  @MailStatusApiProperty()
  thirdReminderStatus: MailStatus

  @ApiProperty({ type: String, format: 'date', nullable: true })
  autoApprovedOn: string | null

  @ApiProperty({ type: String })
  netAmount: string

  @ApiProperty({ type: String })
  vatAmount: string

  @ApiProperty({ type: String })
  currency: string

  constructor (invoice: DraftInvoice) {
    this.invoiceNumber = invoice.invoiceNumber
    this.status = invoice.status
    this.payerId = invoice.payerId
    this.payerName = invoice.payerName
    this.issuedOn = invoice.issuedOn
    this.firstReminderMailStatus = invoice.firstReminderMailStatus
    this.firstReminderOn = invoice.firstReminderOn
    this.secondReminderMailStatus = invoice.secondReminderMailStatus
    this.secondReminderOn = invoice.secondReminderOn
    this.thirdReminderStatus = invoice.thirdReminderStatus
    this.autoApprovedOn = invoice.autoApprovedOn
    this.netAmount = invoice.netAmount
    this.vatAmount = invoice.vatAmount
    this.currency = invoice.currency
  }
}

export class ViewDraftInvoiceIndexResponse extends PaginatedOffsetResponse<DraftInvoiceResponse> {
  @ApiProperty({ type: DraftInvoiceResponse, isArray: true })
  declare items: DraftInvoiceResponse[]

  constructor (items: DraftInvoiceResponse[], total: number | null, limit: number, offset: number) {
    const result = items.map(contact => new DraftInvoiceResponse(contact))

    if (total === null) {
      total = calculatePageSize(items, offset, limit)
    }

    super(result, total, limit, offset)
  }
}
