import { Controller, Get, Query } from '@nestjs/common'
import { ApiTags, ApiOkResponse, ApiOAuth2 } from '@nestjs/swagger'
import { Permissions } from '../../../permission/permission.decorator.js'
import { Permission } from '../../../permission/permission.enum.js'
import { ApiForbiddenErrorResponse } from '../../../exceptions/api-errors/api-error-response.decorator.js'
import { ForbiddenError } from '../../../exceptions/generic/forbidden.error.js'
import { GlobalCustomerRequired } from '../../../auth/decorators/no-customer-required.decorator.js'
import { ViewDraftInvoiceIndexUseCase as ViewDraftInvoiceIndexUseCase } from './view-draft-invoice-index.use-case.js'
import { ViewDraftInvoiceIndexQuery as ViewDraftInvoiceIndexQuery } from './queries/view-draft-invoice-index.query.js'
import { ViewDraftInvoiceIndexResponse as ViewDraftInvoiceIndexResponse } from './view-draft-invoice-index.response.js'

@ApiTags('Draft invoice')
@ApiOAuth2([])
@Controller('draft-invoices')
export class ViewDraftInvoiceIndexController {
  constructor (
    private readonly useCase: ViewDraftInvoiceIndexUseCase
  ) { }

  @Get()
  @GlobalCustomerRequired()
  @Permissions(Permission.INVOICE_READ)
  @ApiOkResponse({ type: ViewDraftInvoiceIndexResponse })
  @ApiForbiddenErrorResponse(ForbiddenError)
  public async viewInvoiceIndex (
    @Query() query: ViewDraftInvoiceIndexQuery
  ): Promise<ViewDraftInvoiceIndexResponse> {
    return await this.useCase.execute(query)
  }
}
