import { ApiProperty } from '@nestjs/swagger'
import { InvoiceStatusApiProperty, InvoiceStatus } from '../../enums/invoice-status.enum.js'
import { InvoiceTypeApiProperty, InvoiceType } from '../../enums/invoice-type.enum.js'
import { Invoice } from '../../types/invoice.type.js'

export class ViewInvoiceResponse {
  @ApiProperty({ type: String })
  invoiceNumber: string

  @InvoiceStatusApiProperty()
  status: InvoiceStatus

  @ApiProperty({ type: String, format: 'date' })
  issuedOn: string

  @ApiProperty({ type: String, format: 'date', nullable: true })
  dueOn: string | null

  @ApiProperty({ type: String })
  customerName: string

  @ApiProperty({ type: String, nullable: true })
  customerReference: string | null

  @InvoiceTypeApiProperty()
  type: InvoiceType

  @ApiProperty({ type: String })
  payerId: string

  @ApiProperty({ type: String })
  payerName: string

  @ApiProperty({ type: String })
  netAmount: string

  @ApiProperty({ type: String })
  vatAmount: string

  @ApiProperty({ type: String })
  currency: string

  @ApiProperty({ type: String, nullable: true })
  accountDocumentNumber: string | null

  @ApiProperty({ type: String, nullable: true })
  accountManagerName: string | null

  @ApiProperty({ type: String })
  companyName: string

  @ApiProperty({ type: String, nullable: true })
  certificateFileName: string | null

  // TODO: - Add pdf

  constructor (invoice: Invoice, fileName: string | null) {
    this.invoiceNumber = invoice.invoiceNumber
    this.status = invoice.status
    this.issuedOn = invoice.issueDate
    this.dueOn = invoice.dueDate
    this.customerName = invoice.customerName
    this.customerReference = invoice.customerReference ?? null
    this.type = invoice.type
    this.payerId = invoice.payerId
    this.payerName = invoice.payerName
    this.netAmount = invoice.netAmount
    this.vatAmount = invoice.vatAmount
    this.currency = invoice.currency
    this.accountDocumentNumber = invoice.accountDocumentNumber ?? null
    this.accountManagerName = invoice.accountManagerName ?? null
    this.companyName = invoice.companyName
    this.certificateFileName = fileName
  }
}
