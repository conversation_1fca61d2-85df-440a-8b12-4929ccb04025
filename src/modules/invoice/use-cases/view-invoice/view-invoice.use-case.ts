import { Injectable } from '@nestjs/common'
import { AuthContext } from '../../../auth/auth.context.js'
import { SapGetInvoiceIndexUseCase } from '../../../sap/use-cases/get-invoice-index/get-invoice-index.use-case.js'
import { SapGetInvoiceIndexResponse } from '../../../sap/use-cases/get-invoice-index/get-invoice-index.response.js'
import { SapQuery } from '../../../sap/query/sap-query.js'
import { InvoiceMapper } from '../../mappers/invoice.mapper.js'
import { InvoiceNotFoundError } from '../../errors/invoice-not-found.error.js'
import { SapGetCertificateIndexUseCase } from '../../../sap/use-cases/get-certificate-index/get-certificate-index.use-case.js'
import { SapGetCertificateIndexResponse } from '../../../sap/use-cases/get-certificate-index/get-certificate-index.response.js'
import { ViewInvoiceResponse } from './view-invoice.response.js'

@Injectable()
export class ViewInvoiceDetailUseCase {
  constructor (
    private readonly authContext: AuthContext,
    private readonly sapGetInvoiceIndexUseCase: SapGetInvoiceIndexUseCase,
    private readonly sapGetInvoiceCertificateUseCase: SapGetCertificateIndexUseCase
  ) {}

  async execute (
    requestNumber: string
  ): Promise<ViewInvoiceResponse> {
    const invoiceQuery = this.getInvoiceSapQuery(requestNumber)
    const certificateQuery = this.getCertificateSapQuery(requestNumber)

    const [invoiceResults, certificateResults] = await Promise.all([
      this.sapGetInvoiceIndexUseCase.execute(invoiceQuery),
      this.sapGetInvoiceCertificateUseCase.execute(certificateQuery)
    ])

    if (invoiceResults.length === 0) {
      throw new InvoiceNotFoundError({ invoiceNumber: requestNumber })
    }

    const invoice = InvoiceMapper.fromSapResponse(invoiceResults[0])
    const fileName = certificateResults
      .map(certificate => certificate.InvoiceUrl)
      .at(0) ?? null

    return new ViewInvoiceResponse(invoice, fileName)
  }

  private getInvoiceSapQuery (
    requestNumber: string
  ): SapQuery<SapGetInvoiceIndexResponse> {
    const selectedCustomerId = this.authContext.getSelectedCustomerId()
    const sapQuery = new SapQuery<SapGetInvoiceIndexResponse>()
      .where('Vbeln', requestNumber)
      .setTop(1)

    if (selectedCustomerId !== null) {
      sapQuery.andWhere('Kunrg', selectedCustomerId)
    }

    return sapQuery
  }

  private getCertificateSapQuery (requestNumber: string): SapQuery<SapGetCertificateIndexResponse> {
    const selectedCustomerId = this.authContext.getSelectedCustomerId()
    const sapQuery = new SapQuery<SapGetCertificateIndexResponse>()
      .where('Invoice', requestNumber)

    if (selectedCustomerId !== null) {
      sapQuery.andWhere('Kunnr', selectedCustomerId)
    }

    return sapQuery
  }
}
