import { Controller, Get, Param } from '@nestjs/common'
import { ApiTags, ApiOkResponse, ApiOAuth2 } from '@nestjs/swagger'
import { Permissions } from '../../../permission/permission.decorator.js'
import { Permission } from '../../../permission/permission.enum.js'
import { GlobalCustomerRequired } from '../../../auth/decorators/no-customer-required.decorator.js'
import { ViewInvoiceDetailUseCase } from './view-invoice.use-case.js'
import { ViewInvoiceResponse } from './view-invoice.response.js'

@ApiTags('Invoice')
@ApiOAuth2([])
@Controller('invoices/:requestNumber')
export class ViewInvoiceDetailController {
  constructor (
    private readonly useCase: ViewInvoiceDetailUseCase
  ) { }

  @Get()
  @GlobalCustomerRequired()
  @Permissions(Permission.INVOICE_READ, Permission.INVOICE_MANAGE)
  @ApiOkResponse({ type: ViewInvoiceResponse })
  public async viewInvoice (
    @Param('requestNumber') requestNumber: string
  ): Promise<ViewInvoiceResponse> {
    return await this.useCase.execute(requestNumber)
  }
}
