import { Module } from '@nestjs/common'
import { ViewInvoiceIndexModule } from './use-cases/view-invoice-index/view-invoice-index.module.js'
import { ApproveDraftInvoiceModule } from './use-cases/approve-draft-invoice/approve-draft-invoice.module.js'
import { ViewDraftInvoiceIndexModule } from './use-cases/view-draft-invoice-index/view-draft-invoice-index.module.js'
import { RejectDraftInvoiceModule } from './use-cases/reject-draft-invoice/reject-draft-invoice.module.js'
import { ViewInvoiceModule } from './use-cases/view-invoice/view-invoice.module.js'
import { ExportInvoicesExcelModule } from './use-cases/export-invoices-excel/export-invoices-excel.module.js'
import { DownloadInvoiceModule } from './use-cases/download-invoice/download-invoice.module.js'
import { DownloadInvoiceCertificateModule } from './use-cases/download-invoice-certificate/download-invoice-certificate.module.js'
import { ExportDraftInvoicesExcelModule } from './use-cases/export-draft-invoices-excel/export-draft-invoices-excel.module.js'

@Module({
  imports: [
    DownloadInvoiceCertificateModule,
    DownloadInvoiceModule,
    ExportInvoicesExcelModule,
    ViewInvoiceIndexModule,
    ViewInvoiceModule,
    ViewDraftInvoiceIndexModule,
    ExportDraftInvoicesExcelModule,
    ApproveDraftInvoiceModule,
    RejectDraftInvoiceModule
  ]
})
export class InvoiceModule {}
