import { Module } from '@nestjs/common'
import { ViewInvoiceIndexModule } from './use-cases/view-invoice-index/view-invoice-index.module.js'
import { ApproveDraftInvoiceModule } from './use-cases/approve-draft-invoice/approve-draft-invoice.module.js'
import { ViewDraftInvoiceIndexModule } from './use-cases/view-draft-invoice-index/view-draft-invoice-index.module.js'
import { RejectDraftInvoiceModule } from './use-cases/reject-draft-invoice/reject-draft-invoice.module.js'
import { ViewInvoiceModule } from './use-cases/view-invoice/view-invoice.module.js'
import { ExportInvoicesCsvModule } from './use-cases/export-invoices-csv/export-invoices-csv.module.js'
import { ExportDraftInvoicesCsvModule } from './use-cases/export-draft-invoices-csv/export-draft-invoices-csv.module.js'

@Module({
  imports: [
    ViewInvoiceIndexModule,
    ExportInvoicesCsvModule,
    ViewInvoiceModule,
    ViewDraftInvoiceIndexModule,
    ExportDraftInvoicesCsvModule,
    ApproveDraftInvoiceModule,
    RejectDraftInvoiceModule
  ]
})
export class InvoiceModule {}
