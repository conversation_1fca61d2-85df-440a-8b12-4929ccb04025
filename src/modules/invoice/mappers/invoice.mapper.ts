import assert from 'assert'
import { SapGetInvoiceIndexResponse } from '../../sap/use-cases/get-invoice-index/get-invoice-index.response.js'
import { Invoice } from '../types/invoice.type.js'
import { SapDateFormatterService } from '../../sap/services/sap-date-formatter.service.js'
import { InvoiceTypeMapper } from '../enums/invoice-type.enum.js'
import { toNullableString } from '../../../utils/strings/to-nullable-string.js'
import { InvoiceStatusMapper } from './invoice-status.mapper.js'

export class InvoiceMapper {
  static fromSapResponse (
    sapResponse: SapGetInvoiceIndexResponse
  ): Invoice {
    assert(sapResponse.Vbeln !== undefined)
    assert(sapResponse.Netwr !== undefined)
    assert(sapResponse.Fkdat !== undefined)
    assert(sapResponse.KunagName1 !== undefined)
    assert(sapResponse.Kunag !== undefined)
    assert(sapResponse.Mwsbk !== undefined)
    assert(sapResponse.Waerk !== undefined)
    assert(sapResponse.Butxt !== undefined)
    assert(sapResponse.Vbtyp !== undefined)
    assert(sapResponse.Kunrg !== undefined)
    assert(sapResponse.KunrgName1 !== undefined)
    assert(sapResponse.SalesRep !== undefined)
    assert(sapResponse.Belnr !== undefined)
    assert(sapResponse.Bstkd !== undefined)

    return {
      invoiceNumber: sapResponse.Vbeln,
      netAmount: sapResponse.Netwr,
      vatAmount: sapResponse.Mwsbk,
      currency: sapResponse.Waerk,
      customerId: sapResponse.Kunag,
      customerName: sapResponse.KunagName1,
      companyName: sapResponse.Butxt,
      type: InvoiceTypeMapper.fromSap(sapResponse.Vbtyp),
      payerId: sapResponse.Kunrg,
      payerName: sapResponse.KunrgName1,
      accountManagerName: toNullableString(sapResponse.SalesRep),
      accountDocumentNumber: toNullableString(sapResponse.Belnr),
      customerReference: toNullableString(sapResponse.Bstkd),
      issueDate: SapDateFormatterService.parseDateToString(sapResponse.Fkdat)!,
      dueDate: sapResponse.Duedate != null
        ? SapDateFormatterService.parseDateToString(sapResponse.Duedate)
        : null,
      status: InvoiceStatusMapper.fromSapResponse(sapResponse)
    }
  }

  static fromSapResponses (
    sapResponses: SapGetInvoiceIndexResponse[]
  ): Invoice[] {
    return sapResponses.map((r: SapGetInvoiceIndexResponse) => this.fromSapResponse(r))
  }
}
