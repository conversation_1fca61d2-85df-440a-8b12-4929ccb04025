import { InvoiceStatus } from '../enums/invoice-status.enum.js'
import { InvoiceType } from '../enums/invoice-type.enum.js'

// TODO: - Add Fktyp once values are clarified https://linear.app/wisemen/issue/IND-462/as-a-user-i-want-to-filter-and-sort-through-final-invoices

export interface Invoice {
  invoiceNumber: string
  status: InvoiceStatus
  issueDate: string
  customerId: string
  customerName: string
  dueDate: string | null
  customerReference: string | null
  type: InvoiceType
  payerId: string
  payerName: string
  netAmount: string
  vatAmount: string
  currency: string
  accountDocumentNumber: string | null
  accountManagerName: string | null
  companyName: string
}
