import { ApiProperty, ApiPropertyOptions } from '@nestjs/swagger'

export enum DraftInvoiceFilterStatus {
  TO_BE_APPROVED = 'to_be_approved',
  APPROVED = 'approved',
  REJECTED = 'rejected'
}
export function DraftInvoiceFilterStatusApiProperty (
  options?: ApiPropertyOptions
): PropertyDecorator {
  return ApiProperty({
    ...options,
    enumName: 'DraftInvoiceFilterStatus',
    enum: DraftInvoiceFilterStatus
  })
}

export enum DraftInvoiceStatus {
  APPROVED_BY_CUSTOMER = 'approved_by_customer',
  AUTO_APPROVED = 'auto_approved',
  INTERNAL_APPROVED = 'internal_approved',
  REJECTED_BY_CUSTOMER = 'rejected_by_customer',
  REJECTED_BY_INDAVER = 'rejected_by_indaver',
  TO_BE_APPROVED_BY_CUSTOMER = 'to_be_approved_by_customer',
  TO_BE_APPROVED_BY_INDAVER = 'to_be_approved_by_indaver'
}

export function DraftInvoiceStatusApiProperty (
  options?: ApiPropertyOptions
): PropertyDecorator {
  return ApiProperty({
    ...options,
    enumName: 'DraftInvoiceStatus',
    enum: DraftInvoiceStatus
  })
}

export const CUSTOMER_DRAFT_INVOICE_STATUSES = [
  DraftInvoiceStatus.APPROVED_BY_CUSTOMER,
  DraftInvoiceStatus.AUTO_APPROVED,
  DraftInvoiceStatus.REJECTED_BY_CUSTOMER,
  DraftInvoiceStatus.TO_BE_APPROVED_BY_CUSTOMER
]

export const SALES_REP_EXCLUSIVE_DRAFT_INVOICE_STATUSES = [
  DraftInvoiceStatus.REJECTED_BY_INDAVER,
  DraftInvoiceStatus.TO_BE_APPROVED_BY_INDAVER,
  DraftInvoiceStatus.INTERNAL_APPROVED
]

export const APPROVABLE_DRAFT_INVOICE_STATUSES = [
  DraftInvoiceStatus.TO_BE_APPROVED_BY_CUSTOMER,
  DraftInvoiceStatus.TO_BE_APPROVED_BY_INDAVER
]

export const INTERNAL_APPROVABLE_STATUSES = [
  DraftInvoiceStatus.TO_BE_APPROVED_BY_INDAVER
]
