import { Injectable } from '@nestjs/common'
import { FieldMustBeNullError } from '../../exceptions/generic/field-must-be-null.error.js'
import { CustomerNotProvidedError } from '../../customer/errors/customer-not-provided.error.js'
import { CustomerPickUpAddressAuthService } from '../../auth/services/customer-pick-up-address-auth.service.js'
import { CustomerWasteProducerAuthService } from '../../auth/services/customer-waste-producer-auth.service.js'
import { UserWasteProducerAuthService } from '../../auth/services/user-waste-producer-auth.service.js'
import { WasteProducerNotAccessibleError } from '../../waste-producer/errors/waste-producer-not-accessible.error.js'
import { AuthContext } from '../../auth/auth.context.js'
import { MissingRequiredFieldError } from '../../exceptions/generic/missing-required-field.error.js'
import { PackagingRequestGeneralInfoValidationUtil } from '../utils/packaging-request-general-info-validation.util.js'
import { UpdatePackagingRequestCommand } from '../use-cases/update-packaging-request/update-packaging-request.command.js'
import { PackagingRequest } from '../types/packaging-request.type.js'
import { DeliveryAddressNotAccessibleError } from '../errors/delivery-address-not-accessible.error.js'
import { SelectedCustomerFilterMismatchError } from '../../customer/errors/selected-customer-filter-mismatch.error.js'

@Injectable()
export class PackagingRequestGeneralInfoValidator {
  constructor (
    private readonly authContext: AuthContext,
    private readonly customerWasteProducerAuthService: CustomerWasteProducerAuthService,
    private readonly userWasteProducerAuthService: UserWasteProducerAuthService,
    private readonly customerPickUpAddressAuthService: CustomerPickUpAddressAuthService
  ) {}

  private validationUtil: PackagingRequestGeneralInfoValidationUtil

  async validateUpdate (
    command: UpdatePackagingRequestCommand,
    packagingRequest: PackagingRequest
  ) {
    this.validationUtil = new PackagingRequestGeneralInfoValidationUtil(packagingRequest)

    if (command.customerId !== undefined) {
      this.validateCustomerId(command.customerId, false)
    }

    if (command.customerId != null) {
      this.canAccessCustomerId(command.customerId)
    }

    if (command.wasteProducerId !== undefined) {
      this.validateWasteProducerId(command.wasteProducerId, false)
    }

    if (command.wasteProducerId != null) {
      await this.canAccessWasteProducerId(
        command.wasteProducerId,
        packagingRequest.customerId ?? null
      )
    }

    if (command.deliveryAddressId !== undefined) {
      this.validateDeliveryAddress(command.deliveryAddressId, false)
    }

    if (command.deliveryAddressId != null) {
      await this.canAccessDeliveryAddress(
        command.deliveryAddressId,
        packagingRequest.customerId ?? null
      )
    }
  }

  async validateSubmit (
    packagingRequest: PackagingRequest
  ): Promise<void> {
    this.validationUtil = new PackagingRequestGeneralInfoValidationUtil(packagingRequest)

    this.validateCustomerId(packagingRequest.customerId)

    if (packagingRequest.customerId !== null) {
      this.canAccessCustomerId(packagingRequest.customerId)
    }

    this.validateWasteProducerId(packagingRequest.wasteProducerId)

    if (packagingRequest.wasteProducerId != null) {
      await this.canAccessWasteProducerId(
        packagingRequest.wasteProducerId,
        packagingRequest.customerId ?? null
      )
    }

    this.validateDeliveryAddress(packagingRequest.deliveryAddressId)

    if (packagingRequest.deliveryAddressId != null) {
      await this.canAccessDeliveryAddress(
        packagingRequest.deliveryAddressId,
        packagingRequest.customerId ?? null
      )
    }
  }

  private validateCustomerId (
    customerId: string | null,
    isSubmit: boolean = true
  ): void {
    if (!this.validationUtil.isCustomerIdAllowed) {
      if (customerId !== null) {
        throw new FieldMustBeNullError({ pointer: '$.customerId' })
      }
    }

    if (isSubmit && this.validationUtil.isCustomerIdRequired) {
      if (customerId === null) {
        throw new MissingRequiredFieldError({ pointer: '$.customerId' })
      }
    }
  }

  canAccessCustomerId (
    customerId: string
  ): void {
    const selectedCustomerId = this.authContext.getSelectedCustomerId()
    if (selectedCustomerId != null && selectedCustomerId !== customerId) {
      throw new SelectedCustomerFilterMismatchError({ pointer: '$.customerId' })
    }
  }

  private validateWasteProducerId (
    wasteProducerId: string | null,
    isSubmit: boolean = true
  ): void {
    if (!this.validationUtil.isWasteProducerIdAllowed) {
      if (wasteProducerId !== null) {
        throw new FieldMustBeNullError({ pointer: '$.wasteProducerId' })
      }
    }

    if (isSubmit && this.validationUtil.isWasteProducerIdRequired) {
      if (wasteProducerId === null) {
        throw new MissingRequiredFieldError({ pointer: '$.wasteProducerId' })
      }
    }
  }

  private async canAccessWasteProducerId (
    wasteProducerId: string,
    customerId: string | null
  ) {
    const userId = this.authContext.getAzureEntraUpn()
    const pointer = '$.wasteProducerId'

    if (customerId === null) {
      throw new CustomerNotProvidedError({ pointer })
    }

    const canCustomerAccessWasteProducer = await this.customerWasteProducerAuthService
      .canCustomerAccessWasteProducer(
        customerId,
        wasteProducerId
      )

    if (!canCustomerAccessWasteProducer) {
      throw new WasteProducerNotAccessibleError({ pointer })
    }

    const canUserAccessWasteProducer = await this.userWasteProducerAuthService
      .canUserAccessWasteProducer(
        userId,
        customerId,
        wasteProducerId
      )

    if (!canUserAccessWasteProducer) {
      throw new WasteProducerNotAccessibleError({ pointer })
    }
  }

  private validateDeliveryAddress (
    deliveryAddressId: string | null,
    isSubmit: boolean = true
  ): void {
    if (!this.validationUtil.isDeliveryAddressIdAllowed) {
      if (deliveryAddressId !== null) {
        throw new FieldMustBeNullError({ pointer: '$.deliveryAddressId' })
      }
    }

    if (isSubmit && this.validationUtil.isDeliveryAddressIdRequired) {
      if (deliveryAddressId === null) {
        throw new MissingRequiredFieldError({ pointer: '$.deliveryAddressId' })
      }
    }
  }

  private async canAccessDeliveryAddress (
    deliveryAddressId: string,
    customerId: string | null
  ): Promise<void> {
    const pointer = '$.deliveryAddressId'

    if (customerId === null) {
      throw new CustomerNotProvidedError({ pointer })
    }

    const canCustomerAccessPickUpAddress = await this.customerPickUpAddressAuthService
      .canCustomerAccessPickUpAddresses(
        customerId,
        [deliveryAddressId]
      )

    if (!canCustomerAccessPickUpAddress) {
      throw new DeliveryAddressNotAccessibleError({ pointer })
    }
  }
}
