import { afterEach, before, describe, it } from 'node:test'
import { randomUUID } from 'node:crypto'
import { expect } from 'expect'
import { createStubInstance, SinonStubbedInstance } from 'sinon'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { CopyPackagingRequestValidator } from '../copy-packaging-request.validator.js'
import { PackagingRequestNotFoundError } from '../../../errors/packaging-request-sap-not-found.error.js'
import { SapGetPickUpRequestDetailResponseBuilder } from '../../../../sap/use-cases/get-pick-up-request-detail/tests/get-pick-up-request-detail.response.builder.js'
import { CopyNonSubmittedPackagingRequestError } from '../../../errors/copy-non-submitted-packaging-request.error.js'
import { InvalidPackagingRequestCopyError } from '../../../errors/invalid-packaging-request-copy.error.js'
import { AuthContext } from '../../../../auth/auth.context.js'
import { UserWasteProducerAuthService } from '../../../../auth/services/user-waste-producer-auth.service.js'

describe('CopyPackagingRequestValidator - Unit Tests', () => {
  let validator: CopyPackagingRequestValidator

  let authContext: SinonStubbedInstance<AuthContext>
  let userWasteProducerAuthService: SinonStubbedInstance<UserWasteProducerAuthService>

  before(() => {
    TestBench.setupUnitTest()

    authContext = createStubInstance(AuthContext)
    userWasteProducerAuthService = createStubInstance(UserWasteProducerAuthService)

    validator = new CopyPackagingRequestValidator(
      authContext,
      userWasteProducerAuthService
    )

    mockMethods()
  })

  afterEach(() => {
    mockMethods()
  })

  function mockMethods (): void {
    authContext.getSelectedCustomerId.returns(null)
    authContext.getAzureEntraUpn.returns(randomUUID())
    userWasteProducerAuthService.canUserAccessWasteProducer.resolves(true)
  }

  it('throws PackagingRequestNotFoundError when response is empty', async () => {
    await expect(validator.validate(randomUUID(), []))
      .rejects
      .toThrow(PackagingRequestNotFoundError)
  })

  it('throws CopyNonSubmittedPackagingRequestError when packaging request is not submitted', async () => {
    const details = [
      new SapGetPickUpRequestDetailResponseBuilder()
        .withStatus('004')
        .withWasteType('06')
        .build()
    ]

    await expect(validator.validate(randomUUID(), details))
      .rejects
      .toThrow(CopyNonSubmittedPackagingRequestError)
  })

  it('throws InvalidPackagingRequestCopyError when response gives back non packaging requests', async () => {
    const details = [
      new SapGetPickUpRequestDetailResponseBuilder()
        .withStatus('006')
        .withWasteType('01')
        .build()
    ]

    await expect(validator.validate(randomUUID(), details))
      .rejects
      .toThrow(InvalidPackagingRequestCopyError)
  })

  it('throws PackagingRequestNotFoundError when customer is not accessible', async () => {
    authContext.getSelectedCustomerId.returns(randomUUID())

    const details = [
      new SapGetPickUpRequestDetailResponseBuilder()
        .withStatus('000')
        .withWasteType('06')
        .withKunnr(randomUUID())
        .build()
    ]

    await expect(validator.validate(randomUUID(), details))
      .rejects
      .toThrow(PackagingRequestNotFoundError)
  })

  it('throws PackagingRequestNotFoundError when waste producer is not accessible', async () => {
    const customerId = randomUUID()
    authContext.getSelectedCustomerId.returns(customerId)
    userWasteProducerAuthService.canUserAccessWasteProducer.resolves(false)

    const details = [
      new SapGetPickUpRequestDetailResponseBuilder()
        .withStatus('000')
        .withWasteType('06')
        .withKunnr(customerId)
        .withKunnrY2(randomUUID())
        .build()
    ]

    await expect(validator.validate(randomUUID(), details))
      .rejects
      .toThrow(PackagingRequestNotFoundError)
  })

  it('throws no error when the packaging request is submitted', async () => {
    const details = [
      new SapGetPickUpRequestDetailResponseBuilder()
        .withStatus('000')
        .withWasteType('06')
        .build()
    ]

    await expect(validator.validate(randomUUID(), details))
      .resolves
      .not
      .toThrow()
  })
})
