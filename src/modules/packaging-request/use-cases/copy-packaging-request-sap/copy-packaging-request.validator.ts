import assert from 'assert'
import { Injectable } from '@nestjs/common'
import { SapGetPickUpRequestDetailResponse } from '../../../sap/use-cases/get-pick-up-request-detail/get-pick-up-request-detail.response.js'
import { SAP_PACKAGE_WASTE_TYPES } from '../../../sap/types/waste.type.js'
import { PackagingRequestNotFoundError } from '../../errors/packaging-request-sap-not-found.error.js'
import { InvalidPackagingRequestCopyError } from '../../errors/invalid-packaging-request-copy.error.js'
import { SAP_SUBMITTED_STATUS } from '../../../pick-up-request/enums/pick-up-request-status.enum.js'
import { CopyNonSubmittedPackagingRequestError } from '../../errors/copy-non-submitted-packaging-request.error.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { UserWasteProducerAuthService } from '../../../auth/services/user-waste-producer-auth.service.js'

@Injectable()
export class CopyPackagingRequestValidator {
  constructor (
    private readonly authContext: AuthContext,
    private readonly userWasteProducerAuthService: UserWasteProducerAuthService
  ) {}

  async validate (
    requestNumber: string,
    responses: SapGetPickUpRequestDetailResponse[]
  ): Promise<void> {
    this.validateResponse(requestNumber, responses)
    this.validateIsPackagingRequest(requestNumber, responses)
    await this.validatePackagingRequestAccessible(responses)

    const hasNonSubmittedPackaging = responses.some((r) => {
      return r.Status === undefined || !SAP_SUBMITTED_STATUS.includes(r.Status)
    })

    if (hasNonSubmittedPackaging) {
      throw new CopyNonSubmittedPackagingRequestError()
    }
  }

  private validateResponse (
    requestNumber: string,
    responses: SapGetPickUpRequestDetailResponse[]
  ): void {
    if (responses.length === 0) {
      throw new PackagingRequestNotFoundError({ requestNumber })
    }
  }

  private validateIsPackagingRequest (
    requestNumber: string,
    responses: SapGetPickUpRequestDetailResponse[]
  ): void {
    const hasNonPackagingRequestStatus = responses
      .some((response) => {
        return (
          response.WasteType === undefined
          || response.WasteType === ''
          || !SAP_PACKAGE_WASTE_TYPES.includes(response.WasteType))
      })

    if (hasNonPackagingRequestStatus) {
      throw new InvalidPackagingRequestCopyError({ requestNumber })
    }
  }

  private async validatePackagingRequestAccessible (
    responses: SapGetPickUpRequestDetailResponse[]
  ): Promise<void> {
    for (const packagingRequest of responses) {
      assert(packagingRequest.Kunnr !== undefined)
      assert(packagingRequest.KunnrY2 !== undefined)

      if (packagingRequest.Kunnr === '') {
        continue
      }

      const selectedCustomerId = this.authContext.getSelectedCustomerId()
      if (selectedCustomerId != null && selectedCustomerId !== packagingRequest.Kunnr) {
        throw new PackagingRequestNotFoundError({ requestNumber: packagingRequest.Reqno ?? 'Reqno not given' })
      }

      if (packagingRequest.KunnrY2 === '') {
        continue
      }

      const userId = this.authContext.getAzureEntraUpn()
      const canUserAccessWasteProducer = await this.userWasteProducerAuthService
        .canUserAccessWasteProducer(
          userId,
          packagingRequest.Kunnr,
          packagingRequest.KunnrY2
        )
      if (!canUserAccessWasteProducer) {
        throw new PackagingRequestNotFoundError({ requestNumber: packagingRequest.Reqno ?? 'Reqno not given' })
      }
    }
  }
}
