import { after, before, describe, it, mock } from 'node:test'
import { randomUUID } from 'crypto'
import request from 'supertest'
import { expect } from 'expect'
import { Repository } from 'typeorm'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { EndToEndTestSetup } from '../../../../../../test/setup/end-to-end-test-setup.js'
import { Permission } from '../../../../permission/permission.enum.js'
import { TestUser } from '../../../../../app/users/tests/setup-user.type.js'
import { PickUpRequest } from '../../../../pick-up-request/entities/pick-up-request.entity.js'
import type { PackagingRequest } from '../../../types/packaging-request.type.js'
import { ValidPackagingRequestEntityBuilder } from '../../../tests/valid-packaging-request-entity.builder.js'
import { UserCustomerAuthService } from '../../../../auth/services/user-customer-auth.service.js'
import { UserCustomer } from '../../../../auth/entities/user-customer.entity.js'
import { UserCustomerEntityBuilder } from '../../../../auth/entities/user-customer.entity-builder.js'
import { SapGetContractLineIndexUseCase } from '../../../../sap/use-cases/get-contract-line-index/get-contract-line-index.use-case.js'
import { GetContractLineIndexResponseBuilder } from '../../../../sap/use-cases/get-contract-line-index/tests/get-contract-line-index.response.builder.js'

describe('Submit packaging request e2e test', () => {
  let setup: EndToEndTestSetup

  let packagingRequestRepository: Repository<PackagingRequest>
  let userCustomerRepository: Repository<UserCustomer>

  let unauthorizedUser: TestUser
  let authorizedUser: TestUser

  before(async () => {
    setup = await TestBench.setupEndToEndTest()

    packagingRequestRepository = setup.dataSource.getRepository(PickUpRequest)
    userCustomerRepository = setup.dataSource.getRepository(UserCustomer)

    const [_unauthorizedUser, _authorizedUser] = await Promise.all([
      await setup.authContext.getUser([]),
      await setup.authContext.getUser([Permission.PACKAGING_REQUEST_MANAGE])
    ])

    unauthorizedUser = _unauthorizedUser
    authorizedUser = _authorizedUser

    await userCustomerRepository.save(
      new UserCustomerEntityBuilder()
        .withUserId(authorizedUser.user.azureEntraUpn)
        .withCustomerId(randomUUID())
        .build()
    )
  })

  after(async () => await setup.teardown())

  it('returns 401 when not authenticated', async () => {
    const response = await request(setup.httpServer)
      .post(`/packaging-requests/${randomUUID()}/submit`)

    expect(response).toHaveStatus(401)
  })

  it('returns 403 when unauthorized', async () => {
    const response = await request(setup.httpServer)
      .post(`/packaging-requests/${randomUUID()}/submit`)
      .set('Authorization', `Bearer ${unauthorizedUser.token}`)

    expect(response).toHaveStatus(403)
  })

  it('returns 404 when packaging request created by other user', async () => {
    const packagingRequest = await packagingRequestRepository.save(
      new ValidPackagingRequestEntityBuilder()
        .createdByUser(unauthorizedUser.user)
        .build()
    )

    const response = await request(setup.httpServer)
      .post(`/packaging-requests/${packagingRequest.uuid}/submit`)
      .set('Authorization', `Bearer ${authorizedUser.token}`)

    expect(response).toHaveStatus(404)
  })

  it('returns 200 when authorized and request created by self', async () => {
    const packagingRequest = await packagingRequestRepository.save(
      new ValidPackagingRequestEntityBuilder()
        .createdByUser(authorizedUser.user)
        .build()
    )

    mock.method(UserCustomerAuthService.prototype, 'canUserAccessCustomer', () => {
      return true
    })
    mock.method(SapGetContractLineIndexUseCase.prototype, 'execute', () => {
      return [
        new GetContractLineIndexResponseBuilder()
          .withKunnr(packagingRequest.customerId!)
          .withKunnrWe(packagingRequest.pickUpAddressIds[0])
          .withVbeln(packagingRequest.packagingRequestMaterials[0].contractNumber)
          .withPosnr(packagingRequest.packagingRequestMaterials[0].contractItem)
          .build()
      ]
    })

    const response = await request(setup.httpServer)
      .post(`/packaging-requests/${packagingRequest.uuid}/submit`)
      .set('Authorization', `Bearer ${authorizedUser.token}`)

    expect(response).toHaveStatus(200)
  })
})
