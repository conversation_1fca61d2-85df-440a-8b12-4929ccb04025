import { SortDirection } from '@wisemen/pagination'
import { FilterWithKeyValue } from '../types/filter-with-key-value.type.js'

export interface DynamicTableViewConfiguration {
  visibleColumns: VisibilityConfiguration[]
  filters: FilterConfiguration[]
  sorting: SortConfiguration[]
}

export interface VisibilityConfiguration {
  uuid: string
  order: number
}

export interface FilterConfiguration {
  uuid: string
  value: string | string[] | boolean | FilterWithKeyValue | FilterWithKeyValue[]
}

export interface SortConfiguration {
  uuid: string
  order: number
  direction: SortDirection
}
