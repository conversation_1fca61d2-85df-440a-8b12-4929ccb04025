import * as fs from 'fs'
import { randomUUID } from 'crypto'
import { QueryRunner } from 'typeorm'
import { SeedDynamicTableType } from '../helpers/seed-columns.type.js'
import { TABLE_COLUMNS_FILE_PATHS } from '../constants/seed-columns-file-path.constants.js'
import { DynamicTableName } from '../enums/dynamic-table-name.enum.js'
import { DynamicTable } from '../entities/dynamic-table.entity.js'
import { exhaustiveCheck } from '../../../utils/helpers/exhaustive-check.helper.js'
import { DynamicTableViewConfiguration } from '../helpers/dynamic-table-view-configuration.js'

export class SeedDynamicColumns {
  static async up (
    queryRunner: QueryRunner,
    dynamicTableName: DynamicTableName
  ): Promise<void> {
    let url: string

    switch (dynamicTableName) {
      case DynamicTableName.CERTIFICATE:
        url = TABLE_COLUMNS_FILE_PATHS.CERTIFICATE
        break
      case DynamicTableName.CONTRACT_LINE:
        url = TABLE_COLUMNS_FILE_PATHS.CONTRACT_LINE
        break
      case DynamicTableName.CONTRACT_OVERVIEW:
        url = TABLE_COLUMNS_FILE_PATHS.CONTRACT_OVERVIEW
        break
      case DynamicTableName.DRAFT_INVOICE:
        url = TABLE_COLUMNS_FILE_PATHS.DRAFT_INVOICE
        break
      case DynamicTableName.FORM_PICK_UP_REQUEST_CONTRACT_LINE:
        url = TABLE_COLUMNS_FILE_PATHS.FORM_PICK_UP_REQUEST_CONTRACT_LINE
        break
      case DynamicTableName.INVOICE:
        url = TABLE_COLUMNS_FILE_PATHS.INVOICE
        break
      case DynamicTableName.PICK_UP_REQUEST:
        url = TABLE_COLUMNS_FILE_PATHS.PICK_UP_REQUEST
        break
      case DynamicTableName.WASTE_INQUIRY:
        url = TABLE_COLUMNS_FILE_PATHS.WASTE_INQUIRY
        break
      case DynamicTableName.GUIDANCE_LETTER:
        url = TABLE_COLUMNS_FILE_PATHS.GUIDANCE_LETTER
        break
      default:
        exhaustiveCheck(dynamicTableName)
    }

    const tableData = JSON.parse(fs.readFileSync(url, 'utf8')) as SeedDynamicTableType

    const dynamicTable = await queryRunner.query(`SELECT uuid FROM dynamic_table WHERE name = $1`, [dynamicTableName]) as DynamicTable[]

    const newDynamicTableUuid = randomUUID()

    if (dynamicTable.length === 0) {
      await queryRunner.query(
        `
        INSERT INTO dynamic_table (
          uuid,
          name,
          created_at,
          updated_at
        ) VALUES ($1, $2, now(), now())
        `,
        [newDynamicTableUuid, dynamicTableName]
      )
    }

    const dynamicTableUuid = dynamicTable.length > 0
      ? dynamicTable[0].uuid
      : newDynamicTableUuid

    for (const column of tableData.columns) {
      await queryRunner.query(
        `
          INSERT INTO dynamic_table_column (
            name,
            applicable_fields,
            searchable_fields,
            filterable_field,
            sortable_fields,
            is_hidable,
            dynamic_table_uuid
          ) VALUES (
            $1, $2::jsonb, $3::jsonb, $4::jsonb, $5::jsonb, $6, $7
          )
          ON CONFLICT (dynamic_table_uuid, name) DO UPDATE SET
            applicable_fields = EXCLUDED.applicable_fields,
            searchable_fields = EXCLUDED.searchable_fields,
            filterable_field = EXCLUDED.filterable_field,
            sortable_fields = EXCLUDED.sortable_fields,
            is_hidable = EXCLUDED.is_hidable
        `,
        [
          column.name,
          JSON.stringify(column.applicableFields),
          JSON.stringify(column.searchableFields),
          JSON.stringify(column.filterableField),
          JSON.stringify(column.sortableFields),
          column.isHidable,
          dynamicTableUuid
        ]
      )
    }

    if (dynamicTable.length > 0) {
      const deletedColumns = await queryRunner.query(
        `
          DELETE FROM dynamic_table_column
          WHERE dynamic_table_uuid = $1
          AND name NOT IN (${tableData.columns.map(column => `'${column.name}'`).join(', ')})
          RETURNING uuid
        `,
        [
          dynamicTable[0].uuid
        ]
      ) as [{ uuid: string }[], number]

      // If there are deleted columns, we need to update the dynamic table view configuration
      const deletedColumnUuids = deletedColumns[0]
        .map(c => c.uuid)
        .filter(uuid => uuid !== undefined)

      if (deletedColumnUuids.length > 0) {
        await queryRunner.query(
          `
            UPDATE dynamic_table_view
            SET
              configuration = jsonb_set(
                jsonb_set(
                  jsonb_set(
                    configuration,
                    '{visibleColumns}',
                    (
                      SELECT
                        COALESCE(jsonb_agg(
                          jsonb_set(t.elem, '{order}', to_jsonb(t.new_order))
                        ), '[]'::jsonb)
                      FROM (
                        SELECT
                          elem,
                          row_number() OVER (ORDER BY (elem ->> 'order')::int) - 1 AS new_order
                        FROM
                          jsonb_array_elements(configuration -> 'visibleColumns') AS elem
                        WHERE
                          elem ->> 'uuid' NOT IN (${deletedColumnUuids.map(uuid => `'${uuid}'`).join(', ')})
                      ) AS t
                    )
                  ),
                  '{filters}',
                  (
                    SELECT
                      COALESCE(jsonb_agg(elem), '[]'::jsonb)
                    FROM
                      jsonb_array_elements(configuration -> 'filters') AS elem
                    WHERE
                      elem ->> 'uuid' NOT IN (${deletedColumnUuids.map(uuid => `'${uuid}'`).join(', ')})
                  )
                ),
                '{sorting}',
                (
                  SELECT
                    COALESCE(jsonb_agg(
                      jsonb_set(t.elem, '{order}', to_jsonb(t.new_order))
                    ), '[]'::jsonb)
                  FROM (
                    SELECT
                      elem,
                      row_number() OVER (ORDER BY (elem ->> 'order')::int) - 1 AS new_order
                    FROM
                      jsonb_array_elements(configuration -> 'sorting') AS elem
                    WHERE
                      elem ->> 'uuid' NOT IN (${deletedColumnUuids.map(uuid => `'${uuid}'`).join(', ')})
                  ) AS t
                )
              )
            WHERE
              dynamic_table_uuid = $1
          `,
          [dynamicTable[0].uuid]
        )
      }
    }

    const dynamicTableDefaultView = await queryRunner.query(`SELECT uuid FROM dynamic_table_view WHERE dynamic_table_uuid = $1 AND is_global = true AND is_default_global = true`, [dynamicTableUuid]) as { uuid: string }[]
    if (dynamicTableDefaultView.length === 0) {
      const dynamicTableColumns = await queryRunner.query(
        `SELECT uuid FROM dynamic_table_column WHERE dynamic_table_uuid = $1`,
        [dynamicTableUuid]
      ) as { uuid: string }[]

      const visibleColumns = dynamicTableColumns.map((column, index) => ({
        uuid: column.uuid,
        order: index
      }))

      const configuration: DynamicTableViewConfiguration = {
        visibleColumns,
        filters: [],
        sorting: []
      }

      await queryRunner.query(
        `
          INSERT INTO dynamic_table_view (
            name,
            is_global,
            is_default_global,
            dynamic_table_uuid,
            configuration
          ) VALUES ($1, true, true, $2, $3)
        `,
        [`Default`, dynamicTableUuid, JSON.stringify(configuration)]
      )
    }
  }

  static async down (
    queryRunner: QueryRunner,
    dynamicTableName: DynamicTableName
  ): Promise<void> {
    const dynamicTable = await queryRunner.query(
      `SELECT uuid FROM dynamic_table WHERE name = $1`,
      [dynamicTableName]
    ) as DynamicTable | null

    if (dynamicTable === null) return

    await queryRunner.query(
      `DELETE FROM dynamic_table_view WHERE dynamic_table_uuid = $1`,
      [dynamicTable.uuid]
    )

    await queryRunner.query(
      `DELETE FROM dynamic_table_column WHERE dynamic_table_uuid = $1`,
      [dynamicTable.uuid]
    )

    await queryRunner.query(
      `DELETE FROM dynamic_table WHERE uuid = $1`,
      [dynamicTable.uuid]
    )
  }
}
