export enum DynamicTableName {
  CERTIFICATE = 'certificate',
  CONTRACT_LINE = 'contract-line', // Used in pick-up request and weekly planning request to show available contract lines
  CONTRACT_OVERVIEW = 'contract-overview', // Used in contract overview to show all contract lines of a contract
  DRAFT_INVOICE = 'draft-invoice',
  FORM_PICK_UP_REQUEST_CONTRACT_LINE = 'form-pick-up-request-contract-line', // Used in pick-up request form to show selected contract lines
  INVOICE = 'invoice',
  PICK_UP_REQUEST = 'pick-up-request',
  WASTE_INQUIRY = 'waste-inquiry',
  GUIDANCE_LETTER = 'guidance-letter'
}
