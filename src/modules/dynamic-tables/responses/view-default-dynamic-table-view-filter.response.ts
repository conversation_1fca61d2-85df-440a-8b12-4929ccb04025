import { ApiProperty } from '@nestjs/swagger'
import { FilterWithKeyValue } from '../types/filter-with-key-value.type.js'

export class FilterConfigurationResponse {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({
    oneOf: [
      { type: 'string' },
      { type: 'array', items: { type: 'string' } },
      { type: 'boolean' },
      { type: 'object', properties: { key: { type: 'string' }, value: { type: 'string' } } },
      { type: 'array', items: { type: 'object', properties: { key: { type: 'string' }, value: { type: 'string' } } } }
    ]
  })
  value: string | string[] | boolean | FilterWithKeyValue | FilterWithKeyValue[]

  constructor (
    uuid: string,
    value: string | string[] | boolean | FilterWithKeyValue | FilterWithKeyValue[]
  ) {
    this.uuid = uuid
    this.value = value
  }
}
