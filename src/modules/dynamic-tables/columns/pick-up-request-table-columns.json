{"columns": [{"name": "requestNumber", "applicableFields": ["requestNumber"], "searchableFields": [], "filterableField": "requestNumber", "sortableFields": ["requestNumber"], "isHidable": true}, {"name": "status", "applicableFields": ["status"], "searchableFields": [], "filterableField": "statuses", "sortableFields": [], "isHidable": true}, {"name": "wasteMaterial", "applicableFields": ["wasteMaterial"], "searchableFields": [], "filterableField": "wasteMaterial", "sortableFields": ["wasteMaterial"], "isHidable": true}, {"name": "customerId", "applicableFields": ["customerId"], "searchableFields": [], "filterableField": "customerId", "sortableFields": [], "isHidable": true}, {"name": "customerName", "applicableFields": ["customerName"], "searchableFields": [], "filterableField": null, "sortableFields": [], "isHidable": true}, {"name": "wasteProducerId", "applicableFields": ["wasteProducerId"], "searchableFields": [], "filterableField": "wasteProducerId", "sortableFields": ["wasteProducerId"], "isHidable": true}, {"name": "wasteProducerName", "applicableFields": ["wasteProducerName"], "searchableFields": [], "filterableField": null, "sortableFields": [], "isHidable": true}, {"name": "pickUpAddressId", "applicableFields": ["pickUpAddressId"], "searchableFields": [], "filterableField": "pickUpAddressId", "sortableFields": ["pickUpAddressId"], "isHidable": true}, {"name": "pickUpAddressName", "applicableFields": ["pickUpAddressName"], "searchableFields": [], "filterableField": null, "sortableFields": [], "isHidable": true}, {"name": "customerReference", "applicableFields": ["customerReference"], "searchableFields": [], "filterableField": "customerReference", "sortableFields": ["customerReference"], "isHidable": true}, {"name": "contractNumber", "applicableFields": ["contractNumber"], "searchableFields": [], "filterableField": "contractNumber", "sortableFields": ["contractNumber"], "isHidable": true}, {"name": "contractItem", "applicableFields": ["contractItem"], "searchableFields": [], "filterableField": "contractItem", "sortableFields": ["contractItem"], "isHidable": true}, {"name": "transportMode", "applicableFields": ["transportMode"], "searchableFields": [], "filterableField": "transportMode", "sortableFields": [], "isHidable": true}, {"name": "dateOfRequest", "applicableFields": ["dateOfRequest"], "searchableFields": [], "filterableField": "dateOfRequest", "sortableFields": ["dateOfRequest"], "isHidable": true}, {"name": "treatmentCenterName", "applicableFields": ["treatmentCenterName"], "searchableFields": [], "filterableField": "treatmentCenterName", "sortableFields": ["treatmentCenterName"], "isHidable": true}, {"name": "accountManager", "applicableFields": ["accountManager"], "searchableFields": [], "filterableField": "accountManager", "sortableFields": [], "isHidable": true}, {"name": "costCenter", "applicableFields": ["costCenter"], "searchableFields": [], "filterableField": "costCenter", "sortableFields": [], "isHidable": true}, {"name": "isTransportByIndaver", "applicableFields": ["isTransportByIndaver"], "searchableFields": [], "filterableField": "isTransportByIndaver", "sortableFields": [], "isHidable": true}, {"name": "requestedStartDate", "applicableFields": ["requestedStartDate"], "searchableFields": [], "filterableField": "requestDate", "sortableFields": ["requestedStartDate"], "isHidable": true}, {"name": "requestedEndDate", "applicableFields": ["requestedEndDate"], "searchableFields": [], "filterableField": "requestDate", "sortableFields": [], "isHidable": true}, {"name": "confirmedTransportDate", "applicableFields": ["confirmedTransportDate"], "searchableFields": [], "filterableField": "confirmedTransportDate", "sortableFields": ["confirmedTransportDate"], "isHidable": true}, {"name": "salesOrder", "applicableFields": ["salesOrder"], "searchableFields": [], "filterableField": "salesOrder", "sortableFields": ["salesOrder"], "isHidable": true}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "applicableFields": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "searchableFields": [], "filterableField": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sortableFields": [], "isHidable": true}, {"name": "nameOfApplicant", "applicableFields": ["nameOfApplicant"], "searchableFields": [], "filterableField": "nameOfApplicant", "sortableFields": [], "isHidable": true}, {"name": "orderNumber", "applicableFields": ["orderNumber"], "searchableFields": [], "filterableField": "orderNumber", "sortableFields": ["orderNumber"], "isHidable": true}, {"name": "containerNumber", "applicableFields": ["containerNumber"], "searchableFields": [], "filterableField": "containerNumber", "sortableFields": ["containerNumber"], "isHidable": true}, {"name": "materialAnalysis", "applicableFields": ["materialAnalysis"], "searchableFields": [], "filterableField": "materialAnalysis", "sortableFields": [], "isHidable": true}, {"name": "deliveryInfo", "applicableFields": ["deliveryInfo"], "searchableFields": [], "filterableField": "deliveryInfo", "sortableFields": [], "isHidable": true}, {"name": "nameInstallation", "applicableFields": ["nameInstallation"], "searchableFields": [], "filterableField": "nameInstallation", "sortableFields": ["nameInstallation"], "isHidable": true}, {"name": "disposalCertificateNumber", "applicableFields": ["disposalCertificateNumber"], "searchableFields": [], "filterableField": "disposalCertificateNumber", "sortableFields": ["disposalCertificateNumber"], "isHidable": true}, {"name": "ewc", "applicableFields": ["ewc"], "searchableFields": [], "filterableField": "ewc", "sortableFields": [], "isHidable": true}, {"name": "tfsNumber", "applicableFields": ["tfsNumber"], "searchableFields": [], "filterableField": null, "sortableFields": [], "isHidable": true}]}