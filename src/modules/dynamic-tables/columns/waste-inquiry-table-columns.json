{"columns": [{"name": "inquiryNumber", "applicableFields": ["inquiryNumber"], "searchableFields": [], "filterableField": "inquiryNumber", "sortableFields": ["inquiryNumber"], "isHidable": true}, {"name": "wasteStreamName", "applicableFields": ["wasteStreamName"], "searchableFields": [], "filterableField": "wasteStreamName", "sortableFields": ["wasteStreamName"], "isHidable": true}, {"name": "date", "applicableFields": ["date"], "searchableFields": [], "filterableField": "date", "sortableFields": ["date"], "isHidable": true}, {"name": "contractId", "applicableFields": ["contractId"], "searchableFields": [], "filterableField": "contractId", "sortableFields": ["contractId"], "isHidable": true}, {"name": "contractItem", "applicableFields": ["contractItem"], "searchableFields": [], "filterableField": "contractItem", "sortableFields": ["contractItem"], "isHidable": true}, {"name": "customerId", "applicableFields": ["customerId"], "searchableFields": [], "filterableField": "customerId", "sortableFields": [], "isHidable": true}, {"name": "customerName", "applicableFields": ["customerName"], "searchableFields": [], "filterableField": null, "sortableFields": ["customerName"], "isHidable": true}, {"name": "salesOrganisationId", "applicableFields": ["salesOrganisationId"], "searchableFields": [], "filterableField": "salesOrganisationId", "sortableFields": ["salesOrganisationId"], "isHidable": true}, {"name": "salesOrganisationName", "applicableFields": ["salesOrganisationName"], "searchableFields": [], "filterableField": null, "sortableFields": [], "isHidable": true}, {"name": "wasteProducerId", "applicableFields": ["wasteProducerId"], "searchableFields": [], "filterableField": "wasteProducerId", "sortableFields": ["wasteProducerId"], "isHidable": true}, {"name": "wasteProducerName", "applicableFields": ["wasteProducerName"], "searchableFields": [], "filterableField": null, "sortableFields": ["wasteProducerName"], "isHidable": true}, {"name": "pickUpAddressId", "applicableFields": ["pickUpAddressId"], "searchableFields": [], "filterableField": "pickUpAddressId", "sortableFields": ["pickUpAddressId"], "isHidable": true}, {"name": "pickUpAddressName", "applicableFields": ["pickUpAddressName"], "searchableFields": [], "filterableField": null, "sortableFields": [], "isHidable": true}, {"name": "requestorName", "applicableFields": ["requestorName"], "searchableFields": [], "filterableField": null, "sortableFields": [], "isHidable": true}, {"name": "status", "applicableFields": ["status"], "searchableFields": [], "filterableField": "statuses", "sortableFields": [], "isHidable": true}, {"name": "ewcCode", "applicableFields": ["ewcLevel1", "ewcLevel2", "ewcLevel3"], "searchableFields": [], "filterableField": "ewcCode", "sortableFields": [], "isHidable": true}, {"name": "typeOfRequest", "applicableFields": ["typeOfRequest"], "searchableFields": [], "filterableField": "typeOfRequest", "sortableFields": ["typeOfRequest"], "isHidable": true}]}