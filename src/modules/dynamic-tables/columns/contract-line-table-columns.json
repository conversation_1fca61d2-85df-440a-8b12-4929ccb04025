{"columns": [{"name": "contractNumber", "applicableFields": ["contractNumber"], "searchableFields": [], "filterableField": "contractNumber", "sortableFields": ["contractNumber"], "isHidable": true}, {"name": "contractItem", "applicableFields": ["contractItem"], "searchableFields": [], "filterableField": "contractItem", "sortableFields": ["contractItem"], "isHidable": true}, {"name": "customerReference", "applicableFields": ["customerReference"], "searchableFields": [], "filterableField": "customerReference", "sortableFields": [], "isHidable": true}, {"name": "wasteMaterial", "applicableFields": ["wasteMaterial"], "searchableFields": [], "filterableField": "wasteMaterial", "sortableFields": ["wasteMaterial"], "isHidable": true}, {"name": "materialNumber", "applicableFields": ["materialNumber"], "searchableFields": [], "filterableField": "materialNumber", "sortableFields": [], "isHidable": true}, {"name": "treatmentCenterName", "applicableFields": ["treatmentCenterName"], "searchableFields": [], "filterableField": "treatmentCenterName", "sortableFields": ["treatmentCenterName"], "isHidable": true}, {"name": "installationName", "applicableFields": ["installationName"], "searchableFields": [], "filterableField": "installationName", "sortableFields": ["installationName"], "isHidable": true}, {"name": "customerId", "applicableFields": ["customerId"], "searchableFields": [], "filterableField": null, "sortableFields": [], "isHidable": true}, {"name": "customerName", "applicableFields": ["customerName"], "searchableFields": [], "filterableField": null, "sortableFields": [], "isHidable": true}, {"name": "wasteProducerId", "applicableFields": ["wasteProducerId"], "searchableFields": [], "filterableField": null, "sortableFields": ["wasteProducerId"], "isHidable": true}, {"name": "wasteProducerName", "applicableFields": ["wasteProducerName"], "searchableFields": [], "filterableField": null, "sortableFields": ["wasteProducerName"], "isHidable": true}, {"name": "pickUpAddressId", "applicableFields": ["pickUpAddressId"], "searchableFields": [], "filterableField": null, "sortableFields": ["pickUpAddressId"], "isHidable": true}, {"name": "pickUpAddressName", "applicableFields": ["pickUpAddressName"], "searchableFields": [], "filterableField": null, "sortableFields": ["pickUpAddressName"], "isHidable": true}, {"name": "asn", "applicableFields": ["asn"], "searchableFields": [], "filterableField": "asn", "sortableFields": ["asn"], "isHidable": true}, {"name": "tfs", "applicableFields": ["tfs"], "searchableFields": [], "filterableField": "tfs", "sortableFields": [], "isHidable": true}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "applicableFields": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "searchableFields": [], "filterableField": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sortableFields": [], "isHidable": true}, {"name": "packaged", "applicableFields": ["packaged"], "searchableFields": [], "filterableField": null, "sortableFields": [], "isHidable": true}, {"name": "tcNumber", "applicableFields": ["tcNumber"], "searchableFields": [], "filterableField": "tcNumber", "sortableFields": ["tcNumber"], "isHidable": true}, {"name": "materialAnalysis", "applicableFields": ["materialAnalysis"], "searchableFields": [], "filterableField": "materialAnalysis", "sortableFields": [], "isHidable": true}, {"name": "ewcCode", "applicableFields": ["ewcCode"], "searchableFields": [], "filterableField": "ewcCode", "sortableFields": ["ewcCode"], "isHidable": true}, {"name": "endTreatmentCenterId", "applicableFields": ["endTreatmentCenterId"], "searchableFields": [], "filterableField": "endTreatmentCenterId", "sortableFields": ["endTreatmentCenterId"], "isHidable": true}, {"name": "endTreatmentCenterName", "applicableFields": ["endTreatmentCenterName"], "searchableFields": [], "filterableField": "endTreatmentCenterName", "sortableFields": ["endTreatmentCenterName"], "isHidable": true}, {"name": "remarks", "applicableFields": ["remarks"], "searchableFields": [], "filterableField": null, "sortableFields": [], "isHidable": true}, {"name": "processCode", "applicableFields": ["processCode"], "searchableFields": [], "filterableField": "processCode", "sortableFields": ["processCode"], "isHidable": true}, {"name": "esnNumber", "applicableFields": ["esnNumber"], "searchableFields": [], "filterableField": "esnNumber", "sortableFields": ["esnNumber"], "isHidable": true}, {"name": "deliveryInfo", "applicableFields": ["deliveryInfo"], "searchableFields": [], "filterableField": "deliveryInfo", "sortableFields": [], "isHidable": true}]}