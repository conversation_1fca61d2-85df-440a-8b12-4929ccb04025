import { afterEach, before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import { createStubInstance, SinonStubbedInstance } from 'sinon'
import { expect } from 'expect'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { ViewContainerTypeIndexValidator } from '../view-container-type-index.validator.js'
import { AuthContext } from '../../../../auth/auth.context.js'
import { UserCustomerAuthService } from '../../../../auth/services/user-customer-auth.service.js'
import { ViewContainerTypeIndexQuery } from '../query/view-container-type-index.query.js'
import { CustomerNotAccessibleError } from '../../../../customer/errors/customer-not-accessible.error.js'
import { ViewContainerTypeIndexQueryBuilder } from './view-container-type-index.query.builder.js'

describe('View container type index validator unit test', () => {
  let validator: ViewContainerTypeIndexValidator

  let query: ViewContainerTypeIndexQuery

  let authContext: SinonStubbedInstance<AuthContext>
  let userCustomerAuthService: SinonStubbedInstance<UserCustomerAuthService>

  before(() => {
    TestBench.setupUnitTest()

    query = new ViewContainerTypeIndexQueryBuilder().build()

    authContext = createStubInstance(AuthContext)
    userCustomerAuthService = createStubInstance(UserCustomerAuthService)

    validator = new ViewContainerTypeIndexValidator(
      authContext,
      userCustomerAuthService
    )
    mockMethods()
  })

  afterEach(() => {
    mockMethods()
  })

  function mockMethods () {
    authContext.getAzureEntraUpn.resolves(randomUUID())
    userCustomerAuthService.canUserAccessCustomer.resolves(true)
  }

  it('doesn\'t throw an error when validation passes', async () => {
    await expect(validator.execute(query)).resolves.not.toThrow()
  })

  it('throws an error when customer is not accessible by auth user', async () => {
    userCustomerAuthService.canUserAccessCustomer.resolves(false)

    await expect(validator.execute(query)).rejects.toThrow(CustomerNotAccessibleError)
  })
})
