import { Injectable } from '@nestjs/common'
import { AuthContext } from '../../../auth/auth.context.js'
import { UserCustomerAuthService } from '../../../auth/services/user-customer-auth.service.js'
import { CustomerNotAccessibleError } from '../../../customer/errors/customer-not-accessible.error.js'
import { ViewContainerTypeIndexQuery } from './query/view-container-type-index.query.js'

@Injectable()
export class ViewContainerTypeIndexValidator {
  constructor (
    private readonly authContext: AuthContext,
    private readonly userCustomerAuthService: UserCustomerAuthService
  ) {}

  async execute (query: ViewContainerTypeIndexQuery): Promise<void> {
    const isInternalUser = this.authContext.isInternalUser()

    if (isInternalUser) {
      return
    }

    const userId = this.authContext.getAzureEntraUpn()
    const canUserAccessCustomer = await this.userCustomerAuthService.canUserAccessCustomer(
      userId,
      query.filter.customerId
    )

    if (!canUserAccessCustomer) {
      throw new CustomerNotAccessibleError()
    }
  }
}
