/* eslint-disable @typescript-eslint/no-explicit-any */
import type { IncomingMessage } from 'http'
import { AsyncResource } from 'async_hooks'
import { Server } from 'http'
import { Injectable, UnauthorizedException, type INestApplicationContext } from '@nestjs/common'
import { WsAdapter } from '@nestjs/platform-ws'
import { WebSocketServer } from 'ws'
import { MessageMappingProperties } from '@nestjs/websockets'
import { filter, first, fromEvent, mergeMap, Observable, share, takeUntil } from 'rxjs'
import { CLOSE_EVENT } from '@nestjs/websockets/constants.js'
import { WebSocket } from 'ws'
import { isNil } from '@nestjs/common/utils/shared.utils.js'
import { AuthMiddleware } from '../auth/middleware/auth.middleware.js'
import { AuthContent, AuthContext } from '../auth/auth.context.js'
import { ImpersonationMiddleware } from '../auth/middleware/impersonation.middleware.js'
import { decodeImpersonationToken, ImpersonationTokenPayload } from '../../app/users/helpers/impersonation-token.helper.js'
import { SecurityLoggerService } from '../../utils/opentelemetry/modules/security-logger.service.js'

declare module 'http' {
  interface IncomingMessage {
    userUuid: string
  }
}

@Injectable()
export class AuthenticatedWsAdapter extends WsAdapter {
  private readonly authMiddleware: AuthMiddleware
  private readonly impersonationMiddleware: ImpersonationMiddleware
  private readonly authContext: AuthContext
  private readonly securityLogger: SecurityLoggerService

  constructor(appOrHttpServer: INestApplicationContext) {
    super(appOrHttpServer)

    this.authMiddleware = appOrHttpServer.get(AuthMiddleware)
    this.impersonationMiddleware = appOrHttpServer.get(ImpersonationMiddleware)
    this.authContext = appOrHttpServer.get(AuthContext)
    this.securityLogger = appOrHttpServer.get(SecurityLoggerService)
  }

  public override create(
    port: number,
    options?: Record<string, any> & {
      namespace?: string
      server?: unknown
      path?: string
    }
  ): any {
    const { server, path, ...wsOptions } = options ?? {}

    if (server != null) {
      return server
    }

    this.ensureHttpServerExists(
      port,
      this.httpServer as Server
    )

    const wss = this.bindErrorHandler(new WebSocketServer({
      noServer: true,
      ...wsOptions
    })) as WebSocketServer

    this.addWsServerToRegistry(wss, port, path ?? '/')

    wss.options.verifyClient = (
      info: { req: IncomingMessage },
      cb: (res: boolean, code?: number, message?: string, headers?: Record<string, string>) => void
    ): void => {
      const authToken = this.getParameter(info, 'authorization')

      if (authToken == null) {
        this.securityLogger.logWebSocketAuthFailure(
          info.req.socket.remoteAddress,
          info.req.headers['user-agent']
        )
        cb(false)
      } else {
        this.verifyAuthorization(authToken as string)
          .then((auth) => {
            this.authContext.run(auth, () => {
              const impersonationBase64Token = this.getParameter(info, 'x-impersonate-user')
              if (impersonationBase64Token !== null) {
                const impersonationToken = decodeImpersonationToken(
                  impersonationBase64Token as string
                )
                this.verifyImpersonation(impersonationToken).then((auth) => {
                  this.authContext.run(auth, () => {
                    cb(true)
                  })
                })
                  .catch((_e) => {
                    this.securityLogger.logWebSocketAuthFailure(
                      info.req.socket.remoteAddress,
                      info.req.headers['user-agent']
                    )
                    cb(false)
                  })
              }

              cb(true)
            })
          })
          .catch(() => {
            this.securityLogger.logWebSocketAuthFailure(
              info.req.socket.remoteAddress,
              info.req.headers['user-agent']
            )
            cb(false)
          })
      }
    }

    return wss
  }

  public override bindMessageHandlers(
    client: WebSocket,
    handlers: MessageMappingProperties[],
    transform: (data: any) => Observable<any>
  ) {
    const asyncResource = new AsyncResource('WebSocket')
    const handlersMap = new Map<string, MessageMappingProperties>()

    handlers.forEach(handler => handlersMap.set(handler.message as string, handler))

    const close$ = fromEvent(client, CLOSE_EVENT).pipe(share(), first())
    const source$ = fromEvent(client, 'message').pipe(
      mergeMap<{ data: string }, Observable<any>>(data =>
        // this.bindMessageHandler(data, handlersMap, transform).pipe(
        //   filter(result => !isNil(result))
        // )
        // eslint-disable-next-line @typescript-eslint/unbound-method
        asyncResource.runInAsyncScope(this.bindMessageHandler, this, data, handlersMap, transform)
          .pipe(filter(result => !isNil(result)))

      ),
      takeUntil(close$)
    )
    const onMessage = (response: any) => {
      if (client.readyState !== WebSocket.OPEN) {
        return
      }

      client.send(JSON.stringify(response))
    }

    source$.subscribe(onMessage)
  }

  private getParameter(info: { req: IncomingMessage }, name: string): string[] | string | null {
    const headerParam = info.req.headers?.[name]
    const queryParam = new URLSearchParams(info.req.url?.split('?')[1]?.toLowerCase()).get(name)

    return headerParam ?? queryParam
  }

  private async verifyAuthorization(header: string): Promise<AuthContent> {
    const [bearer, token] = header.split(' ')

    if (bearer !== 'Bearer' || token == null) {
      throw new UnauthorizedException()
    }

    const payload = await this.authMiddleware.verify(token)

    return {
      uuid: payload.uuid,
      zitadelSub: payload.zitadelSub,
      impersonateUserUuid: null,
      azureEntraId: payload.azureEntraId,
      impersonateAzureEntraId: null,
      azureEntraUpn: payload.azureEntraUpn,
      impersonateAzureEntraUpn: null,
      selectedCustomerId: payload.selectedCustomerId ?? null
    }
  }

  private async verifyImpersonation(
    impersonationToken: ImpersonationTokenPayload
  ): Promise<AuthContent> {
    const authContent = this.authContext.getAuthOrFail()

    await this.impersonationMiddleware.checkPermission(authContent.uuid)
    await this.impersonationMiddleware.checkImpersonateUserAccess(impersonationToken)

    authContent.impersonateUserUuid = impersonationToken.userUuid
    authContent.impersonateAzureEntraId = impersonationToken.azureEntraId
    authContent.impersonateAzureEntraUpn = impersonationToken.azureEntraUpn

    return authContent
  }
}
