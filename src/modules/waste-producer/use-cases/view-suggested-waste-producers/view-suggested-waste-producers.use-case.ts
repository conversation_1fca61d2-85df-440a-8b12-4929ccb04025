import { Injectable } from '@nestjs/common'
import { RequestType } from '../../../../utils/enums/request-type.enum.js'
import { exhaustiveCheck } from '../../../../utils/helpers/exhaustive-check.helper.js'
import { SapGetWasteProducerIndexResponse } from '../../../sap/use-cases/get-waste-producer-index/get-waste-producer-index.response.js'
import { SUGGESTED_WASTE_PRODUCERS_AMOUNT } from '../../../../utils/constants/suggested-entities-amount.constant.js'
import { WasteProducer } from '../../types/waste-producer.type.js'
import { MapWasteProducerSapService } from '../../services/map-waste-producer.service.js'
import { SapGetWasteProducerIndexUseCase } from '../../../sap/use-cases/get-waste-producer-index/get-waste-producer-index.use-case.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { GetCustomerDefaultSalesOrganisationIdUseCase } from '../../../customer/use-cases/get-customer-default-sales-organisation-id/get-customer-default-sales-organisation-id.use-case.js'
import { UserWasteProducerAuthService } from '../../../auth/services/user-waste-producer-auth.service.js'
import { SapQuery } from '../../../sap/query/sap-query.js'
import { FilterOperator } from '../../../sap/enums/odata-filter-operator.enum.js'
import { ViewSuggestedWasteProducersQuery } from './view-suggested-waste-producers.query.js'
import { ViewSuggestedWasteProducersResponse } from './view-suggested-waste-producers.response.js'
import { ViewSuggestedWasteProducersRepository } from './view-suggested-waste-producers.repository.js'
import { ViewSuggestedWasteProducersValidator } from './view-suggested-waste-producers.validator.js'

@Injectable()
export class ViewSuggestedWasteProducersUseCase {
  constructor (
    private readonly validator: ViewSuggestedWasteProducersValidator,
    private readonly authContext: AuthContext,
    private readonly getCustomerSalesOrganisationId: GetCustomerDefaultSalesOrganisationIdUseCase,
    private readonly userWasteProducerAuthService: UserWasteProducerAuthService,
    private readonly repository: ViewSuggestedWasteProducersRepository,
    private readonly sapGetWasteProducerIndex: SapGetWasteProducerIndexUseCase
  ) {}

  public async execute (
    query: ViewSuggestedWasteProducersQuery
  ): Promise<ViewSuggestedWasteProducersResponse> {
    this.validator.validate(query)

    const userUuid = this.authContext.getUserUuidOrFail()
    const userId = this.authContext.getAzureEntraUpn()
    const recentWasteProducerIds = await this.getRecentWasteProducerIds(
      userUuid,
      query.filter.requestType,
      query.filter.customerId
    )
    const customerDefaultSalesOrganisationId = await this.getCustomerSalesOrganisationId
      .getOrganisationIdOrFail(
        query.filter.customerId
      )
    const restrictedWasteProducerIds = await this.userWasteProducerAuthService
      .getRestrictedWasteProducerIds(
        userId,
        query.filter.customerId
      )

    let sapWasteProducers: SapGetWasteProducerIndexResponse[] = []

    if (recentWasteProducerIds.length > 0) {
      sapWasteProducers = await this.getSapWasteProducersByIds(
        query.filter.customerId,
        customerDefaultSalesOrganisationId,
        recentWasteProducerIds,
        restrictedWasteProducerIds
      )

      if (sapWasteProducers.length < SUGGESTED_WASTE_PRODUCERS_AMOUNT) {
        const sapExtraWasteProducers = await this.getSapWasteProducers(
          query.filter.customerId,
          customerDefaultSalesOrganisationId,
          SUGGESTED_WASTE_PRODUCERS_AMOUNT - sapWasteProducers.length,
          restrictedWasteProducerIds,
          recentWasteProducerIds
        )

        sapWasteProducers.push(...sapExtraWasteProducers)
      }
    } else {
      sapWasteProducers = await this.getSapWasteProducers(
        query.filter.customerId,
        customerDefaultSalesOrganisationId,
        SUGGESTED_WASTE_PRODUCERS_AMOUNT,
        restrictedWasteProducerIds
      )
    }

    const wasteProducers = this.mapWasteProducer(sapWasteProducers)

    return new ViewSuggestedWasteProducersResponse(
      wasteProducers
    )
  }

  private async getRecentWasteProducerIds (
    userUuid: string,
    requestType: RequestType,
    customerId: string
  ): Promise<string[]> {
    switch (requestType) {
      case RequestType.WASTE:
        return this.repository.findRecentWasteInquiryWasteProducerIds(userUuid, customerId)
      case RequestType.PICK_UP:
        return this.repository.findRecentPickUpRequestWasteProducerIds(userUuid, customerId)
      default:
        exhaustiveCheck(requestType)
    }
  }

  private async getSapWasteProducersByIds (
    customerId: string,
    customerDefaultSalesOrganisationId: string,
    wasteProducerIds: string[],
    restrictedWasteProducerIds?: string[]
  ): Promise<SapGetWasteProducerIndexResponse[]> {
    if (wasteProducerIds.length === 0) return []

    const sapQuery = new SapQuery<SapGetWasteProducerIndexResponse>()
      .where('Customer', customerId)
      .andWhere('SalesOrganization', customerDefaultSalesOrganisationId)
      .andWhere((qb) => {
        qb.where('WasteProducer', wasteProducerIds[0])
        for (let i = 1; i < wasteProducerIds.length; i++) {
          qb.orWhere('WasteProducer', wasteProducerIds[i])
        }
        return qb
      })

    if (restrictedWasteProducerIds !== undefined && restrictedWasteProducerIds.length > 0) {
      sapQuery.andWhere((qb) => {
        qb.where('WasteProducer', restrictedWasteProducerIds[0])
        for (let i = 1; i < restrictedWasteProducerIds.length; i++) {
          qb.orWhere('WasteProducer', restrictedWasteProducerIds[i])
        }
        return qb
      })
    }

    const sapResponse = await this.sapGetWasteProducerIndex.execute(sapQuery)

    return sapResponse.items
  }

  private async getSapWasteProducers (
    customerId: string,
    customerDefaultSalesOrganisationId: string,
    amount: number,
    restrictedWasteProducerIds?: string[],
    excludeWasteProducerIds?: string[]
  ): Promise<SapGetWasteProducerIndexResponse[]> {
    const sapQuery = new SapQuery<SapGetWasteProducerIndexResponse>()
      .where('Customer', customerId)
      .andWhere('SalesOrganization', customerDefaultSalesOrganisationId)

    if (restrictedWasteProducerIds !== undefined && restrictedWasteProducerIds.length > 0) {
      sapQuery.andWhere((qb) => {
        qb.where('WasteProducer', restrictedWasteProducerIds[0])
        for (let i = 1; i < restrictedWasteProducerIds.length; i++) {
          qb.orWhere('WasteProducer', restrictedWasteProducerIds[i])
        }
        return qb
      })
    }

    if (excludeWasteProducerIds !== undefined && excludeWasteProducerIds.length > 0) {
      sapQuery.andWhere((qb) => {
        qb.where('WasteProducer', excludeWasteProducerIds[0], FilterOperator.NOT_EQUAL)
        for (let i = 1; i < excludeWasteProducerIds.length; i++) {
          qb.andWhere('WasteProducer', excludeWasteProducerIds[i], FilterOperator.NOT_EQUAL)
        }
        return qb
      })
    }

    sapQuery.addOrderBy('WasteProducerName', 'asc')
    sapQuery.setTop(amount)

    const sapResponse = await this.sapGetWasteProducerIndex.execute(sapQuery)

    return sapResponse.items
  }

  private mapWasteProducer (
    wasteProducers: SapGetWasteProducerIndexResponse[]
  ): WasteProducer[] {
    return MapWasteProducerSapService.mapResultsToWasteProducers(wasteProducers)
  }
}
