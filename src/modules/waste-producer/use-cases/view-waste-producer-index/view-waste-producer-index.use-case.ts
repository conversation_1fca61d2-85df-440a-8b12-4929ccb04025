import { Injectable } from '@nestjs/common'
import { typeormPagination } from '@wisemen/pagination'
import { SapGetWasteProducerIndexResponse } from '../../../sap/use-cases/get-waste-producer-index/get-waste-producer-index.response.js'
import { SapGetWasteProducerIndexUseCase } from '../../../sap/use-cases/get-waste-producer-index/get-waste-producer-index.use-case.js'
import { MapWasteProducerSapService } from '../../services/map-waste-producer.service.js'
import { GetCustomerDefaultSalesOrganisationIdUseCase } from '../../../customer/use-cases/get-customer-default-sales-organisation-id/get-customer-default-sales-organisation-id.use-case.js'
import { SapQuery } from '../../../sap/query/sap-query.js'
import { UserWasteProducerAuthService } from '../../../auth/services/user-waste-producer-auth.service.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { ViewWasteProducerIndexResponse } from './view-waste-producer-index.response.js'
import { ViewWasteProducerIndexQuery } from './query/view-waste-producer-index.query.js'
import { ViewWasteProducerIndexValidator } from './view-waste-producer-index.validator.js'

@Injectable()
export class ViewWasteProducerIndexUseCase {
  constructor (
    private readonly validator: ViewWasteProducerIndexValidator,
    private readonly authContext: AuthContext,
    private readonly getCustomerSalesOrganisationId: GetCustomerDefaultSalesOrganisationIdUseCase,
    private readonly userWasteProducerAuthService: UserWasteProducerAuthService,
    private readonly sapGetWasteProducerIndex: SapGetWasteProducerIndexUseCase
  ) {}

  public async execute (
    query: ViewWasteProducerIndexQuery
  ): Promise<ViewWasteProducerIndexResponse> {
    this.validator.execute(query)

    const sapQuery = await this.getSapQuery(query)
    const sapResponse = await this.sapGetWasteProducerIndex.execute(sapQuery)

    const wasteProducers = MapWasteProducerSapService.mapResultsToWasteProducers(sapResponse.items)

    const pagination = typeormPagination(query.pagination)
    return new ViewWasteProducerIndexResponse(
      wasteProducers,
      null,
      pagination.take,
      pagination.skip
    )
  }

  private async getSapQuery (
    query: ViewWasteProducerIndexQuery
  ): Promise<SapQuery<SapGetWasteProducerIndexResponse>> {
    const filterCustomerId = query.filter?.customerId ?? this.authContext.getSelectedCustomerId()

    const sapQuery = new SapQuery<SapGetWasteProducerIndexResponse>(query)

    if (filterCustomerId != null) {
      const [customerDefaultSalesOrganisationId, restrictedWasteProducerIds] = await Promise.all([
        this.getCustomerSalesOrganisationId
          .getOrganisationIdOrFail(
            filterCustomerId
          ),
        this.userWasteProducerAuthService
          .getRestrictedWasteProducerIds(
            this.authContext.getAzureEntraUpn(),
            filterCustomerId
          )
      ])

      sapQuery
        .where('Customer', filterCustomerId)
        .andWhere('SalesOrganization', customerDefaultSalesOrganisationId)

      if (restrictedWasteProducerIds !== undefined && restrictedWasteProducerIds.length > 0) {
        sapQuery.andWhere((qb) => {
          qb.where('WasteProducer', restrictedWasteProducerIds[0])
          for (let i = 1; i < restrictedWasteProducerIds.length; i++) {
            qb.orWhere('WasteProducer', restrictedWasteProducerIds[i])
          }
          return qb
        })
      }
    }

    return sapQuery
  }
}
