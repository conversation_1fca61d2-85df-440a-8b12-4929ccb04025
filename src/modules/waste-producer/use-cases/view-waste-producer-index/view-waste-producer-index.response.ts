import { ApiProperty } from '@nestjs/swagger'
import { PaginatedOffsetResponse } from '@wisemen/pagination'
import { WasteProducerResponse } from '../../responses/waste-producer.response.js'
import { WasteProducer } from '../../types/waste-producer.type.js'
import { calculatePageSize } from '../../../../utils/helpers/unknown-page-size.helper.js'

export class ViewWasteProducerIndexResponse extends PaginatedOffsetResponse<WasteProducerResponse> {
  @ApiProperty({ type: WasteProducerResponse, isArray: true })
  declare items: WasteProducerResponse[]

  constructor (items: WasteProducer[], total: number | null, limit: number, offset: number) {
    const result = items.map(contact => new WasteProducerResponse(contact))

    if (total === null) {
      total = calculatePageSize(items, offset, limit)
    }

    super(result, total, limit, offset)
  }
}
