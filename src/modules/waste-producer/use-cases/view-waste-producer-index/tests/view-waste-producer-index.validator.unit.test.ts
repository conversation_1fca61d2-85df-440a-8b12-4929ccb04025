import { afterEach, before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import { SinonStubbedInstance, createStubInstance } from 'sinon'
import { expect } from 'expect'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { ViewWasteProducerIndexValidator } from '../view-waste-producer-index.validator.js'
import { ViewWasteProducerIndexQuery } from '../query/view-waste-producer-index.query.js'
import { AuthContext } from '../../../../auth/auth.context.js'
import { SelectedCustomerFilterMismatchError } from '../../../../customer/errors/selected-customer-filter-mismatch.error.js'
import { ViewWasteProducerIndexQueryBuilder } from './view-waste-producer-index.query.builder.js'
import { ViewWasteProducerIndexFilterQueryBuilder } from './view-waste-producer-index.filter.builder.js'

describe('View waste producer index validator unit test', () => {
  let validator: ViewWasteProducerIndexValidator

  let filterCustomerId: string
  let query: ViewWasteProducerIndexQuery

  let authContext: SinonStubbedInstance<AuthContext>

  before(() => {
    TestBench.setupUnitTest()

    filterCustomerId = randomUUID()
    query = new ViewWasteProducerIndexQueryBuilder()
      .withFilter(
        new ViewWasteProducerIndexFilterQueryBuilder()
          .withCustomerId(filterCustomerId)
          .build()
      )
      .build()

    authContext = createStubInstance(AuthContext)

    validator = new ViewWasteProducerIndexValidator(
      authContext
    )

    mockMethods()
  })

  afterEach(() => {
    mockMethods()
  })

  function mockMethods () {
    authContext.getAzureEntraUpn.resolves(randomUUID())
    authContext.getSelectedCustomerId.returns(filterCustomerId)
  }

  it('doesn\'t throw an error when validation passes', () => {
    expect(() => validator.execute(query)).not.toThrow()
  })

  it('throws an error when customer is different from selected customer', () => {
    authContext.getSelectedCustomerId.returns(randomUUID())

    expect(() => validator.execute(query)).toThrow(SelectedCustomerFilterMismatchError)
  })
})
