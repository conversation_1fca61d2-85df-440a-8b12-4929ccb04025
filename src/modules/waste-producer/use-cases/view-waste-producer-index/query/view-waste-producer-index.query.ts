import { Equals, IsNotEmpty, <PERSON><PERSON><PERSON>al, IsString, ValidateNested } from 'class-validator'
import { ApiProperty } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { FilterQuery, PaginatedOffsetSearchQuery } from '@wisemen/pagination'
import { IsUndefinable } from '@wisemen/validators'

export class ViewWasteProducerIndexFilterQuery extends FilterQuery {
  @ApiProperty({ type: String, required: false, description: 'Optional for internal users' })
  @IsUndefinable()
  @IsString()
  @IsNotEmpty()
  customerId?: string
}

export class ViewWasteProducerIndexQuery extends PaginatedOffsetSearchQuery {
  @Equals(undefined)
  sort?: never

  @ApiProperty({ type: ViewWasteProducerIndexFilterQuery, required: false })
  @IsUndefinable()
  @Type(() => ViewWasteProducerIndexFilterQuery)
  @ValidateNested()
  @IsNotEmpty()
  filter?: ViewWasteProducerIndexFilterQuery

  @ApiProperty({ type: String, required: false })
  @IsOptional()
  search?: string
}
