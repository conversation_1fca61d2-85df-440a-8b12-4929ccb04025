import { DEFAULT_SHAREPOINT_SITE_TITLE } from '../constants/sharepoint-site-title.constant.js'
import { SharepointCommand, SharepointMetadataItemCommand as SharepointMetadataItemCommand, SharepointMetadataItemName } from '../types/sharepoint.command.js'
import { SharepointMetaDataItemCommandBuilder } from './sharepoint-metadata-item.command.builder.js'

export class SharepointCommandBuilder {
  private command: SharepointCommand

  constructor () {
    this.command = {
      metaData: []
    }
  }

  withMetaData (metaData: SharepointMetadataItemCommand[]): this {
    this.command.metaData = metaData
    return this
  }

  addMetaData (metaData: SharepointMetadataItemCommand): this {
    this.command.metaData.push(metaData)

    return this
  }

  addSiteTitle (siteTitle: typeof DEFAULT_SHAREPOINT_SITE_TITLE): this {
    const metadata = new SharepointMetaDataItemCommandBuilder()
      .withName(SharepointMetadataItemName.SITE_TITLE)
      .withValue(siteTitle)
      .build()
    this.command.metaData.push(metadata)
    return this
  }

  addLibraryName (libraryName: string): this {
    const metadata = new SharepointMetaDataItemCommandBuilder()
      .withName(SharepointMetadataItemName.LIBRARY_NAME)
      .withValue(libraryName)
      .build()
    this.command.metaData.push(metadata)
    return this
  }

  addFileName (fileName: string): this {
    const metadata = new SharepointMetaDataItemCommandBuilder()
      .withName(SharepointMetadataItemName.FILE_NAME)
      .withValue(fileName)
      .build()
    this.command.metaData.push(metadata)
    return this
  }

  withSource (source: string): this {
    this.command.source = source
    return this
  }

  build (): SharepointCommand {
    return this.command
  }
}
