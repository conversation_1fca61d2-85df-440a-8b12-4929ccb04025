import { SharepointMetadataItemCommand, SharepointMetadataItemName } from '../types/sharepoint.command.js'

export class SharepointMetaDataItemCommandBuilder {
  private command: SharepointMetadataItemCommand

  constructor () {
    this.command = {
      name: SharepointMetadataItemName.FILE_NAME,
      value: 'any'
    }
  }

  withName (name: SharepointMetadataItemName): this {
    this.command.name = name
    return this
  }

  withValue (value: string): this {
    this.command.value = value

    return this
  }

  build (): SharepointMetadataItemCommand {
    return this.command
  }
}
