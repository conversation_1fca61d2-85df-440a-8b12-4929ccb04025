import { EnvType } from '../../config/env.enum.js'
import { SharepointLibrarySuffix } from '../enums/sharepoint-library-suffix.enum.js'
import { exhaustiveCheck } from '../../../utils/helpers/exhaustive-check.helper.js'

export class SharepointLibraryNameEnvMapper {
  static toSharepointLibraryName (libraryName: string) {
    return `${libraryName}${this.getSuffix()}`
  }

  static getSuffix (): SharepointLibrarySuffix {
    const env = process.env.NODE_ENV as EnvType | undefined ?? EnvType.LOCAL

    switch (env) {
      case EnvType.LOCAL:
      case EnvType.DEVELOPMENT:
      case EnvType.TEST:
      case EnvType.QA: return SharepointLibrarySuffix.DS4
      case EnvType.STAGING: return SharepointLibrarySuffix.QS4
      case EnvType.PRODUCTION: return SharepointLibrarySuffix.PS4
      default:
        exhaustiveCheck(env)
    }
  }
}
