import { Module } from '@nestjs/common'
import { GetUserSiteIndexModule } from './use-cases/get-user-site-index/get-user-site-index.module.js'
import { GetDocumentIndexModule } from './use-cases/get-document-index/get-document-index.module.js'
import { MtwmSharepointDownloadDocumentModule } from './use-cases/download-mtwm-document/download-mtwm-document.module.js'
import { GetDocumentFiltersModule } from './use-cases/get-document-filters/get-document-filters.module.js'
import { SharepointDownloadDocumentModule } from './use-cases/download-document/download-document.module.js'
import { SharepointUploadDocumentModule } from './use-cases/upload-document/upload-document.module.js'

@Module({
  imports: [
    GetDocumentFiltersModule,
    GetDocumentIndexModule,
    GetUserSiteIndexModule,
    MtwmSharepointDownloadDocumentModule,
    SharepointDownloadDocumentModule,
    SharepointUploadDocumentModule
  ],
  exports: [
    GetDocumentFiltersModule,
    GetDocumentIndexModule,
    GetUserSiteIndexModule,
    MtwmSharepointDownloadDocumentModule,
    SharepointDownloadDocumentModule,
    SharepointUploadDocumentModule
  ]
})
export class SharepointModule {}
