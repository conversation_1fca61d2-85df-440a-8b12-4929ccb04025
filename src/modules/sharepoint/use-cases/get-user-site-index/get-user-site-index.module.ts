import { Module } from '@nestjs/common'
import { SharepointCacheModule } from '../../cache/sharepoint.cache.module.js'
import { MtwmSharepointClientModule } from '../../clients/mtwm/mwtm-sharepoint-client.module.js'
import { GetUserSiteIndexUseCase } from './get-user-site-index.use-case.js'

@Module({
  imports: [MtwmSharepointClientModule, SharepointCacheModule],
  providers: [GetUserSiteIndexUseCase],
  exports: [GetUserSiteIndexUseCase]
})
export class GetUserSiteIndexModule {}
