import { Injectable } from '@nestjs/common'
import { MTWM_SHAREPOINT_ENDPOINTS } from '../../constants/mwtm-endpoints.constant.js'
import { SharepointCache } from '../../cache/sharepoint.cache.js'
import { SharepointCall } from '../../enums/sharepoint-call.enum.js'
import { MwtmSharepointClient } from '../../clients/mtwm/mwtm-sharepoint.client.js'
import { GetUserSiteIndexResponse } from './get-user-site-index.response.js'

@Injectable()
export class GetUserSiteIndexUseCase {
  constructor (
    private readonly sharepointClient: MwtmSharepointClient,
    private readonly sharepointCache: SharepointCache
  ) {}

  async execute (azureEntraUpn: string): Promise<GetUserSiteIndexResponse[]> {
    const call = SharepointCall.GET_USER_SITES

    const cacheResult = await this.sharepointCache.getResponse<GetUserSiteIndexResponse[]>(
      call,
      azureEntraUpn
    )

    if (cacheResult !== null) {
      return cacheResult
    }

    const response = await this.sharepointClient.client
      .get<GetUserSiteIndexResponse[]>(MTWM_SHAREPOINT_ENDPOINTS.SITES.INDEX, {
        params: {
          UserId: azureEntraUpn
        }
      })

    await this.sharepointCache.putResponse(response.data, call, azureEntraUpn)

    return response.data
  }
}
