import { SharepointDocumentStatus } from '../../../enums/sharepoint-document-status.enum.js'
import { SharepointDocumentViewName } from '../../../enums/sharepoint-document-view-name.enum.js'
import { GetDocumentIndexFileResponse } from '../get-document-index.response.js'

export class GetDocumentIndexFileResponseBuilder {
  private response: GetDocumentIndexFileResponse

  constructor () {
    this.reset()
  }

  private reset (): this {
    this.response = {
      id: '55',
      contentType: 'TFS document',
      viewName: SharepointDocumentViewName.TFS,
      name: 'BE0001013537_Goedk_Ovam.pdf',
      downloadUrl: null,
      customerName: 'WP 2',
      actionDate: null,
      applicapleFrom: '2025-01-13T23:00:00Z',
      applicapleUntil: '2025-01-14T12:28:09Z',
      typeTFS: '2',
      refExt: 'BE0001013537',
      typeTransportDoc: '',
      status: SharepointDocumentStatus.ACTIVE
    }

    return this
  }

  withCustomerName (customerName: string): this {
    this.response.customerName = customerName
    return this
  }

  public build (): GetDocumentIndexFileResponse {
    const result = this.response

    this.reset()

    return result
  }
}
