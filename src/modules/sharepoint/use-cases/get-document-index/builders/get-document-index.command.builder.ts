import { GetDocumentIndexCommand } from '../get-document-index.command.js'

export class GetDocumentIndexCommandBuilder {
  private command: GetDocumentIndexCommand

  constructor () {
    this.reset()

    this.command.nextLink = ''
    this.command.siteId = ''
    this.command.publishedDocumentsLibraryId = ''
    this.command.wasteProducerIds = []
    this.command.viewName = ''
    this.command.textSearchFilter = ''
    this.command.yearFilter = ''
    this.command.statusFilter = ''
    this.command.typeTransportDocFilter = ''
    this.command.refextFilter = ''
    this.command.webUrl = ''
    this.command.itemIdFilter = undefined
  }

  private reset (): this {
    this.command = new GetDocumentIndexCommand()
    return this
  }

  withNextLink (nextLink: string): this {
    this.command.nextLink = nextLink
    return this
  }

  withSiteId (siteId: string): this {
    this.command.siteId = siteId
    return this
  }

  withPublishedDocumentsLibraryId (publishedDocumentsLibraryId: string): this {
    this.command.publishedDocumentsLibraryId = publishedDocumentsLibraryId
    return this
  }

  withWasteProducerIds (wasteProducerIds: number[]): this {
    this.command.wasteProducerIds = wasteProducerIds
    return this
  }

  withViewName (viewName: string): this {
    this.command.viewName = viewName
    return this
  }

  withTextSearchFilter (textSearchFilter: string): this {
    this.command.textSearchFilter = textSearchFilter
    return this
  }

  withYearFilter (yearFilter: string): this {
    this.command.yearFilter = yearFilter
    return this
  }

  withStatusFilter (statusFilter: string): this {
    this.command.statusFilter = statusFilter
    return this
  }

  withTypeTransportDocFilter (typeTransportDocFilter: string): this {
    this.command.typeTransportDocFilter = typeTransportDocFilter
    return this
  }

  withRefextFilter (refextFilter: string): this {
    this.command.refextFilter = refextFilter
    return this
  }

  withWebUrl (webUrl: string): this {
    this.command.webUrl = webUrl
    return this
  }

  public build (): GetDocumentIndexCommand {
    const result = this.command
    this.reset()
    return result
  }
}
