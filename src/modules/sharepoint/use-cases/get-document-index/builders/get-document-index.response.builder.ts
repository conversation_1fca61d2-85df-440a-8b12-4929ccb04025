import { GetDocumentIndexFileResponse, GetDocumentIndexResponse } from '../get-document-index.response.js'
import { GetDocumentIndexFileResponseBuilder } from './get-document-index-file.response.builder.js'

export class GetDocumentIndexResponseBuilder {
  private response: GetDocumentIndexResponse

  constructor () {
    this.reset()
  }

  private reset (): this {
    this.response = {
      oDataNextLink: '',
      files: [new GetDocumentIndexFileResponseBuilder().build()]
    }

    return this
  }

  withFiles (files: GetDocumentIndexFileResponse[]): this {
    this.response.files = files
    return this
  }

  public build (): GetDocumentIndexResponse {
    const result = this.response

    this.reset()

    return result
  }
}
