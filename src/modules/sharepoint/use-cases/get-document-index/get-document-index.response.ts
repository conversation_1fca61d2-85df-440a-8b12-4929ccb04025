import { SharepointDocumentStatus } from '../../enums/sharepoint-document-status.enum.js'
import { SharepointDocumentViewName } from '../../enums/sharepoint-document-view-name.enum.js'

export interface GetDocumentIndexFileResponse {
  id: string
  contentType: string
  viewName: SharepointDocumentViewName
  name: string
  downloadUrl: string | null
  customerName: string
  actionDate: string | null
  applicapleFrom: string
  applicapleUntil: string
  typeTFS: string
  refExt: string
  typeTransportDoc: string
  status: SharepointDocumentStatus | ''
}

export interface GetDocumentIndexResponse {
  oDataNextLink: string
  files: GetDocumentIndexFileResponse[]
}
