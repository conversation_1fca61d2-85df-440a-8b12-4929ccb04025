import { Injectable } from '@nestjs/common'
import { MTWM_SHAREPOINT_ENDPOINTS } from '../../constants/mwtm-endpoints.constant.js'
import { MwtmSharepointClient } from '../../clients/mtwm/mwtm-sharepoint.client.js'
import { GetDocumentIndexResponse } from './get-document-index.response.js'
import { GetDocumentIndexCommand } from './get-document-index.command.js'

@Injectable()
export class GetDocumentIndexUseCase {
  constructor (
    private readonly sharepointClient: MwtmSharepointClient
  ) {}

  async execute (
    command: GetDocumentIndexCommand
  ): Promise<GetDocumentIndexResponse> {
    const url = MTWM_SHAREPOINT_ENDPOINTS.DOCUMENTS.INDEX

    const response = await this.sharepointClient.client.post<GetDocumentIndexResponse>(url, command)

    return response.data
  }
}
