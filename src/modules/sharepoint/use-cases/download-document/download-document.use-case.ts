import assert from 'assert'
import { Injectable } from '@nestjs/common'
import { SharepointClient } from '../../clients/sharepoint.client.js'
import { SharepointCommand, SharepointMetadataItemName } from '../../types/sharepoint.command.js'
import { SHAREPOINT_ENDPOINTS } from '../../constants/endpoints.constant.js'
import { MimeType } from '../../../files/enums/mime-type.enum.js'
import { DownloadDocumentUseCaseResponse } from './download-document-use-case.response.js'

@Injectable()
export class SharepointDownloadDocumentUseCase {
  constructor (
    private readonly client: SharepointClient
  ) {}

  async execute (command: SharepointCommand): Promise<DownloadDocumentUseCaseResponse> {
    const url = SHAREPOINT_ENDPOINTS.DOWNLOAD

    const response = await this.client.client.post<ArrayBuffer>(url, command, {
      responseType: 'arraybuffer'
    })

    const rawContentType = response.headers['content-type'] as unknown

    const mimeType = typeof rawContentType === 'string' && Object.values(MimeType).includes(rawContentType as MimeType)
      ? rawContentType as MimeType
      : MimeType.OCTET_STREAM

    const fileName = command.metaData
      .filter(data => data.name === SharepointMetadataItemName.FILE_NAME)
      .at(0)?.value

    assert(fileName !== undefined, 'no file name given')

    return {
      buffer: Buffer.from(response.data),
      mimeType,
      fileName
    }
  }
}
