import { randomUUID } from 'crypto'
import { MwtmSharepointDownloadDocumentCommand } from '../../download-mtwm-document.command.js'

export class SharepointDownloadDocumentCommandBuilder {
  private command: MwtmSharepointDownloadDocumentCommand

  constructor () {
    this.reset()
  }

  private reset (): this {
    this.command = {
      currentListId: randomUUID(),
      listItemId: randomUUID(),
      siteId: randomUUID()
    }

    return this
  }

  withCurrentListId (currentListId: string): this {
    this.command.currentListId = currentListId

    return this
  }

  withListItemId (listItemId: string): this {
    this.command.listItemId = listItemId

    return this
  }

  withSiteId (siteId: string): this {
    this.command.siteId = siteId

    return this
  }

  public build (): MwtmSharepointDownloadDocumentCommand {
    const result = this.command

    this.reset()

    return result
  }
}
