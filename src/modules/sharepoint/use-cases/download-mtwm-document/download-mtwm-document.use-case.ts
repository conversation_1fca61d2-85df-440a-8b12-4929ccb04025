import { Injectable } from '@nestjs/common'
import { MTWM_SHAREPOINT_ENDPOINTS } from '../../constants/mwtm-endpoints.constant.js'
import { MwtmSharepointClient } from '../../clients/mtwm/mwtm-sharepoint.client.js'
import { MwtmSharepointDownloadDocumentCommand } from './download-mtwm-document.command.js'

@Injectable()
export class MwtmSharepointDownloadDocumentUseCase {
  constructor (
    private readonly sharepointClient: MwtmSharepointClient
  ) {}

  async execute (command: MwtmSharepointDownloadDocumentCommand): Promise<string> {
    const url = MTWM_SHAREPOINT_ENDPOINTS.DOCUMENTS.DOWNLOAD
    const response = await this.sharepointClient.client.post(url, command)

    return response.data as string
  }
}
