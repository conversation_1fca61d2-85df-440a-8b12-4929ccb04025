import { Module } from '@nestjs/common'
import { SharepointCacheModule } from '../../cache/sharepoint.cache.module.js'
import { MtwmSharepointClientModule } from '../../clients/mtwm/mwtm-sharepoint-client.module.js'
import { MwtmSharepointGetDocumentFiltersUseCase } from './get-document-filters.use-case.js'

@Module({
  imports: [MtwmSharepointClientModule, SharepointCacheModule],
  providers: [MwtmSharepointGetDocumentFiltersUseCase],
  exports: [MwtmSharepointGetDocumentFiltersUseCase]
})
export class GetDocumentFiltersModule {}
