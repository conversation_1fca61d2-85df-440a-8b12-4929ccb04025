import { Injectable } from '@nestjs/common'
import { MTWM_SHAREPOINT_ENDPOINTS } from '../../constants/mwtm-endpoints.constant.js'
import { SharepointCall } from '../../enums/sharepoint-call.enum.js'
import { SharepointCache } from '../../cache/sharepoint.cache.js'
import { MwtmSharepointClient } from '../../clients/mtwm/mwtm-sharepoint.client.js'
import { SharepointGetDocumentFiltersResponse } from './get-document-filters.response.js'
import { GetDocumentFiltersQuery as GetDocumentFiltersCommand } from './get-document-filters.command.js'

@Injectable()
export class MwtmSharepointGetDocumentFiltersUseCase {
  constructor (
    private readonly sharepointClient: MwtmSharepointClient,
    private readonly sharepointCache: SharepointCache
  ) {}

  async execute (
    command: GetDocumentFiltersCommand
  ): Promise<SharepointGetDocumentFiltersResponse> {
    const call = SharepointCall.GET_DOCUMENT_FILTERS

    const cacheResult = await this.sharepointCache.getResponse<
      SharepointGetDocumentFiltersResponse
    >(
      call,
      this.getCacheIdentifier(command)
    )

    if (cacheResult !== null) {
      return cacheResult
    }

    const response = await this.sharepointClient.client
      .post<SharepointGetDocumentFiltersResponse>(
        MTWM_SHAREPOINT_ENDPOINTS.AVAILABLE_FILTERS.INDEX, command
      )

    await this.sharepointCache.putResponse(response.data, call, this.getCacheIdentifier(command))

    return response.data
  }

  private getCacheIdentifier (
    command: GetDocumentFiltersCommand
  ): string {
    const jsonString = JSON.stringify(command)
    return Buffer.from(jsonString).toString('base64')
  }
}
