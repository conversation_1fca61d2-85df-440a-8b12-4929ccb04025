import { Injectable } from '@nestjs/common'
import { SharepointClient } from '../../clients/sharepoint.client.js'
import { SharepointCommand } from '../../types/sharepoint.command.js'
import { SHAREPOINT_ENDPOINTS } from '../../constants/endpoints.constant.js'
import { SharepointUploadDocumentResponse } from './upload-document.response.js'

@Injectable()
export class SharepointUploadDocumentUseCase {
  constructor (
    private readonly client: SharepointClient
  ) {}

  async execute (command: SharepointCommand): Promise<SharepointUploadDocumentResponse> {
    const url = SHAREPOINT_ENDPOINTS.UPLOAD
    return await this.client.client.post(url, command)
  }
}
