import { DEFAULT_SHAREPOINT_SITE_TITLE } from '../../constants/sharepoint-site-title.constant.js'
import { SharepointUploadDocumentResponse } from './upload-document.response.js'

export class SharepointUploadDocumentResponseBuilder {
  private response: SharepointUploadDocumentResponse

  constructor () {
    this.response = {
      'metas': {
        filename: 'something.pdf',
        libraryName: 'library',
        siteTitle: DEFAULT_SHAREPOINT_SITE_TITLE
      },
      'sp-id': 0
    }
  }

  build (): SharepointUploadDocumentResponse {
    return this.response
  }
}
