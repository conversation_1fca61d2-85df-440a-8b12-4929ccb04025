import { Injectable } from '@nestjs/common'
import axios, { AxiosError, AxiosInstance, AxiosResponse } from 'axios'
import { ConfigService } from '@nestjs/config'
import dayjs from 'dayjs'
import QueryString from 'qs'
import { captureException } from '@sentry/nestjs'
import { OpenTelemetryLoggerService, LogContext } from '../../../../utils/opentelemetry/modules/logger.service.js'
import { isProdEnv } from '../../../config/env.checker.js'
import { Oauth2CommandBuilder } from '../../builders/oauth2.command.builder.js'
import { Oauth2TokenResponse } from '../../types/oauth2-token.response.js'
import { Oauth2Command, CERTIFICATE_CLIENT_ASSERTION_TYPE } from '../../types/oauth2.command.js'
import { MwtmSharepointClientOauthAssertionGenerator } from './sharepoint-client-oauth-assertion.generator.js'

@Injectable()
export class MwtmSharepointClient {
  private readonly _client: AxiosInstance
  private token: Oauth2TokenResponse | null = null

  constructor (
    private readonly configService: ConfigService,
    private readonly logService: OpenTelemetryLoggerService,
    private readonly assertionGenerator: MwtmSharepointClientOauthAssertionGenerator
  ) {
    this._client = axios.create({ baseURL: configService.getOrThrow('SHAREPOINT_DOCUMENT_BASE_URL') })
    this.applyRequestInterceptors()
    this.applyResponseInterceptors()
  }

  get client (): AxiosInstance {
    return this._client
  }

  private applyRequestInterceptors (): void {
    this._client.interceptors.request.use(async (config) => {
      const token = (await this.getToken()).access_token

      config.headers.Authorization = `Bearer ${token}`

      return config
    })
  }

  private applyResponseInterceptors (): void {
    this._client.interceptors.response.use(
      (response: AxiosResponse) => {
        this.logService.info({
          context: LogContext.INFO,
          body: {
            status: response.status
          },
          attributes: {
            method: response.config?.method,
            url: response.config?.url,
            headers: response.config?.headers,
            params: response.config?.params,
            data: response.config?.data
          }
        })

        return response
      },
      (error: AxiosError) => {
        captureException(error, {
          extra: {
            request: {
              method: error.config?.method,
              url: error.config?.url,
              headers: error.config?.headers,
              params: error.config?.params as unknown,
              data: error.config?.data as unknown
            },
            response: {
              status: error.response?.status,
              data: error.response?.data
            }
          }
        })

        this.logService.error({
          context: LogContext.INFO,
          body: {
            status: error.response?.status,
            data: error.response?.data
          },
          attributes: {
            method: error.config?.method,
            url: error.config?.url,
            headers: error.config?.headers,
            params: error.config?.params,
            data: error.config?.data
          }
        })
        throw error
      }
    )
  }

  private async getToken (): Promise<Oauth2TokenResponse> {
    if (this.token === null || this.isTokenExpired()) {
      this.token = await this.requestToken()
    }

    return this.token
  }

  private async requestToken (): Promise<Oauth2TokenResponse> {
    const url = this.configService.getOrThrow<string>('SHAREPOINT_DOCUMENT_OAUTH_URL')
    const command = await this.buildCommand()
    const data = QueryString.stringify(command)

    const response = await axios.post<Oauth2TokenResponse>(url, data, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    })

    return {
      ...response.data,
      created_at: dayjs().subtract(1, 'minute').toDate()
    }
  }

  private async buildCommand (): Promise<Oauth2Command> {
    const command = new Oauth2CommandBuilder()
      .withClientId(this.configService.getOrThrow<string>('SHAREPOINT_DOCUMENT_CLIENT_ID'))
      .withGrantType('client_credentials')
      .withScope(this.configService.getOrThrow<string>('SHAREPOINT_DOCUMENT_SCOPE'))

    if (isProdEnv()) {
      command
        .withClientAssertion(await this.assertionGenerator.generate())
        .withClientAssertionType(CERTIFICATE_CLIENT_ASSERTION_TYPE)
    } else {
      command.withClientSecret(this.configService.getOrThrow<string>('SHAREPOINT_DOCUMENT_CLIENT_SECRET'))
    }

    return command.build()
  }

  private isTokenExpired (): boolean {
    if (this.token === null) {
      return true
    }

    return dayjs(this.token.created_at).add(this.token.expires_in, 'seconds').isBefore()
  }
}
