import { createHash, randomUUID } from 'crypto'
import { Injectable } from '@nestjs/common'
import dayjs from 'dayjs'
import { importPKCS8, SignJWT } from 'jose'
import { ConfigService } from '@nestjs/config'

@Injectable()
export class MwtmSharepointClientOauthAssertionGenerator {
  constructor (
    private readonly configService: ConfigService
  ) {}

  async generate (): Promise<string> {
    const base64Key = Buffer.from(this.configService.getOrThrow<string>('SHAREPOINT_DOCUMENT_PRIVATE_KEY'), 'base64').toString()
    const certificate = Buffer.from(this.configService.getOrThrow<string>('SHAREPOINT_DOCUMENT_CERTIFICATE'), 'base64').toString()
    const clientId = this.configService.getOrThrow<string>('SHAREPOINT_DOCUMENT_CLIENT_ID')
    const aud = this.configService.getOrThrow<string>('SHAREPOINT_DOCUMENT_OAUTH_URL')

    const cryptoKey = await importPKCS8(base64Key, 'PS256')

    // Remove header, footer and padding
    const certDerB64 = certificate
      .replace(/-----BEGIN CERTIFICATE-----/g, '')
      .replace(/-----END CERTIFICATE-----/g, '')
      .replace(/\s+/g, '')

    const der = Buffer.from(certDerB64, 'base64')
    const hash = createHash('sha256').update(der).digest()
    const thumb = hash.toString('base64')
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=+$/, '')

    const now = dayjs().unix()

    const assertion = await new SignJWT({
      aud,
      exp: dayjs.unix(now).add(5, 'minutes').unix(),
      iat: now,
      iss: clientId,
      jti: randomUUID(),
      nbf: now,
      sub: clientId
    })
      .setProtectedHeader({
        'alg': 'PS256',
        'typ': 'JWT',
        'x5t#S256': thumb
      })
      .sign(cryptoKey)

    return assertion
  }
}
