import { Injectable } from '@nestjs/common'
import { captureException } from '@sentry/nestjs'
import axios, { AxiosError, AxiosInstance, AxiosResponse } from 'axios'
import { ConfigService } from '@nestjs/config'
import { LogContext, OpenTelemetryLoggerService } from '../../../utils/opentelemetry/modules/logger.service.js'

@Injectable()
export class SharepointClient {
  private readonly _client: AxiosInstance

  constructor (
    private readonly configService: ConfigService,
    private readonly logService: OpenTelemetryLoggerService
  ) {
    this._client = axios.create({
      baseURL: this.configService.getOrThrow('SHAREPOINT_BASE_URL'),
      headers: {
        'Ocp-Apim-Subscription-Key': this.configService.getOrThrow<string>('SHAREPOINT_SUBSCRIPTION_KEY')
      }
    })
    this.applyResponseInterceptors()
  }

  get client (): AxiosInstance {
    return this._client
  }

  private applyResponseInterceptors (): void {
    this._client.interceptors.response.use(
      (response: AxiosResponse) => {
        this.logService.info({
          context: LogContext.INFO,
          body: {
            status: response.status
          },
          attributes: {
            method: response.config?.method,
            url: response.config?.url,
            headers: response.config?.headers,
            params: response.config?.params,
            data: response.config?.data
          }
        })

        return response
      },
      (error: AxiosError) => {
        captureException(error, {
          extra: {
            request: {
              method: error.config?.method,
              url: error.config?.url,
              headers: error.config?.headers,
              params: error.config?.params as unknown,
              data: error.config?.data as unknown
            },
            response: {
              status: error.response?.status,
              data: error.response?.data
            }
          }
        })

        this.logService.error({
          context: LogContext.INFO,
          body: {
            status: error.response?.status,
            data: error.response?.data
          },
          attributes: {
            method: error.config?.method,
            url: error.config?.url,
            headers: error.config?.headers,
            params: error.config?.params,
            data: JSON.stringify(error.config?.data)
          }
        })
        throw error
      }
    )
  }
}
