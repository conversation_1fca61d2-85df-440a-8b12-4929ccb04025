import { Injectable, type CanActivate, type ExecutionContext } from '@nestjs/common'
import { Reflector } from '@nestjs/core'
import { AuthContext } from '../../auth/auth.context.js'
import type { Permission } from '../permission.enum.js'
import { PERMISSIONS_KEY } from '../permission.decorator.js'
import { PermissionGuardService } from './permission.guard.service.js'
import { SecurityLoggerService } from '../../../utils/opentelemetry/modules/security-logger.service.js'

@Injectable()
export class PermissionsGuard implements CanActivate {
  constructor(
    private readonly reflector: Reflector,
    private readonly permissionService: PermissionGuardService,
    private readonly authContext: AuthContext,
    private readonly securityLogger: SecurityLoggerService
  ) { }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const requiredPermissions = this.reflector.getAllAndOverride<Permission[]>(PERMISSIONS_KEY, [
      context.getHandler(),
      context.getClass()
    ])

    if (requiredPermissions == null) {
      return true
    }

    const userUuid = this.authContext.getUserUuidOrFail()
    const hasPermissions = await this.permissionService.hasPermissions(userUuid, requiredPermissions)

    if (!hasPermissions) {
      // Log authorization denial
      const request = context.switchToHttp().getRequest()
      const endpoint = `${context.getClass().name}.${context.getHandler().name}`

      this.securityLogger.logAuthorizationDenied(
        userUuid,
        endpoint,
        requiredPermissions,
        request?.ip
      )
    }

    return hasPermissions
  }
}
