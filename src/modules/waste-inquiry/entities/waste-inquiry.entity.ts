import { Column, CreateDateColumn, Entity, Index, JoinColumn, ManyToOne, PrimaryGeneratedColumn, Relation, UpdateDateColumn } from 'typeorm'
import { StateOfMatter } from '../enums/state-of-matter.enum.js'
import { WastePackagingType } from '../enums/waste-packaging-type.enum.js'
import { Customer } from '../../customer/types/customer.type.js'
import { WasteProducer } from '../../waste-producer/types/waste-producer.type.js'
import { PickUpAddress } from '../../pick-up-address/types/pick-up-address.type.js'
import { StableTemperatureType } from '../enums/stable-temperature-type.enum.js'
import { WasteComposition } from '../types/waste-composition.type.js'
import { FileLink } from '../../files/entities/file-link.entity.js'
import { WasteLegislationOption } from '../enums/waste-legislation-option.enum.js'
import { WastePropertyOption } from '../enums/waste-property-option.enum.js'
import { WasteMeasurementUnit } from '../enums/waste-measurement-unit.enum.js'
import { WasteDischargeFrequency } from '../enums/waste-discharge-frequency.enum.js'
import { UnNumber } from '../types/un-number.type.js'
import { WasteTransportType } from '../enums/waste-transport-type.enum.js'
import { ContainerLoadingType } from '../enums/container-loading-type.enum.js'
import { WasteLoadingType } from '../enums/waste-loading-type.enum.js'
import { WasteTransportInOption } from '../enums/waste-transport-in-option.enum.js'
import { WasteLoadingMethod } from '../enums/waste-loading-method.enum.js'
import { WasteStoredInOption } from '../enums/waste-stored-in-option.enum.js'
import { Contact } from '../../contact/types/contact.type.js'
import { RegulatedTransportOption } from '../enums/regulated-transport-option.enum.js'
import { WastePackaging } from '../types/waste-packaging.type.js'
import { CollectionRequirementOption } from '../enums/collection-requirement-option.enum.js'
import { User } from '../../../app/users/entities/user.entity.js'
import { WasteFlashpointOption } from '../enums/waste-flashpoint-option.enum.js'
import { WastePhOption } from '../enums/waste-ph-option.enum.js'
import { SvhcExtraOption } from '../enums/svhc-extra-option.enum.js'

@Entity()
export class WasteInquiry {
  @PrimaryGeneratedColumn('uuid')
  uuid: string

  @CreateDateColumn({ precision: 3 })
  createdAt: Date

  @UpdateDateColumn({ precision: 3 })
  updatedAt: Date

  @Column({ type: 'varchar', nullable: true })
  wasteStreamName: string | null

  @Column({ type: 'varchar', nullable: true })
  wasteStreamDescription: string | null

  @Column({ type: 'varchar', nullable: true })
  ewcLevel1: string | null

  @Column({ type: 'varchar', nullable: true })
  ewcLevel2: string | null

  @Column({ type: 'varchar', nullable: true })
  ewcLevel3: string | null

  @Column({ type: 'enum', enum: StateOfMatter, nullable: true })
  stateOfMatter: StateOfMatter | null

  @Column({ type: 'enum', enum: WastePackagingType, nullable: true })
  packagingType: WastePackagingType | null

  @Column({ type: 'enum', enum: WasteFlashpointOption, nullable: true })
  flashpoint: WasteFlashpointOption | null

  @Column({ type: 'enum', enum: WastePhOption, nullable: true })
  ph: WastePhOption | null

  @Column({ type: 'real', nullable: true })
  specificGravity: number | null

  @Column({ type: 'enum', enum: StableTemperatureType, nullable: true })
  stableTemperatureType: StableTemperatureType | null

  @Column({ type: 'real', nullable: true })
  minStableTemperature: number | null

  @Column({ type: 'real', nullable: true })
  maxStableTemperature: number | null

  @Column({ type: 'real', nullable: true })
  averageStableTemperature: number | null

  @Column({ type: 'boolean', default: false })
  noSds: boolean

  @Column({ type: 'boolean', default: false })
  noAnalysisReport: boolean

  @Column({ type: 'jsonb', default: [] })
  composition: WasteComposition[]

  @Column({ type: 'boolean', nullable: true })
  isSampleAvailable: boolean | null

  @Column({ type: 'enum', enum: WasteLegislationOption, array: true, default: [] })
  selectedLegislationOptions: WasteLegislationOption[]

  @Column({ type: 'enum', enum: SvhcExtraOption, nullable: true })
  svhcExtra: SvhcExtraOption | null

  @Column({ type: 'varchar', nullable: true })
  legislationRemarks: string | null

  @Column({ type: 'enum', enum: WastePropertyOption, array: true, default: [] })
  selectedPropertyOptions: WastePropertyOption[]

  @Column({ type: 'varchar', nullable: true })
  propertyRemarks: string | null

  @Column({ type: 'double precision', nullable: true })
  expectedYearlyVolumeAmount: number | null

  @Column({ type: 'enum', enum: WasteMeasurementUnit, nullable: true })
  expectedYearlyVolumeUnit: WasteMeasurementUnit | null

  @Column({ type: 'double precision', nullable: true })
  expectedPerCollectionQuantity: number | null

  @Column({ type: 'enum', enum: WasteMeasurementUnit, nullable: true })
  expectedPerCollectionUnit: WasteMeasurementUnit | null

  @Column({ type: 'enum', enum: WasteDischargeFrequency, nullable: true })
  dischargeFrequency: WasteDischargeFrequency | null

  @Column({ type: 'date', nullable: true })
  firstCollectionDate: string | null

  @Column({ type: 'date', nullable: true })
  expectedEndDate: string | null

  @Column({ type: 'varchar', nullable: true })
  collectionRemarks: string | null

  @Column({ type: 'boolean', nullable: true })
  isTransportByIndaver: boolean | null

  @Column({ type: 'boolean', nullable: true })
  isLoadingByIndaver: boolean | null

  @Column({ type: 'enum', enum: RegulatedTransportOption, nullable: true })
  isRegulatedTransport: RegulatedTransportOption | null

  @Column({ type: 'jsonb', default: [] })
  unNumbers: UnNumber[]

  @Column({ type: 'jsonb', default: [] })
  packaging: WastePackaging[]

  @Column({ type: 'enum', enum: WasteTransportType, nullable: true })
  transportType: WasteTransportType | null

  @Column({ type: 'enum', enum: ContainerLoadingType, nullable: true })
  containerLoadingType: ContainerLoadingType | null

  @Column({ type: 'enum', enum: WasteLoadingType, nullable: true })
  loadingType: WasteLoadingType | null

  @Column({ type: 'enum', enum: WasteTransportInOption, nullable: true })
  transportIn: WasteTransportInOption | null

  @Column({ type: 'enum', enum: WasteLoadingMethod, nullable: true })
  loadingMethod: WasteLoadingMethod | null

  @Column({ type: 'enum', enum: WasteStoredInOption, nullable: true })
  storedIn: WasteStoredInOption | null

  @Column({ type: 'double precision', nullable: true })
  transportVolumeAmount: number | null

  @Column({ type: 'enum', enum: WasteMeasurementUnit, nullable: true })
  transportVolumeUnit: WasteMeasurementUnit | null

  @Column({ type: 'boolean', nullable: true })
  isTankOwnedByCustomer: boolean | null

  @Column({ type: 'enum', enum: CollectionRequirementOption, nullable: true })
  collectionRequirements: CollectionRequirementOption | null

  @Column({ type: 'varchar', nullable: true })
  remarks: string | null

  @Column({ type: 'jsonb', default: [] })
  sendCopyToContacts: Contact[]

  @Column({ type: 'timestamp', nullable: true })
  submittedOn: Date | null

  @Column({ type: 'varchar', nullable: true })
  inquiryNumber: string | null

  @Column({ type: 'varchar', nullable: true })
  @Index()
  customerId: string | null

  @Column({ type: 'varchar', nullable: true })
  customerName: string | null

  @Column({ type: 'varchar', nullable: true })
  @Index()
  wasteProducerId: string | null

  @Column({ type: 'varchar', nullable: true })
  wasteProducerName: string | null

  @Column({ type: 'boolean', default: false })
  isUnknownWasteProducer: boolean

  @Column({ type: 'boolean', default: false })
  isUnknownPickUpAddress: boolean

  @Column({ type: 'varchar', nullable: true })
  @Index()
  pickUpAddressId: string | null

  @Column({ type: 'varchar', nullable: true })
  pickUpAddressName: string | null

  @Column({ type: 'uuid' })
  @Index()
  createdByUserUuid: string

  @ManyToOne(() => User, user => user.wasteInquiries)
  @JoinColumn({ name: 'created_by_user_uuid' })
  createdByUser?: Relation<User>

  @Column({ type: 'varchar', nullable: true })
  hazardInducer1: string | null

  @Column({ type: 'varchar', nullable: true })
  hazardInducer2: string | null

  @Column({ type: 'varchar', nullable: true })
  hazardInducer3: string | null

  customer?: Customer | null
  wasteProducer?: WasteProducer | null
  pickUpAddress?: PickUpAddress | null

  sdsFiles?: FileLink[]
  analysisReportFiles?: FileLink[]
  additionalFiles?: FileLink[]
}
