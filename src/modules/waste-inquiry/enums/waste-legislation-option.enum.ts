import { exhaustiveCheck } from '../../../utils/helpers/exhaustive-check.helper.js'
import { SapWasteSubjectLegislationCode } from '../../sap/use-cases/create-waste-inquiry/create-waste-inquiry-sap.command.js'

export enum WasteLegislationOption {
  NONE = 'none',
  RADIOACTIVE = 'radioactive',
  CWC = 'cwc',
  CONTROLLED_DRUGS = 'controlled-drugs',
  DRUG_PRECURSOR = 'drug-precursor',
  HG_CONTAINING = 'hg-containing',
  OZON_DEPLETING_SUBSTANCE = 'ozon-depleting-substance',
  ANIMAL_BYPRODUCT = 'animal-byproduct',
  INFECTIOUS_WASTE = 'infectious-waste',
  SVHC = 'svhc'
}

export function mapWasteLegislationOptionToSapValue (
  wasteLegislationOption: WasteLegislationOption
): SapWasteSubjectLegislationCode {
  switch (wasteLegislationOption) {
    case WasteLegislationOption.NONE:
      return '00'
    case WasteLegislationOption.RADIOACTIVE:
      return '01'
    case WasteLegislationOption.CWC:
      return '02'
    case WasteLegislationOption.CONTROLLED_DRUGS:
      return '03'
    case WasteLegislationOption.DRUG_PRECURSOR:
      return '04'
    case WasteLegislationOption.HG_CONTAINING:
      return '05'
    case WasteLegislationOption.OZON_DEPLETING_SUBSTANCE:
      return '06'
    case WasteLegislationOption.ANIMAL_BYPRODUCT:
      return '07'
    case WasteLegislationOption.INFECTIOUS_WASTE:
      return '08'
    case WasteLegislationOption.SVHC:
      return '09'
    default:
      exhaustiveCheck(wasteLegislationOption)
  }
}

export function mapSapValueToWasteLegislationOption (
  sapValue: SapWasteSubjectLegislationCode
): WasteLegislationOption | null {
  switch (sapValue) {
    case '00':
      return WasteLegislationOption.NONE
    case '01':
      return WasteLegislationOption.RADIOACTIVE
    case '02':
      return WasteLegislationOption.CWC
    case '03':
      return WasteLegislationOption.CONTROLLED_DRUGS
    case '04':
      return WasteLegislationOption.DRUG_PRECURSOR
    case '05':
      return WasteLegislationOption.HG_CONTAINING
    case '06':
      return WasteLegislationOption.OZON_DEPLETING_SUBSTANCE
    case '07':
      return WasteLegislationOption.ANIMAL_BYPRODUCT
    case '08':
      return WasteLegislationOption.INFECTIOUS_WASTE
    case '09':
      return WasteLegislationOption.SVHC
    default:
      return null
  }
}
