import { exhaustiveCheck } from '../../../utils/helpers/exhaustive-check.helper.js'
import { SapPackagingType } from '../../sap/use-cases/create-waste-inquiry/create-waste-inquiry-sap.command.js'

export enum WastePackagingOption {
  ASF = 'asf',
  ASP = 'asp',
  BIG_BAG = 'big-bag',
  CARDBOARD_BOX = 'cardboard-box',
  IBC = 'ibc',
  METAL_DRUM = 'metal-drum',
  OVERSIZED_DRUM = 'oversized-drum',
  PLASTIC_DRUM = 'plastic-drum',
  OTHER = 'other'
}

export function mapWastePackagingOptionToSapValue (
  wastePackagingOption: WastePackagingOption
): SapPackagingType {
  switch (wastePackagingOption) {
    case WastePackagingOption.ASF:
      return '01'
    case WastePackagingOption.ASP:
      return '02'
    case WastePackagingOption.BIG_BAG:
      return '03'
    case WastePackagingOption.CARDBOARD_BOX:
      return '04'
    case WastePackagingOption.IBC:
      return '05'
    case WastePackagingOption.METAL_DRUM:
      return '06'
    case WastePackagingOption.OVERSIZED_DRUM:
      return '07'
    case WastePackagingOption.PLASTIC_DRUM:
      return '08'
    case WastePackagingOption.OTHER:
      return '09'
    default:
      exhaustiveCheck(wastePackagingOption)
  }
}

export function mapSapValueToWastePackagingOption (
  sapValue: SapPackagingType
): WastePackagingOption | null {
  switch (sapValue) {
    case '01':
      return WastePackagingOption.ASF
    case '02':
      return WastePackagingOption.ASP
    case '03':
      return WastePackagingOption.BIG_BAG
    case '04':
      return WastePackagingOption.CARDBOARD_BOX
    case '05':
      return WastePackagingOption.IBC
    case '06':
      return WastePackagingOption.METAL_DRUM
    case '07':
      return WastePackagingOption.OVERSIZED_DRUM
    case '08':
      return WastePackagingOption.PLASTIC_DRUM
    case '09':
      return WastePackagingOption.OTHER
    default:
      return null
  }
}
