import { exhaustiveCheck } from '../../../utils/helpers/exhaustive-check.helper.js'
import { SapWasteSubjectHazardCode } from '../../sap/use-cases/create-waste-inquiry/create-waste-inquiry-sap.command.js'

export enum WastePropertyOption {
  NONE = 'none',
  EXPLOSIVE = 'explosive',
  GASEOUS = 'gaseous',
  PEROXIDE = 'peroxide',
  POLYMERISATION_SENSITIVE = 'polymerisation-sensitive',
  PYROPHORIC = 'pyrophoric',
  STRONG_OXIDIZING = 'strong-oxidizing',
  REACTIVE_WITH_T_GAS = 'reactive-with-t-gas',
  REACTIVE_WITH_F_GAS = 'reactive-with-f-gas',
  HIGH_ACUTE_TOXIC = 'high-acute-toxic',
  THERMAL_UNSTABLE = 'thermal-unstable'
}

export function mapWastePropertyOptionToSapValue (
  wastePropertyOption: WastePropertyOption
): SapWasteSubjectHazardCode {
  switch (wastePropertyOption) {
    case WastePropertyOption.NONE:
      return '00'
    case WastePropertyOption.EXPLOSIVE:
      return '01'
    case WastePropertyOption.GASEOUS:
      return '02'
    case WastePropertyOption.PEROXIDE:
      return '03'
    case WastePropertyOption.POLYMERISATION_SENSITIVE:
      return '04'
    case WastePropertyOption.PYROPHORIC:
      return '05'
    case WastePropertyOption.STRONG_OXIDIZING:
      return '06'
    case WastePropertyOption.REACTIVE_WITH_T_GAS:
      return '07'
    case WastePropertyOption.REACTIVE_WITH_F_GAS:
      return '08'
    case WastePropertyOption.HIGH_ACUTE_TOXIC:
      return '09'
    case WastePropertyOption.THERMAL_UNSTABLE:
      return '10'
    default:
      exhaustiveCheck(wastePropertyOption)
  }
}

export function mapSapValueToWastePropertyOption (
  sapValue: SapWasteSubjectHazardCode
): WastePropertyOption | null {
  switch (sapValue) {
    case '00':
      return WastePropertyOption.NONE
    case '01':
      return WastePropertyOption.EXPLOSIVE
    case '02':
      return WastePropertyOption.GASEOUS
    case '03':
      return WastePropertyOption.PEROXIDE
    case '04':
      return WastePropertyOption.POLYMERISATION_SENSITIVE
    case '05':
      return WastePropertyOption.PYROPHORIC
    case '06':
      return WastePropertyOption.STRONG_OXIDIZING
    case '07':
      return WastePropertyOption.REACTIVE_WITH_T_GAS
    case '08':
      return WastePropertyOption.REACTIVE_WITH_F_GAS
    case '09':
      return WastePropertyOption.HIGH_ACUTE_TOXIC
    case '10':
      return WastePropertyOption.THERMAL_UNSTABLE
    default:
      return null
  }
}
