import { exhaustiveCheck } from '../../../utils/helpers/exhaustive-check.helper.js'
import { SapNumberPackGroup } from '../../sap/use-cases/create-waste-inquiry/create-waste-inquiry-sap.command.js'

export enum PackingGroup {
  NOT_APPLICABLE = 'not-applicable',
  ONE = 'one',
  TWO = 'two',
  THREE = 'three'
}

export function mapSapIValueToPackingGroup (
  sapIValue: string
): PackingGroup {
  switch (sapIValue) {
    case '':
      return PackingGroup.NOT_APPLICABLE
    case 'I':
      return PackingGroup.ONE
    case 'II':
      return PackingGroup.TWO
    case 'III':
      return PackingGroup.THREE
    default:
      throw new Error(`Unknown packing group value: ${sapIValue}`)
  }
}

export function mapPackingGroupToSapValue (
  packingGroup: PackingGroup
): SapNumberPackGroup {
  switch (packingGroup) {
    case PackingGroup.NOT_APPLICABLE:
      return '0'
    case PackingGroup.ONE:
      return '1'
    case PackingGroup.TWO:
      return '2'
    case PackingGroup.THREE:
      return '3'
    default:
      exhaustiveCheck(packingGroup)
  }
}

export function mapSapValueToPackingGroup (
  sapValue: SapNumberPackGroup
): PackingGroup | null {
  switch (sapValue) {
    case '0':
      return PackingGroup.NOT_APPLICABLE
    case '1':
      return PackingGroup.ONE
    case '2':
      return PackingGroup.TWO
    case '3':
      return PackingGroup.THREE
    default:
      return null
  }
}
