import { ApiProperty, ApiPropertyOptions } from '@nestjs/swagger'
import { SapTypeOfWasteRequestType } from '../../sap/types/type-of-waste-request.type.js'

export enum TypeOfWasteRequest {
  NEW_WASTE_REQUEST = 'new_waste_request',
  CONFORMITY_ASSESSMENT = 'conformity_assessment'
}

export function TypeOfWasteRequestApiProperty (options?: ApiPropertyOptions): PropertyDecorator {
  return ApiProperty({
    ...options,
    enum: TypeOfWasteRequest,
    enumName: 'TypeOfWasteRequest'
  })
}

export function mapSapValueToTypeOfWasteRequest (
  value: SapTypeOfWasteRequestType
): TypeOfWasteRequest | null {
  switch (value) {
    case '1':
      return TypeOfWasteRequest.NEW_WASTE_REQUEST
    case '2':
      return TypeOfWasteRequest.CONFORMITY_ASSESSMENT
    default:
      return null
  }
}

export function mapTypeOfWasteRequestToSapValue (
  typeOfWasteRequest: TypeOfWasteRequest | null
): SapTypeOfWasteRequestType | null {
  switch (typeOfWasteRequest) {
    case TypeOfWasteRequest.NEW_WASTE_REQUEST:
      return '1'
    case TypeOfWasteRequest.CONFORMITY_ASSESSMENT:
      return '2'
    default:
      return null
  }
}
