import { before, describe, it } from 'node:test'
import { expect } from 'expect'
import { randWord } from '@ngneat/falso'
import { TestBench } from '../../../../../test/setup/test-bench.js'
import { ValidWasteInquiryEntityBuilder } from '../../tests/valid-waste-inquiry-entity.builder.js'
import { WasteInquiryEntityBuilder } from '../../tests/waste-inquiry-entity.builder.js'
import { WasteInquiryTransportValidator } from '../waste-inquiry-transport.validator.js'
import { RegulatedTransportOption } from '../../enums/regulated-transport-option.enum.js'
import { WastePackagingType } from '../../enums/waste-packaging-type.enum.js'
import { WasteTransportType } from '../../enums/waste-transport-type.enum.js'
import { FieldMustBeNullError } from '../../../exceptions/generic/field-must-be-null.error.js'
import { StateOfMatter } from '../../enums/state-of-matter.enum.js'
import { ContainerLoadingType } from '../../enums/container-loading-type.enum.js'
import { WasteTransportInOption } from '../../enums/waste-transport-in-option.enum.js'
import { WasteLoadingType } from '../../enums/waste-loading-type.enum.js'
import { CollectionRequirementOption } from '../../enums/collection-requirement-option.enum.js'
import { WasteLoadingMethod } from '../../enums/waste-loading-method.enum.js'
import { WasteStoredInOption } from '../../enums/waste-stored-in-option.enum.js'
import { WasteMeasurementUnit } from '../../enums/waste-measurement-unit.enum.js'
import { WastePackagingOption } from '../../enums/waste-packaging-option.enum.js'
import { WeightUnit } from '../../enums/weight-unit.enum.js'
import { MissingRequiredFieldError } from '../../../exceptions/generic/missing-required-field.error.js'
import { PackingGroup } from '../../enums/packaging-group.enum.js'

describe('Waste inquiry transport validator unit test', () => {
  let validator: WasteInquiryTransportValidator

  before(() => {
    TestBench.setupUnitTest()

    validator = new WasteInquiryTransportValidator()
  })

  describe('Update validation', () => {
    describe('unNumbers', () => {
      it('doesn\'t throw an error when unNumbers provided but regulated transport', () => {
        const wasteInquiry = new WasteInquiryEntityBuilder()
          .withIsRegulatedTransport(RegulatedTransportOption.YES)
          .withUnNumbers([{
            unNumber: randWord(),
            packingGroup: null,
            isHazardous: false
          }])
          .build()

        expect(() => validator.validateUpdate(wasteInquiry)).not.toThrow()
      })

      it('throws an error when unNumbers provided but not regulated transport defined', () => {
        const wasteInquiry = new WasteInquiryEntityBuilder()
          .withIsRegulatedTransport(RegulatedTransportOption.NO)
          .withUnNumbers([{
            unNumber: randWord(),
            packingGroup: null,
            isHazardous: false
          }])
          .build()

        expect(() => validator.validateUpdate(wasteInquiry)).toThrow(FieldMustBeNullError)
      })
    })

    describe('transportType', () => {
      it('doesn\'t throw an error when combination allowed', () => {
        const wasteInquiry = new WasteInquiryEntityBuilder()
          .withPackagingType(WastePackagingType.BULK)
          .withStateOfMatter(StateOfMatter.SLUDGY)
          .withTransportType(WasteTransportType.OTHER)
          .build()

        expect(() => validator.validateUpdate(wasteInquiry)).not.toThrow()
      })

      it('throws an error when packaging type isn\'t bulk', () => {
        const wasteInquiry = new WasteInquiryEntityBuilder()
          .withPackagingType(WastePackagingType.PACKAGED)
          .withStateOfMatter(StateOfMatter.POWDER)
          .withTransportType(WasteTransportType.OTHER)
          .build()

        expect(() => validator.validateUpdate(wasteInquiry)).toThrow(FieldMustBeNullError)
      })

      it('throws an error when state of matter isn\'t allowed', () => {
        const wasteInquiry = new WasteInquiryEntityBuilder()
          .withPackagingType(WastePackagingType.BULK)
          .withStateOfMatter(StateOfMatter.GASEOUS)
          .withTransportType(WasteTransportType.OTHER)
          .build()

        expect(() => validator.validateUpdate(wasteInquiry)).toThrow(FieldMustBeNullError)
      })
    })

    describe('containerLoadingType', () => {
      it('doesn\'t throw an error when combination allowed', () => {
        const wasteInquiry = new WasteInquiryEntityBuilder()
          .withContainerLoadingType(ContainerLoadingType.CHAIN)
          .withTransportType(WasteTransportType.CONTAINER)
          .withStateOfMatter(StateOfMatter.SOLID)
          .withPackagingType(WastePackagingType.BULK)
          .build()

        expect(() => validator.validateUpdate(wasteInquiry)).not.toThrow()
      })

      it('throws an error when transport type isn\'t container', () => {
        const wasteInquiry = new WasteInquiryEntityBuilder()
          .withContainerLoadingType(ContainerLoadingType.CHAIN)
          .withTransportType(WasteTransportType.OTHER)
          .withStateOfMatter(StateOfMatter.SOLID)
          .withPackagingType(WastePackagingType.BULK)
          .build()

        expect(() => validator.validateUpdate(wasteInquiry)).toThrow(FieldMustBeNullError)
      })
    })

    describe('isLoadingByIndaver', () => {
      it('doesn\'t throw an error when combination allowed', () => {
        const wasteInquiry = new WasteInquiryEntityBuilder()
          .withIsLoadingByIndaver(true)
          .withPackagingType(WastePackagingType.BULK)
          .withStateOfMatter(StateOfMatter.SOLID)
          .withTransportType(WasteTransportType.TIPPER_TRUCK)
          .build()

        expect(() => validator.validateUpdate(wasteInquiry)).not.toThrow()
      })

      it('throws an error when packaging type isn\'t allowed', () => {
        const wasteInquiry = new WasteInquiryEntityBuilder()
          .withIsLoadingByIndaver(true)
          .withPackagingType(WastePackagingType.PACKAGED)
          .withStateOfMatter(StateOfMatter.SOLID)
          .withTransportType(WasteTransportType.TIPPER_TRUCK)
          .build()

        expect(() => validator.validateUpdate(wasteInquiry)).toThrow(FieldMustBeNullError)
      })

      it('throws an error when state of matter isn\'t allowed', () => {
        const wasteInquiry = new WasteInquiryEntityBuilder()
          .withIsLoadingByIndaver(true)
          .withPackagingType(WastePackagingType.BULK)
          .withStateOfMatter(StateOfMatter.GASEOUS)
          .withTransportType(WasteTransportType.TIPPER_TRUCK)
          .build()

        expect(() => validator.validateUpdate(wasteInquiry)).toThrow(FieldMustBeNullError)
      })

      it('throws an error when transport type isn\'t allowed', () => {
        const wasteInquiry = new WasteInquiryEntityBuilder()
          .withIsLoadingByIndaver(true)
          .withPackagingType(WastePackagingType.BULK)
          .withStateOfMatter(StateOfMatter.SOLID)
          .withTransportType(WasteTransportType.OTHER)
          .build()

        expect(() => validator.validateUpdate(wasteInquiry)).toThrow(FieldMustBeNullError)
      })
    })

    describe('transportIn', () => {
      it('doesn\'t throw an error when loading type specified', () => {
        const wasteInquiry = new WasteInquiryEntityBuilder()
          .withTransportIn(WasteTransportInOption.OTHER)
          .withLoadingType(WasteLoadingType.BEFORE_WASTE_COLLECTION)
          .withStateOfMatter(StateOfMatter.GASEOUS)
          .withPackagingType(WastePackagingType.BULK)
          .build()

        expect(() => validator.validateUpdate(wasteInquiry)).not.toThrow()
      })

      it('throws an error when loading type isn\'t specified', () => {
        const wasteInquiry = new WasteInquiryEntityBuilder()
          .withTransportIn(WasteTransportInOption.OTHER)
          .withLoadingType(null)
          .withStateOfMatter(StateOfMatter.GASEOUS)
          .withPackagingType(WastePackagingType.BULK)
          .build()

        expect(() => validator.validateUpdate(wasteInquiry)).toThrow(FieldMustBeNullError)
      })
    })

    describe ('isTankOwnedByCustomer', () => {
      it('doesn\'t throw an error when combination allowed', () => {
        const wasteInquiry = new WasteInquiryEntityBuilder()
          .withIsTankOwnedByCustomer(true)
          .withTransportIn(WasteTransportInOption.TANK_CONTAINER)
          .withPackagingType(WastePackagingType.BULK)
          .withStateOfMatter(StateOfMatter.GASEOUS)
          .withLoadingType(WasteLoadingType.BEFORE_WASTE_COLLECTION)
          .build()

        expect(() => validator.validateUpdate(wasteInquiry)).not.toThrow()
      })

      it('throws an error when transport in is invalid', () => {
        const wasteInquiry = new WasteInquiryEntityBuilder()
          .withIsTankOwnedByCustomer(true)
          .withTransportIn(WasteTransportInOption.OTHER)
          .withPackagingType(WastePackagingType.BULK)
          .withStateOfMatter(StateOfMatter.GASEOUS)
          .build()

        expect(() => validator.validateUpdate(wasteInquiry)).toThrow(FieldMustBeNullError)
      })

      it('throws an error when packaging type is invalid', () => {
        const wasteInquiry = new WasteInquiryEntityBuilder()
          .withIsTankOwnedByCustomer(true)
          .withTransportIn(WasteTransportInOption.TANK_CONTAINER)
          .withPackagingType(WastePackagingType.PACKAGED)
          .withStateOfMatter(StateOfMatter.GASEOUS)
          .build()

        expect(() => validator.validateUpdate(wasteInquiry)).toThrow(FieldMustBeNullError)
      })

      it('throws an error when state of matter is invalid', () => {
        const wasteInquiry = new WasteInquiryEntityBuilder()
          .withIsTankOwnedByCustomer(true)
          .withTransportIn(WasteTransportInOption.TANK_CONTAINER)
          .withPackagingType(WastePackagingType.PACKAGED)
          .withStateOfMatter(StateOfMatter.POWDER)
          .build()

        expect(() => validator.validateUpdate(wasteInquiry)).toThrow(FieldMustBeNullError)
      })
    })

    describe ('collectionRequirements', () => {
      it('doesn\'t throw an error when combination allowed', () => {
        const wasteInquiry = new WasteInquiryEntityBuilder()
          .withCollectionRequirements(CollectionRequirementOption.TRACTOR)
          .withTransportIn(WasteTransportInOption.TANK_CONTAINER)
          .withPackagingType(WastePackagingType.BULK)
          .withStateOfMatter(StateOfMatter.GASEOUS)
          .withLoadingType(WasteLoadingType.ON_WASTE_COLLECTION)
          .build()

        expect(() => validator.validateUpdate(wasteInquiry)).not.toThrow()
      })

      it('throws an error when transport in is invalid', () => {
        const wasteInquiry = new WasteInquiryEntityBuilder()
          .withCollectionRequirements(CollectionRequirementOption.TRACTOR)
          .withTransportIn(WasteTransportInOption.OTHER)
          .withPackagingType(WastePackagingType.BULK)
          .withStateOfMatter(StateOfMatter.GASEOUS)
          .build()

        expect(() => validator.validateUpdate(wasteInquiry)).toThrow(FieldMustBeNullError)
      })

      it('throws an error when packaging type is invalid', () => {
        const wasteInquiry = new WasteInquiryEntityBuilder()
          .withCollectionRequirements(CollectionRequirementOption.TRACTOR)
          .withTransportIn(WasteTransportInOption.TANK_CONTAINER)
          .withPackagingType(WastePackagingType.PACKAGED)
          .withStateOfMatter(StateOfMatter.GASEOUS)
          .build()

        expect(() => validator.validateUpdate(wasteInquiry)).toThrow(FieldMustBeNullError)
      })

      it('throws an error when state of matter is invalid', () => {
        const wasteInquiry = new WasteInquiryEntityBuilder()
          .withCollectionRequirements(CollectionRequirementOption.TRACTOR)
          .withTransportIn(WasteTransportInOption.TANK_CONTAINER)
          .withPackagingType(WastePackagingType.PACKAGED)
          .withStateOfMatter(StateOfMatter.POWDER)
          .build()

        expect(() => validator.validateUpdate(wasteInquiry)).toThrow(FieldMustBeNullError)
      })
    })

    describe ('loadingMethod', () => {
      it('doesn\'t throw an error when combination allowed', () => {
        const wasteInquiry = new WasteInquiryEntityBuilder()
          .withLoadingMethod(WasteLoadingMethod.GRAVITATIONAL)
          .withLoadingType(WasteLoadingType.ON_WASTE_COLLECTION)
          .withStateOfMatter(StateOfMatter.GASEOUS)
          .withPackagingType(WastePackagingType.BULK)
          .build()

        expect(() => validator.validateUpdate(wasteInquiry)).not.toThrow()
      })

      it('throws an error when loading type is invalid', () => {
        const wasteInquiry = new WasteInquiryEntityBuilder()
          .withLoadingMethod(WasteLoadingMethod.GRAVITATIONAL)
          .withLoadingType(WasteLoadingType.BEFORE_WASTE_COLLECTION)
          .withStateOfMatter(StateOfMatter.GASEOUS)
          .withPackagingType(WastePackagingType.BULK)
          .build()

        expect(() => validator.validateUpdate(wasteInquiry)).toThrow(FieldMustBeNullError)
      })
    })

    describe ('storedIn', () => {
      it('doesn\'t throw an error when combination allowed', () => {
        const wasteInquiry = new WasteInquiryEntityBuilder()
          .withStoredIn(WasteStoredInOption.DRUMS)
          .withLoadingType(WasteLoadingType.ON_WASTE_COLLECTION)
          .withStateOfMatter(StateOfMatter.GASEOUS)
          .withPackagingType(WastePackagingType.BULK)
          .build()

        expect(() => validator.validateUpdate(wasteInquiry)).not.toThrow()
      })

      it('throws an error when loading type is invalid', () => {
        const wasteInquiry = new WasteInquiryEntityBuilder()
          .withStoredIn(WasteStoredInOption.DRUMS)
          .withLoadingType(WasteLoadingType.BEFORE_WASTE_COLLECTION)
          .withStateOfMatter(StateOfMatter.GASEOUS)
          .withPackagingType(WastePackagingType.BULK)
          .build()

        expect(() => validator.validateUpdate(wasteInquiry)).toThrow(FieldMustBeNullError)
      })
    })

    describe ('transportVolume', () => {
      it('doesn\'t throw an error when combination allowed', () => {
        const wasteInquiry = new WasteInquiryEntityBuilder()
          .withTransportVolume(1, WasteMeasurementUnit.TO)
          .withStateOfMatter(StateOfMatter.POWDER)
          .withPackagingType(WastePackagingType.BULK)
          .build()

        expect(() => validator.validateUpdate(wasteInquiry)).not.toThrow()
      })

      it('throws an error when state of matter is invalid', () => {
        const wasteInquiry = new WasteInquiryEntityBuilder()
          .withTransportVolume(1, WasteMeasurementUnit.TO)
          .withStateOfMatter(StateOfMatter.GASEOUS)
          .withPackagingType(WastePackagingType.BULK)
          .build()

        expect(() => validator.validateUpdate(wasteInquiry)).toThrow(FieldMustBeNullError)
      })

      it('throws an error when packaging type is invalid', () => {
        const wasteInquiry = new WasteInquiryEntityBuilder()
          .withTransportVolume(1, WasteMeasurementUnit.TO)
          .withStateOfMatter(StateOfMatter.POWDER)
          .withPackagingType(WastePackagingType.PACKAGED)
          .build()

        expect(() => validator.validateUpdate(wasteInquiry)).toThrow(FieldMustBeNullError)
      })
    })

    describe ('loadingType', () => {
      it('doesn\'t throw an error when combination allowed', () => {
        const wasteInquiry = new WasteInquiryEntityBuilder()
          .withLoadingType(WasteLoadingType.ON_WASTE_COLLECTION)
          .withStateOfMatter(StateOfMatter.GASEOUS)
          .withPackagingType(WastePackagingType.BULK)
          .build()

        expect(() => validator.validateUpdate(wasteInquiry)).not.toThrow()
      })

      it('throws an error when state of matter is invalid', () => {
        const wasteInquiry = new WasteInquiryEntityBuilder()
          .withLoadingType(WasteLoadingType.ON_WASTE_COLLECTION)
          .withStateOfMatter(StateOfMatter.SOLID)
          .withPackagingType(WastePackagingType.BULK)
          .build()

        expect(() => validator.validateUpdate(wasteInquiry)).toThrow(FieldMustBeNullError)
      })

      it('throws an error when packaging type is invalid', () => {
        const wasteInquiry = new WasteInquiryEntityBuilder()
          .withLoadingType(WasteLoadingType.ON_WASTE_COLLECTION)
          .withStateOfMatter(StateOfMatter.GASEOUS)
          .withPackagingType(WastePackagingType.PACKAGED)
          .build()

        expect(() => validator.validateUpdate(wasteInquiry)).toThrow(FieldMustBeNullError)
      })
    })

    describe ('packaging', () => {
      it('doesn\'t throw an error when combination allowed', () => {
        const wasteInquiry = new WasteInquiryEntityBuilder()
          .withPackaging([{
            type: WastePackagingOption.ASF,
            size: '25cm x 25cm x 25xm',
            weightPerPieceValue: 1,
            weightPerPieceUnit: WeightUnit.KG,
            hasInnerPackaging: true,
            remarks: null
          }])
          .withPackagingType(WastePackagingType.PACKAGED)
          .build()

        expect(() => validator.validateUpdate(wasteInquiry)).not.toThrow()
      })

      it('throws an error when packaging type is invalid', () => {
        const wasteInquiry = new WasteInquiryEntityBuilder()
          .withPackaging([{
            type: WastePackagingOption.ASF,
            size: '25cm x 25cm x 25xm',
            weightPerPieceValue: 1,
            weightPerPieceUnit: WeightUnit.KG,
            hasInnerPackaging: true,
            remarks: null
          }])
          .withPackagingType(WastePackagingType.BULK)
          .build()

        expect(() => validator.validateUpdate(wasteInquiry)).toThrow(FieldMustBeNullError)
      })
    })
  })

  describe('Submit validation', () => {
    const wasteInquiry = new ValidWasteInquiryEntityBuilder().build()

    it('doesn\'t throw an error when validation passes', () => {
      expect(() => validator.validateSubmit(wasteInquiry)).not.toThrow()
    })

    describe('isTransportByIndaver', () => {
      it('throws an error when isTransportByIndaver doesn\'t exists', () => {
        const wasteInquiryWithoutIsTransportByIndaver = {
          ...wasteInquiry,
          isTransportByIndaver: null
        }

        expect(() => validator.validateSubmit(wasteInquiryWithoutIsTransportByIndaver))
          .toThrow(MissingRequiredFieldError)
      })
    })

    describe('isRegulatedTransport', () => {
      it('throws an error when isRegulatedTransport doesn\'t exists', () => {
        const wasteInquiryWithoutIsRegulatedTransport = {
          ...wasteInquiry,
          isRegulatedTransport: null
        }

        expect(() => validator.validateSubmit(wasteInquiryWithoutIsRegulatedTransport))
          .toThrow(MissingRequiredFieldError)
      })
    })

    describe('unNumbers', () => {
      it('throws an error when is regulated transport and unNumbers doesn\'t exists', () => {
        const wasteInquiryRegulatedWithoutUnNumbers = {
          ...wasteInquiry,
          isRegulatedTransport: RegulatedTransportOption.YES,
          unNumbers: []
        }

        expect(() => validator.validateSubmit(wasteInquiryRegulatedWithoutUnNumbers))
          .toThrow(MissingRequiredFieldError)
      })

      it('doesn\'t throw an error when regulated transport is unknown and unNumbers doesn\'t exists', () => {
        const wasteInquiryRegulatedWithoutUnNumbers = {
          ...wasteInquiry,
          isRegulatedTransport: RegulatedTransportOption.UNKNOWN,
          unNumbers: []
        }

        expect(() => validator.validateSubmit(wasteInquiryRegulatedWithoutUnNumbers))
          .not.toThrow()
      })

      it('throws an error when unregulated transport and unNumbers given', () => {
        const wasteInquiryRegulatedWithoutUnNumbers = {
          ...wasteInquiry,
          isRegulatedTransport: RegulatedTransportOption.NO,
          unNumbers: [{
            unNumber: '1',
            packingGroup: PackingGroup.ONE,
            isHazardous: false
          }]
        }

        expect(() => validator.validateSubmit(wasteInquiryRegulatedWithoutUnNumbers))
          .toThrow(FieldMustBeNullError)
      })

      it('throws an error when unNumbers incomplete', () => {
        const wasteInquiryRegulatedWithoutUnNumbers = {
          ...wasteInquiry,
          isRegulatedTransport: RegulatedTransportOption.YES,
          unNumbers: [{
            unNumber: null,
            packingGroup: null,
            isHazardous: false
          }]
        }

        expect(() => validator.validateSubmit(wasteInquiryRegulatedWithoutUnNumbers))
          .toThrow(MissingRequiredFieldError)
      })
    })

    describe('transportType', () => {
      it('throws an error when transportType doesn\'t exists but required for selected state of matter and packaging type', () => {
        const wasteInquiryWithoutExpectedTransportType = {
          ...wasteInquiry,
          stateOfMatter: StateOfMatter.POWDER,
          packagingType: WastePackagingType.BULK,
          transportType: null
        }

        expect(() => validator.validateSubmit(wasteInquiryWithoutExpectedTransportType))
          .toThrow(MissingRequiredFieldError)
      })

      it('throws an error when transportType exists but expected empty for selected state of matter and packaging type', () => {
        const wasteInquiryWithUnexpectedTransportType = {
          ...wasteInquiry,
          stateOfMatter: StateOfMatter.VISCOUS,
          packagingType: WastePackagingType.PACKAGED,
          transportType: WasteTransportType.OTHER
        }

        expect(() => validator.validateSubmit(wasteInquiryWithUnexpectedTransportType))
          .toThrow(FieldMustBeNullError)
      })
    })

    describe('containerLoadingType', () => {
      it('throws an error when containerLoadingType doesn\'t exists but required for selected transportType', () => {
        const wasteInquiryWithoutContainerLoadingType = {
          ...wasteInquiry,
          stateOfMatter: StateOfMatter.SOLID,
          packagingType: WastePackagingType.BULK,
          transportType: WasteTransportType.CONTAINER,
          isTransportByIndaver: true,
          containerLoadingType: null
        }

        expect(() => validator.validateSubmit(wasteInquiryWithoutContainerLoadingType))
          .toThrow(MissingRequiredFieldError)
      })

      it('throws an error when containerLoadingType given but expected empty for selected transportType', () => {
        const wasteInquiryWithoutContainerLoadingType = {
          ...wasteInquiry,
          stateOfMatter: StateOfMatter.SOLID,
          packagingType: WastePackagingType.BULK,
          transportType: WasteTransportType.OTHER,
          containerLoadingType: ContainerLoadingType.CHAIN
        }

        expect(() => validator.validateSubmit(wasteInquiryWithoutContainerLoadingType))
          .toThrow(FieldMustBeNullError)
      })
    })

    describe('transportIn', () => {
      it('throws an error when transportIn doesn\'t exists but required for selected state of matter, packaging type and loading type', () => {
        const wasteInquiryWithoutTransportIn = {
          ...wasteInquiry,
          stateOfMatter: StateOfMatter.GASEOUS,
          packagingType: WastePackagingType.BULK,
          loadingType: WasteLoadingType.BEFORE_WASTE_COLLECTION,
          transportIn: null
        }

        expect(() => validator.validateSubmit(wasteInquiryWithoutTransportIn))
          .toThrow(MissingRequiredFieldError)
      })

      it('throws an error when transportIn given but expected empty for selected state of matter, packaging type and loading type', () => {
        const wasteInquiryWithTransportIn = {
          ...wasteInquiry,
          stateOfMatter: StateOfMatter.GASEOUS,
          packagingType: WastePackagingType.PACKAGED,
          transportIn: WasteTransportInOption.NO_PREFERENCE
        }

        expect(() => validator.validateSubmit(wasteInquiryWithTransportIn))
          .toThrow(FieldMustBeNullError)
      })
    })

    describe('isTankOwnedByCustomer', () => {
      it('throws an error when isTankOwnedByCustomer given but expected empty for selected state of matter, packaging type and loading type', () => {
        const wasteInquiryWithStoredIn = {
          ...wasteInquiry,
          stateOfMatter: StateOfMatter.POWDER,
          packagingType: WastePackagingType.PACKAGED,
          transportIn: null,
          loadingMethod: null,
          storedIn: WasteStoredInOption.DRUMS,
          isTankOwnedByCustomer: true
        }

        expect(() => validator.validateSubmit(wasteInquiryWithStoredIn))
          .toThrow(FieldMustBeNullError)
      })
    })

    describe('collectionRequirements', () => {
      it('throws an error when collectionRequirements given but expected empty for selected state of matter, packaging type and loading type', () => {
        const wasteInquiryWithStoredIn = {
          ...wasteInquiry,
          stateOfMatter: StateOfMatter.POWDER,
          packagingType: WastePackagingType.PACKAGED,
          transportIn: null,
          loadingMethod: null,
          storedIn: WasteStoredInOption.DRUMS,
          collectionRequirements: CollectionRequirementOption.TRACTOR
        }

        expect(() => validator.validateSubmit(wasteInquiryWithStoredIn))
          .toThrow(FieldMustBeNullError)
      })
    })

    describe('loadingMethod', () => {
      it('throws an error when loadingMethod doesn\'t exists but required for selected state of matter, packaging type and loading type', () => {
        const wasteInquiryWithoutLoadingMethod = {
          ...wasteInquiry,
          stateOfMatter: StateOfMatter.GASEOUS,
          packagingType: WastePackagingType.BULK,
          loadingType: WasteLoadingType.ON_WASTE_COLLECTION,
          transportIn: WasteTransportInOption.OTHER,
          transportVolumeAmount: null,
          transportVolumeUnit: null,
          isTransportByIndaver: true,
          loadingMethod: null
        }

        expect(() => validator.validateSubmit(wasteInquiryWithoutLoadingMethod))
          .toThrow(MissingRequiredFieldError)
      })

      it('throws an error when loadingMethod given but expected empty for selected state of matter, packaging type and loading type', () => {
        const wasteInquiryWithLoadingMethod = {
          ...wasteInquiry,
          stateOfMatter: StateOfMatter.GASEOUS,
          packagingType: WastePackagingType.PACKAGED,
          transportIn: null,
          loadingMethod: WasteLoadingMethod.GRAVITATIONAL
        }

        expect(() => validator.validateSubmit(wasteInquiryWithLoadingMethod))
          .toThrow(FieldMustBeNullError)
      })
    })

    describe('storedIn', () => {
      it('throws an error when storedIn given but expected empty for selected state of matter, packaging type and loading type', () => {
        const wasteInquiryWithStoredIn = {
          ...wasteInquiry,
          stateOfMatter: StateOfMatter.GASEOUS,
          packagingType: WastePackagingType.PACKAGED,
          transportIn: null,
          loadingMethod: null,
          storedIn: WasteStoredInOption.DRUMS
        }

        expect(() => validator.validateSubmit(wasteInquiryWithStoredIn))
          .toThrow(FieldMustBeNullError)
      })
    })

    describe('transportVolumeAmount', () => {
      it('throws an error when transportVolumeAmount doesn\'t exists but required for selected state of matter and packaging type', () => {
        const wasteInquiryWithoutExpectedVolumeAmount = {
          ...wasteInquiry,
          stateOfMatter: StateOfMatter.POWDER,
          packagingType: WastePackagingType.BULK,
          transportType: WasteTransportType.OTHER,
          transportVolumeAmount: null,
          containerLoadingType: null
        }

        expect(() => validator.validateSubmit(wasteInquiryWithoutExpectedVolumeAmount))
          .toThrow(MissingRequiredFieldError)
      })

      it('throws an error when transportVolumeAmount exists but expected empty for selected state of matter and packaging type', () => {
        const wasteInquiryWithUnexpectedVolumeAmount = {
          ...wasteInquiry,
          stateOfMatter: StateOfMatter.VISCOUS,
          packagingType: WastePackagingType.PACKAGED,
          transportVolumeAmount: 1
        }

        expect(() => validator.validateSubmit(wasteInquiryWithUnexpectedVolumeAmount))
          .toThrow(FieldMustBeNullError)
      })
    })

    describe('transportVolumeUnit', () => {
      it('throws an error when transportVolumeUnit doesn\'t exists but required for selected state of matter and packaging type', () => {
        const wasteInquiryWithoutExpectedVolumeUnit = {
          ...wasteInquiry,
          stateOfMatter: StateOfMatter.POWDER,
          packagingType: WastePackagingType.BULK,
          transportType: WasteTransportType.OTHER,
          transportVolumeAmount: 1,
          transportVolumeUnit: null,
          containerLoadingType: null
        }

        expect(() => validator.validateSubmit(wasteInquiryWithoutExpectedVolumeUnit))
          .toThrow(MissingRequiredFieldError)
      })

      it('throws an error when transportVolumeUnit exists but expected empty for selected state of matter and packaging type', () => {
        const wasteInquiryWithUnexpectedVolumeUnit = {
          ...wasteInquiry,
          stateOfMatter: StateOfMatter.VISCOUS,
          packagingType: WastePackagingType.PACKAGED,
          transportVolumeAmount: 1,
          transportVolumeUnit: WasteMeasurementUnit.TO
        }

        expect(() => validator.validateSubmit(wasteInquiryWithUnexpectedVolumeUnit))
          .toThrow(FieldMustBeNullError)
      })
    })

    describe('loadingType', () => {
      it('throws an error when loadingType doesn\'t exists but required for selected state of matter and packaging type', () => {
        const wasteInquiryWithPackaging = {
          ...wasteInquiry,
          stateOfMatter: StateOfMatter.GASEOUS,
          packagingType: WastePackagingType.BULK,
          transportVolumeAmount: null,
          transportVolumeUnit: null,
          isTransportByIndaver: true,
          loadingType: null
        }

        expect(() => validator.validateSubmit(wasteInquiryWithPackaging))
          .toThrow(MissingRequiredFieldError)
      })

      it('throws an error when loadingType given but expected empty for selected state of matter and packaging type', () => {
        const wasteInquiryWithPackaging = {
          ...wasteInquiry,
          stateOfMatter: StateOfMatter.SOLID,
          packagingType: WastePackagingType.BULK,
          transportType: WasteTransportType.OTHER,
          transportVolumeAmount: 1,
          transportVolumeUnit: WasteMeasurementUnit.TO,
          loadingType: WasteLoadingType.ON_WASTE_COLLECTION
        }

        expect(() => validator.validateSubmit(wasteInquiryWithPackaging))
          .toThrow(FieldMustBeNullError)
      })
    })

    describe('packaging', () => {
      it('throws an error when packaging given but expected empty for selected packaging type', () => {
        const wasteInquiryWithPackaging = {
          ...wasteInquiry,
          stateOfMatter: StateOfMatter.SOLID,
          packagingType: WastePackagingType.BULK,
          packaging: [{
            type: WastePackagingOption.ASF,
            size: '25cm x 25cm x 25xm',
            weightPerPieceValue: 1,
            weightPerPieceUnit: WeightUnit.KG,
            hasInnerPackaging: true,
            remarks: null
          }]
        }

        expect(() => validator.validateSubmit(wasteInquiryWithPackaging))
          .toThrow(FieldMustBeNullError)
      })

      it('throws an error when packaging incomplete', () => {
        const wasteInquiryWithInvalidPackaging = {
          ...wasteInquiry,
          stateOfMatter: StateOfMatter.SOLID,
          packagingType: WastePackagingType.PACKAGED,
          transportVolumeAmount: null,
          transportVolumeUnit: null,
          packaging: [{
            type: null,
            size: null,
            weightPerPieceValue: null,
            weightPerPieceUnit: null,
            hasInnerPackaging: null,
            remarks: null
          }]
        }

        expect(() => validator.validateSubmit(wasteInquiryWithInvalidPackaging))
          .toThrow(MissingRequiredFieldError)
      })
    })
  })
})
