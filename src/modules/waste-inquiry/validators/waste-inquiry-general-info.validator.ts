import { Injectable } from '@nestjs/common'
import { WasteInquiry } from '../entities/waste-inquiry.entity.js'
import { UserCustomerAuthService } from '../../auth/services/user-customer-auth.service.js'
import { CustomerWasteProducerAuthService } from '../../auth/services/customer-waste-producer-auth.service.js'
import { UserWasteProducerAuthService } from '../../auth/services/user-waste-producer-auth.service.js'
import { CustomerPickUpAddressAuthService } from '../../auth/services/customer-pick-up-address-auth.service.js'
import { CustomerNotProvidedError } from '../../customer/errors/customer-not-provided.error.js'
import { WasteProducerNotAccessibleError } from '../../waste-producer/errors/waste-producer-not-accessible.error.js'
import { PickUpAddressNotAccessibleError } from '../../pick-up-address/errors/pick-up-address-not-accessible.error.js'
import { WasteInquiryGeneralInfoValidationUtil } from '../utils/waste-inquiry-general-info-validation.util.js'
import { MissingRequiredFieldError } from '../../exceptions/generic/missing-required-field.error.js'
import { FieldMustBeNullError } from '../../exceptions/generic/field-must-be-null.error.js'
import { AuthContext } from '../../auth/auth.context.js'
import { SelectedCustomerFilterMismatchError } from '../../customer/errors/selected-customer-filter-mismatch.error.js'

@Injectable()
export class WasteInquiryGeneralInfoValidator {
  constructor (
    private readonly authContext: AuthContext,
    private readonly userCustomerAuthService: UserCustomerAuthService,
    private readonly customerWasteProducerAuthService: CustomerWasteProducerAuthService,
    private readonly userWasteProducerAuthService: UserWasteProducerAuthService,
    private readonly customerPickUpAddressAuthService: CustomerPickUpAddressAuthService
  ) {}

  async validateUpdate (
    wasteInquiry: Partial<WasteInquiry>
  ): Promise<void> {
    const validationUtil = new WasteInquiryGeneralInfoValidationUtil(wasteInquiry)

    this.validateCustomer(
      wasteInquiry.customerId ?? null,
      wasteInquiry.customerName ?? null,
      validationUtil,
      false
    )
    this.validateWasteProducer(
      wasteInquiry.wasteProducerId ?? null,
      wasteInquiry.wasteProducerName ?? null,
      validationUtil,
      false
    )
    this.validatePickUpAddress(
      wasteInquiry.pickUpAddressId ?? null,
      wasteInquiry.pickUpAddressName ?? null,
      validationUtil,
      false
    )

    if (wasteInquiry.customerId != null) {
      this.canAccessCustomerId(wasteInquiry.customerId)
    }

    if (wasteInquiry.wasteProducerId != null) {
      await this.canAccessWasteProducerId(
        wasteInquiry.wasteProducerId,
        wasteInquiry.customerId ?? null
      )
    }

    if (wasteInquiry.pickUpAddressId != null) {
      await this.canAccessPickUpAddress(
        wasteInquiry.pickUpAddressId,
        wasteInquiry.customerId ?? null
      )
    }
  }

  async validateSubmit (
    wasteInquiry: WasteInquiry
  ): Promise<void> {
    const validationUtil = new WasteInquiryGeneralInfoValidationUtil(wasteInquiry)

    // Customer
    this.validateCustomer(wasteInquiry.customerId, wasteInquiry.customerName, validationUtil)

    if (wasteInquiry.customerId !== null) {
      this.canAccessCustomerId(wasteInquiry.customerId)
    }

    // Waste producer
    this.validateWasteProducer(
      wasteInquiry.wasteProducerId,
      wasteInquiry.wasteProducerName,
      validationUtil
    )

    if (wasteInquiry.wasteProducerId !== null) {
      await this.canAccessWasteProducerId(
        wasteInquiry.wasteProducerId,
        wasteInquiry.customerId
      )
    }

    // isUnknownWasteProducer
    if (validationUtil.isUnknownWasteProducerRequired) {
      if (wasteInquiry.isUnknownWasteProducer === null) {
        throw new MissingRequiredFieldError({ pointer: '$.isUnknownWasteProducer' })
      }
    }
    if (!validationUtil.isUnknownWasteProducerAllowed) {
      if (wasteInquiry.isUnknownWasteProducer !== null) {
        throw new FieldMustBeNullError({ pointer: '$.isUnknownWasteProducer' })
      }
    }

    // Pick-up address
    this.validatePickUpAddress(
      wasteInquiry.pickUpAddressId,
      wasteInquiry.pickUpAddressName,
      validationUtil
    )

    if (wasteInquiry.pickUpAddressId !== null) {
      await this.canAccessPickUpAddress(
        wasteInquiry.pickUpAddressId,
        wasteInquiry.customerId
      )
    }

    // isUnknownPickUpAddress
    if (validationUtil.isUnknownPickUpAddressRequired) {
      if (wasteInquiry.isUnknownPickUpAddress === null) {
        throw new MissingRequiredFieldError({ pointer: '$.isUnknownPickUpAddress' })
      }
    }
    if (!validationUtil.isUnknownPickUpAddressAllowed) {
      if (wasteInquiry.isUnknownPickUpAddress !== null) {
        throw new FieldMustBeNullError({ pointer: '$.isUnknownPickUpAddress' })
      }
    }
  }

  private validateCustomer (
    customerId: string | null,
    customerName: string | null,
    validationUtil: WasteInquiryGeneralInfoValidationUtil,
    isSubmit: boolean = true
  ) {
    if (!validationUtil.isCustomerIdAllowed) {
      if (customerId !== null) {
        throw new FieldMustBeNullError({ pointer: '$.customerId' })
      }
    }

    if (!validationUtil.isCustomerNameAllowed) {
      if (customerName !== null) {
        throw new FieldMustBeNullError({ pointer: '$.customerName' })
      }
    }

    if (isSubmit && validationUtil.isCustomerIdRequired) {
      if (customerId === null) {
        throw new MissingRequiredFieldError({ pointer: '$.customerId' })
      }
    }

    if (isSubmit && validationUtil.isCustomerNameRequired) {
      if (customerName === null) {
        throw new MissingRequiredFieldError({ pointer: '$.customerName' })
      }
    }
  }

  private validateWasteProducer (
    wasteProducerId: string | null,
    wasteProducerName: string | null,
    validationUtil: WasteInquiryGeneralInfoValidationUtil,
    isSubmit: boolean = true
  ) {
    if (!validationUtil.isWasteProducerIdAllowed) {
      if (wasteProducerId !== null) {
        throw new FieldMustBeNullError({ pointer: '$.wasteProducerId' })
      }
    }

    if (!validationUtil.isWasteProducerNameAllowed) {
      if (wasteProducerName !== null) {
        throw new FieldMustBeNullError({ pointer: '$.wasteProducerName' })
      }
    }

    if (isSubmit && validationUtil.isWasteProducerIdRequired) {
      if (wasteProducerId === null) {
        throw new MissingRequiredFieldError({ pointer: '$.wasteProducerId' })
      }
    }

    if (isSubmit && validationUtil.isWasteProducerNameRequired) {
      if (wasteProducerName === null) {
        throw new MissingRequiredFieldError({ pointer: '$.wasteProducerName' })
      }
    }
  }

  private validatePickUpAddress (
    pickUpAddressId: string | null,
    pickUpAddressName: string | null,
    validationUtil: WasteInquiryGeneralInfoValidationUtil,
    isSubmit: boolean = true
  ) {
    if (!validationUtil.isPickUpAddressIdAllowed) {
      if (pickUpAddressId !== null) {
        throw new FieldMustBeNullError({ pointer: '$.pickUpAddressId' })
      }
    }

    if (!validationUtil.isPickUpAddressNameAllowed) {
      if (pickUpAddressName !== null) {
        throw new FieldMustBeNullError({ pointer: '$.pickUpAddressName' })
      }
    }

    if (isSubmit && validationUtil.isPickUpAddressIdRequired) {
      if (pickUpAddressId === null) {
        throw new MissingRequiredFieldError({ pointer: '$.pickUpAddressId' })
      }
    }

    if (isSubmit && validationUtil.isPickUpAddressNameRequired) {
      if (pickUpAddressName === null) {
        throw new MissingRequiredFieldError({ pointer: '$.pickUpAddressName' })
      }
    }
  }

  private canAccessCustomerId (customerId: string): void {
    const selectedCustomerId = this.authContext.getSelectedCustomerId()
    if (selectedCustomerId != null && selectedCustomerId !== customerId) {
      throw new SelectedCustomerFilterMismatchError({ pointer: '$.customerId' })
    }
  }

  private async canAccessWasteProducerId (
    wasteProducerId: string,
    customerId: string | null
  ): Promise<void> {
    const pointer = '$.wasteProducerId'

    if (customerId === null) {
      throw new CustomerNotProvidedError({ pointer })
    }
    const userId = this.authContext.getAzureEntraUpn()

    const canCustomerAccessWasteProducer = await this.customerWasteProducerAuthService
      .canCustomerAccessWasteProducer(
        customerId,
        wasteProducerId
      )

    if (!canCustomerAccessWasteProducer) {
      throw new WasteProducerNotAccessibleError({ pointer })
    }

    const canUserAccessWasteProducer = await this.userWasteProducerAuthService
      .canUserAccessWasteProducer(
        userId,
        customerId,
        wasteProducerId
      )

    if (!canUserAccessWasteProducer) {
      throw new WasteProducerNotAccessibleError({ pointer })
    }
  }

  private async canAccessPickUpAddress (
    pickUpAddressId: string,
    customerId: string | null
  ): Promise<void> {
    const pointer = '$.pickUpAddressIds'

    if (customerId === null) {
      throw new CustomerNotProvidedError({ pointer })
    }

    const canCustomerAccessPickUpAddress = await this.customerPickUpAddressAuthService
      .canCustomerAccessPickUpAddress(
        customerId,
        pickUpAddressId
      )

    if (!canCustomerAccessPickUpAddress) {
      throw new PickUpAddressNotAccessibleError({ pointer })
    }
  }
}
