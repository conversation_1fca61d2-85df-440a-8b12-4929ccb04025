import { Injectable } from '@nestjs/common'
import { SapGetWasteInquiryIndexResponse } from '../../sap/use-cases/get-waste-inquiry-index/get-waste-inquiry-index.response.js'
import { AuthContext } from '../../auth/auth.context.js'
import { UserWasteProducerAuthService } from '../../auth/services/user-waste-producer-auth.service.js'
import { WasteInquiryNotFoundError } from '../errors/waste-inquiry-not-found-error.js'

@Injectable()
export class WasteInquirySapValidator {
  constructor (
    private readonly authContext: AuthContext,
    private readonly userWasteProducerAuthService: UserWasteProducerAuthService
  ) {}

  async validate (wasteInquiry: SapGetWasteInquiryIndexResponse): Promise<void> {
    await this.validateWasteInquiryAccessible(wasteInquiry)
  }

  private async validateWasteInquiryAccessible (
    wasteInquiry: SapGetWasteInquiryIndexResponse
  ): Promise<void> {
    if (wasteInquiry.Customer === undefined || wasteInquiry.Customer === '') {
      return
    }

    const selectedCustomerId = this.authContext.getSelectedCustomerId()
    if (selectedCustomerId != null && selectedCustomerId !== wasteInquiry.Customer) {
      throw new WasteInquiryNotFoundError({ inquiryNumber: wasteInquiry.InquiryNumber ?? 'InquiryNumber not given' })
    }
    if (wasteInquiry.WasteProducer === undefined || wasteInquiry.WasteProducer === '') {
      return
    }

    const userId = this.authContext.getAzureEntraUpn()
    const canUserAccessWasteProducer = await this.userWasteProducerAuthService
      .canUserAccessWasteProducer(
        userId,
        wasteInquiry.Customer,
        wasteInquiry.WasteProducer
      )
    if (!canUserAccessWasteProducer) {
      throw new WasteInquiryNotFoundError({ inquiryNumber: wasteInquiry.InquiryNumber ?? 'InquiryNumber not given' })
    }
  }
}
