import { ApiProperty } from '@nestjs/swagger'
import { PackingGroup } from '../enums/packaging-group.enum.js'

export class WasteInquiryUnNumberResponse {
  @ApiProperty({ type: String, nullable: true })
  unNumber: string | null

  @ApiProperty({ type: Number, nullable: true, enum: PackingGroup, enumName: 'PackingGroup' })
  packingGroup: PackingGroup | null

  @ApiProperty({ type: Boolean })
  isHazardous: boolean
}
