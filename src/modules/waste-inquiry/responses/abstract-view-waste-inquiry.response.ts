import assert from 'assert'
import { ApiProperty } from '@nestjs/swagger'
import { CustomerResponse } from '../../customer/responses/customer.response.js'
import { WasteProducerResponse } from '../../waste-producer/responses/waste-producer.response.js'
import { PickUpAddressResponse } from '../../pick-up-address/responses/pick-up-address.response.js'
import { StateOfMatter } from '../enums/state-of-matter.enum.js'
import { WastePackagingType } from '../enums/waste-packaging-type.enum.js'
import { WasteFlashpointOption } from '../enums/waste-flashpoint-option.enum.js'
import { WastePhOption } from '../enums/waste-ph-option.enum.js'
import { StableTemperatureType } from '../enums/stable-temperature-type.enum.js'
import { FileLinkResponse } from '../../files/responses/file-link.response.js'
import { WasteLegislationOption } from '../enums/waste-legislation-option.enum.js'
import { SvhcExtraOption } from '../enums/svhc-extra-option.enum.js'
import { WastePropertyOption } from '../enums/waste-property-option.enum.js'
import { WasteMeasurementUnit } from '../enums/waste-measurement-unit.enum.js'
import { WasteDischargeFrequency } from '../enums/waste-discharge-frequency.enum.js'
import { RegulatedTransportOption } from '../enums/regulated-transport-option.enum.js'
import { WasteTransportType } from '../enums/waste-transport-type.enum.js'
import { ContainerLoadingType } from '../enums/container-loading-type.enum.js'
import { WasteLoadingType } from '../enums/waste-loading-type.enum.js'
import { WasteTransportInOption } from '../enums/waste-transport-in-option.enum.js'
import { WasteLoadingMethod } from '../enums/waste-loading-method.enum.js'
import { WasteStoredInOption } from '../enums/waste-stored-in-option.enum.js'
import { CollectionRequirementOption } from '../enums/collection-requirement-option.enum.js'
import { ContactTypeResponse } from '../../contact/responses/contact.response.js'
import { WasteInquiry } from '../entities/waste-inquiry.entity.js'
import { UniformFullWasteInquiry } from '../types/uniform-waste-inquiry-detail.type.js'
import { WasteInquiryStatus } from '../enums/waste-inquiry-status.enum.js'
import { UniformWasteInquiry } from '../types/uniform-waste-inquiry.type.js'
import { FileLink } from '../../files/entities/file-link.entity.js'
import { ExternalFile } from '../../files/types/external-file.type.js'
import { WastePackagingResponse } from './waste-packaging.response.js'
import { WasteInquiryUnNumberResponse } from './un-number.response.js'
import { WasteCompositionResponse } from './waste-composition.response.js'

export abstract class AbstractViewWasteInquiryResponse {
  @ApiProperty({ type: String, enum: WasteInquiryStatus, enumName: 'WasteInquiryStatus' })
  status: WasteInquiryStatus

  @ApiProperty({ type: CustomerResponse, nullable: true })
  customer: CustomerResponse | null

  @ApiProperty({ type: WasteProducerResponse, nullable: true })
  wasteProducer: WasteProducerResponse | null

  @ApiProperty({ type: PickUpAddressResponse, nullable: true })
  pickUpAddress: PickUpAddressResponse | null

  @ApiProperty({ type: String, nullable: true })
  wasteStreamName: string | null

  @ApiProperty({ type: String, nullable: true })
  wasteStreamDescription: string | null

  @ApiProperty({ type: String, nullable: true })
  ewcLevel1Name: string | null

  @ApiProperty({ type: String, nullable: true })
  ewcLevel2Name: string | null

  @ApiProperty({ type: String, nullable: true })
  ewcLevel3Name: string | null

  @ApiProperty({ type: String, nullable: true, enum: StateOfMatter, enumName: 'StateOfMatter' })
  stateOfMatter: string | null

  @ApiProperty({ type: String, nullable: true, enum: WastePackagingType, enumName: 'WastePackagingType' })
  packagingType: string | null

  @ApiProperty({ type: String, nullable: true, enum: WasteFlashpointOption, enumName: 'WasteFlashpointOption' })
  flashpoint: string | null

  @ApiProperty({ type: String, nullable: true, enum: WastePhOption, enumName: 'WastePhOption' })
  ph: string | null

  @ApiProperty({ type: Number, nullable: true, multipleOf: 0.01 })
  specificGravity: number | null

  @ApiProperty({ type: String, nullable: true, enum: StableTemperatureType, enumName: 'StableTemperatureType' })
  stableTemperatureType: string | null

  @ApiProperty({ type: Number, nullable: true, multipleOf: 0.01 })
  minStableTemperature: number | null

  @ApiProperty({ type: Number, nullable: true, multipleOf: 0.01 })
  maxStableTemperature: number | null

  @ApiProperty({ type: Number, nullable: true, multipleOf: 0.01 })
  averageStableTemperature: number | null

  @ApiProperty({ type: FileLinkResponse, isArray: true })
  sdsFiles: FileLinkResponse[]

  @ApiProperty({ type: Boolean })
  noSds: boolean

  @ApiProperty({ type: FileLinkResponse, isArray: true })
  analysisReportFiles: FileLinkResponse[]

  @ApiProperty({ type: Boolean })
  noAnalysisReport: boolean

  @ApiProperty({ type: WasteCompositionResponse, isArray: true })
  composition: WasteCompositionResponse[]

  @ApiProperty({ type: Boolean, nullable: true })
  isSampleAvailable: boolean | null

  @ApiProperty({ type: String, isArray: true, enum: WasteLegislationOption, enumName: 'WasteLegislationOption' })
  selectedLegislationOptions: string[]

  @ApiProperty({ type: String, nullable: true, enum: SvhcExtraOption, enumName: 'SvhcExtraOption' })
  svhcExtra: string | null

  @ApiProperty({ type: String, nullable: true })
  legislationRemarks: string | null

  @ApiProperty({ type: String, isArray: true, enum: WastePropertyOption, enumName: 'WastePropertyOption' })
  selectedPropertyOptions: string[]

  @ApiProperty({ type: String, nullable: true })
  propertyRemarks: string | null

  @ApiProperty({ type: Number, nullable: true, multipleOf: 0.01 })
  expectedYearlyVolumeAmount: number | null

  @ApiProperty({ type: String, nullable: true, enum: WasteMeasurementUnit, enumName: 'WasteMeasurementUnit' })
  expectedYearlyVolumeUnit: string | null

  @ApiProperty({ type: Number, nullable: true, multipleOf: 0.01 })
  expectedPerCollectionQuantity: number | null

  @ApiProperty({ type: String, nullable: true, enum: WasteMeasurementUnit, enumName: 'WasteMeasurementUnit' })
  expectedPerCollectionUnit: string | null

  @ApiProperty({ type: String, nullable: true, enum: WasteDischargeFrequency, enumName: 'WasteDischargeFrequency' })
  dischargeFrequency: string | null

  @ApiProperty({ type: String, nullable: true, format: 'date' })
  firstCollectionDate: string | null

  @ApiProperty({ type: String, nullable: true, format: 'date' })
  expectedEndDate: string | null

  @ApiProperty({ type: String, nullable: true })
  collectionRemarks: string | null

  @ApiProperty({ type: Boolean, nullable: true })
  isTransportByIndaver: boolean | null

  @ApiProperty({ type: Boolean, nullable: true })
  isLoadingByIndaver: boolean | null

  @ApiProperty({ type: String, nullable: true, enum: RegulatedTransportOption, enumName: 'RegulatedTransportOption' })
  isRegulatedTransport: string | null

  @ApiProperty({ type: WasteInquiryUnNumberResponse, isArray: true })
  unNumbers: WasteInquiryUnNumberResponse[]

  @ApiProperty({ type: WastePackagingResponse, isArray: true })
  packaging: WastePackagingResponse[]

  @ApiProperty({ type: String, nullable: true, enum: WasteTransportType, enumName: 'WasteTransportType' })
  transportType: string | null

  @ApiProperty({ type: String, nullable: true, enum: ContainerLoadingType, enumName: 'ContainerLoadingType' })
  containerLoadingType: string | null

  @ApiProperty({ type: String, nullable: true, enum: WasteLoadingType, enumName: 'WasteLoadingType' })
  loadingType: string | null

  @ApiProperty({ type: String, nullable: true, enum: WasteTransportInOption, enumName: 'WasteTransportInOption' })
  transportIn: string | null

  @ApiProperty({ type: String, nullable: true, enum: WasteLoadingMethod, enumName: 'WasteLoadingMethod' })
  loadingMethod: string | null

  @ApiProperty({ type: String, nullable: true, enum: WasteStoredInOption, enumName: 'WasteStoredInOption' })
  storedIn: string | null

  @ApiProperty({ type: Number, nullable: true, multipleOf: 0.01 })
  transportVolumeAmount: number | null

  @ApiProperty({ type: String, nullable: true, enum: WasteMeasurementUnit, enumName: 'WasteMeasurementUnit' })
  transportVolumeUnit: string | null

  @ApiProperty({ type: Boolean, nullable: true })
  isTankOwnedByCustomer: boolean | null

  @ApiProperty({ type: String, nullable: true, enum: CollectionRequirementOption, enumName: 'CollectionRequirementOption' })
  collectionRequirements: CollectionRequirementOption | null

  @ApiProperty({ type: String, nullable: true })
  remarks: string | null

  @ApiProperty({ type: ContactTypeResponse, isArray: true })
  sendCopyToContacts: ContactTypeResponse[]

  @ApiProperty({ type: FileLinkResponse, isArray: true })
  additionalFiles: FileLinkResponse[]

  @ApiProperty({ type: String, nullable: true, format: 'date-time' })
  submittedOn: string | null

  @ApiProperty({ type: String, nullable: true })
  hazardInducer1: string | null

  @ApiProperty({ type: String, nullable: true })
  hazardInducer2: string | null

  @ApiProperty({ type: String, nullable: true })
  hazardInducer3: string | null

  constructor (wasteInquiry: WasteInquiry | UniformFullWasteInquiry) {
    assert(wasteInquiry.customer !== undefined)
    assert(wasteInquiry.wasteProducer !== undefined)
    assert(wasteInquiry.pickUpAddress !== undefined)
    assert(wasteInquiry.sdsFiles !== undefined)
    assert(wasteInquiry.analysisReportFiles !== undefined)
    assert(wasteInquiry.additionalFiles !== undefined)

    this.status = wasteInquiry instanceof UniformWasteInquiry
      || wasteInquiry instanceof UniformFullWasteInquiry
      ? wasteInquiry.status
      : WasteInquiryStatus.DRAFT
    this.customer = wasteInquiry.customer !== null
      ? new CustomerResponse(wasteInquiry.customer)
      : null
    this.wasteProducer = wasteInquiry.wasteProducer !== null
      ? new WasteProducerResponse(wasteInquiry.wasteProducer)
      : null
    this.pickUpAddress = wasteInquiry.pickUpAddress !== null
      ? new PickUpAddressResponse(wasteInquiry.pickUpAddress)
      : null
    this.wasteStreamName = wasteInquiry.wasteStreamName
    this.wasteStreamDescription = wasteInquiry.wasteStreamDescription
    this.ewcLevel1Name = wasteInquiry.ewcLevel1
    this.ewcLevel2Name = wasteInquiry.ewcLevel2
    this.ewcLevel3Name = wasteInquiry.ewcLevel3
    this.expectedYearlyVolumeAmount = wasteInquiry.expectedYearlyVolumeAmount
    this.expectedYearlyVolumeUnit = wasteInquiry.expectedYearlyVolumeUnit
    this.stateOfMatter = wasteInquiry.stateOfMatter
    this.packagingType = wasteInquiry.packagingType
    this.flashpoint = wasteInquiry.flashpoint
    this.ph = wasteInquiry.ph
    this.specificGravity = wasteInquiry.specificGravity
    this.stableTemperatureType = wasteInquiry.stableTemperatureType
    this.minStableTemperature = wasteInquiry.minStableTemperature
    this.maxStableTemperature = wasteInquiry.maxStableTemperature
    this.averageStableTemperature = wasteInquiry.averageStableTemperature
    this.sdsFiles = wasteInquiry.sdsFiles
      .map((file: FileLink | ExternalFile) => new FileLinkResponse(file))
    this.noSds = wasteInquiry.noSds
    this.analysisReportFiles = wasteInquiry.analysisReportFiles
      .map((file: FileLink | ExternalFile) => new FileLinkResponse(file))
    this.noAnalysisReport = wasteInquiry.noAnalysisReport
    this.composition = wasteInquiry.composition
    this.isSampleAvailable = wasteInquiry.isSampleAvailable
    this.selectedLegislationOptions = wasteInquiry.selectedLegislationOptions
    this.svhcExtra = wasteInquiry.svhcExtra
    this.legislationRemarks = wasteInquiry.legislationRemarks
    this.selectedPropertyOptions = wasteInquiry.selectedPropertyOptions
    this.propertyRemarks = wasteInquiry.propertyRemarks
    this.expectedYearlyVolumeAmount = wasteInquiry.expectedYearlyVolumeAmount
    this.expectedYearlyVolumeUnit = wasteInquiry.expectedYearlyVolumeUnit
    this.expectedPerCollectionQuantity = wasteInquiry.expectedPerCollectionQuantity
    this.expectedPerCollectionUnit = wasteInquiry.expectedPerCollectionUnit
    this.dischargeFrequency = wasteInquiry.dischargeFrequency
    this.firstCollectionDate = wasteInquiry.firstCollectionDate
    this.expectedEndDate = wasteInquiry.expectedEndDate
    this.collectionRemarks = wasteInquiry.collectionRemarks
    this.isTransportByIndaver = wasteInquiry.isTransportByIndaver
    this.isLoadingByIndaver = wasteInquiry.isLoadingByIndaver
    this.isRegulatedTransport = wasteInquiry.isRegulatedTransport
    this.unNumbers = wasteInquiry.unNumbers
    this.transportType = wasteInquiry.transportType
    this.packaging = wasteInquiry.packaging
    this.containerLoadingType = wasteInquiry.containerLoadingType
    this.loadingType = wasteInquiry.loadingType
    this.transportIn = wasteInquiry.transportIn
    this.loadingMethod = wasteInquiry.loadingMethod
    this.storedIn = wasteInquiry.storedIn
    this.transportVolumeAmount = wasteInquiry.transportVolumeAmount
    this.transportVolumeUnit = wasteInquiry.transportVolumeUnit
    this.isTankOwnedByCustomer = wasteInquiry.isTankOwnedByCustomer
    this.collectionRequirements = wasteInquiry.collectionRequirements
    this.remarks = wasteInquiry.remarks
    this.sendCopyToContacts = wasteInquiry.sendCopyToContacts
    this.additionalFiles = wasteInquiry.additionalFiles
      .map((file: FileLink | ExternalFile) => new FileLinkResponse(file))
    this.submittedOn = wasteInquiry.submittedOn != null
      ? wasteInquiry.submittedOn.toISOString()
      : null
    this.hazardInducer1 = wasteInquiry.hazardInducer1
    this.hazardInducer2 = wasteInquiry.hazardInducer2
    this.hazardInducer3 = wasteInquiry.hazardInducer3
  }
}
