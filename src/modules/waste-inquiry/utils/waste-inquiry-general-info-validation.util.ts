import { WasteInquiry } from '../entities/waste-inquiry.entity.js'

export class WasteInquiryGeneralInfoValidationUtil {
  private readonly wasteInquiry: Partial<WasteInquiry>

  constructor (wasteInquiry: Partial<WasteInquiry>) {
    this.wasteInquiry = wasteInquiry
  }

  // Customer
  get isCustomerIdAllowed (): boolean {
    return this.wasteInquiry.customerName !== null
  }

  get isCustomerIdRequired (): boolean {
    return this.isCustomerIdAllowed
  }

  get isCustomerNameAllowed (): boolean {
    return this.wasteInquiry.customerId !== null
  }

  get isCustomerNameRequired (): boolean {
    return this.isCustomerNameAllowed
  }

  // Waste producer
  get isWasteProducerIdAllowed (): boolean {
    return this.wasteInquiry.isUnknownWasteProducer === false
      && this.wasteInquiry.wasteProducerName !== null
  }

  get isWasteProducerIdRequired (): boolean {
    return this.wasteInquiry.isUnknownWasteProducer === false
      || this.wasteInquiry.wasteProducerName !== null
  }

  get isWasteProducerNameAllowed (): boolean {
    return this.wasteInquiry.isUnknownWasteProducer === false
      && this.wasteInquiry.wasteProducerId !== null
  }

  get isWasteProducerNameRequired (): boolean {
    return this.wasteInquiry.isUnknownWasteProducer === false
      || this.wasteInquiry.wasteProducerId !== null
  }

  // Unknown waste producer
  get isUnknownWasteProducerAllowed (): boolean {
    return true
  }

  get isUnknownWasteProducerRequired (): boolean {
    return this.isUnknownWasteProducerAllowed
  }

  // Pick-up address
  get isPickUpAddressIdAllowed (): boolean {
    return this.wasteInquiry.isUnknownPickUpAddress === false
      && this.wasteInquiry.pickUpAddressName !== null
  }

  get isPickUpAddressIdRequired (): boolean {
    return this.wasteInquiry.isUnknownPickUpAddress === false
      || this.wasteInquiry.pickUpAddressName !== null
  }

  get isPickUpAddressNameAllowed (): boolean {
    return this.wasteInquiry.isUnknownPickUpAddress === false
      && this.wasteInquiry.pickUpAddressId !== null
  }

  get isPickUpAddressNameRequired (): boolean {
    return this.wasteInquiry.isUnknownPickUpAddress === false
      || this.wasteInquiry.pickUpAddressId !== null
  }

  // Unknown pick-up address
  get isUnknownPickUpAddressAllowed (): boolean {
    return true
  }

  get isUnknownPickUpAddressRequired (): boolean {
    return this.isUnknownPickUpAddressAllowed
  }
}
