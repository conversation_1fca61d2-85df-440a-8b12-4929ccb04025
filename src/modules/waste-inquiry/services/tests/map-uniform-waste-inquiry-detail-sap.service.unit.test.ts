import { before, describe, it } from 'node:test'
import { expect } from 'expect'
import { TestBench } from '../../../../../test/setup/test-bench.js'
import { SapGetWasteInquiryIndexResponse } from '../../../sap/use-cases/get-waste-inquiry-index/get-waste-inquiry-index.response.js'
import { StateOfMatter } from '../../enums/state-of-matter.enum.js'
import { WastePackagingType } from '../../enums/waste-packaging-type.enum.js'
import { WasteFlashpointOption } from '../../enums/waste-flashpoint-option.enum.js'
import { WastePhOption } from '../../enums/waste-ph-option.enum.js'
import { StableTemperatureType } from '../../enums/stable-temperature-type.enum.js'
import { SvhcExtraOption } from '../../enums/svhc-extra-option.enum.js'
import { WasteMeasurementUnit } from '../../enums/waste-measurement-unit.enum.js'
import { WasteDischargeFrequency } from '../../enums/waste-discharge-frequency.enum.js'
import { RegulatedTransportOption } from '../../enums/regulated-transport-option.enum.js'
import { WasteTransportInOption } from '../../enums/waste-transport-in-option.enum.js'
import { WasteLoadingType } from '../../enums/waste-loading-type.enum.js'
import { WasteLoadingMethod } from '../../enums/waste-loading-method.enum.js'
import { WasteStoredInOption } from '../../enums/waste-stored-in-option.enum.js'
import { CollectionRequirementOption } from '../../enums/collection-requirement-option.enum.js'
import { MapUniformWasteInquiryDetailSapService } from '../map-uniform-waste-inquiry-detail-sap.service.js'
import { WasteInquiryStatus } from '../../enums/waste-inquiry-status.enum.js'

describe('Map uniform waste inquiry detail SAP unit test', () => {
  before(() => {
    TestBench.setupUnitTest()
  })

  it('Maps a SAP waste inquiry response to a uniform waste inquiry detail', () => {
    const wasteInquiry: SapGetWasteInquiryIndexResponse = {
      NewRequestStatus: 'K',
      InquiryNumber: '123456',
      Contract: '1234567890',
      ContractItem: '0001',
      WasteName: 'Waste Stream Name',
      EwcCustomerLevel1: '01',
      EwcCustomerLevel2: '02',
      EwcCustomerLevel3: '03',
      RequestorName: 'John Doe',
      Customer: '50000000',
      CustomerName: 'Indaver Site Hooge Maey',
      CustomerAddress: 'Moerstraat 99, 2030 Antwerpen',
      WasteProducer: '60000000',
      WasteProducerName: 'Indaver',
      WasteProducerAddress: 'Pottery Road, Dublin A96 KW29',
      PickUpAddress: '70000000',
      PickUpAddressName: 'Indaver Site Hooge Maey',
      PickUpAddressAddress: 'Moerstraat 99, 2030 Antwerpen',
      StateOfMatter: '01',
      WasteType: '1',
      Flashpoint: '1',
      Ph: '1',
      Gravity: 11.5,
      Temperature: '2',
      MinTemperature: 5,
      MaxTemperature: 21,
      Sds: false,
      AnalysisReport: false,
      Sample: false,
      TypeOfSvhc: '2',
      PersistentSubstance: '2',
      CommentSubjectLegislation: 'Legislation remarks',
      CommentSubjectHazard: 'Hazard remarks',
      QuantityYear: 100,
      QuantityYearUom: 'M3',
      CollectionQuantity: 100,
      CollectionUom: 'M3',
      FrequencyDischarge: '3',
      CollectionDate: '2023-10-01',
      ExpectedEnd: null,
      InformationDelivery: 'Collection remarks',
      TransportArranged: true,
      LoadingByIndaver: true,
      TransportRegulated: 'U',
      TransportType: '1',
      LoadingType: '1',
      LoadingMethod: '1',
      StoredIn: '1',
      Volume: 100,
      VolumeUom: 'M3',
      TankOwnedCustomer: true,
      RequiredForCollection: '3',
      RequestDescription: 'General remarks',
      HazardInducer1: '123',
      HazardInducer2: '124',
      HazardInducer3: '125',
      FirstNameCustomerZone: 'John',
      LastNameCustomerZone: 'Doe'
    }

    const result = MapUniformWasteInquiryDetailSapService.mapResultToUniformWasteInquiry(
      wasteInquiry
    )

    expect(result).toMatchObject({
      status: WasteInquiryStatus.COMPLETED,
      inquiryNumber: '123456',
      contractNumber: '1234567890',
      contractItem: '0001',
      wasteStreamName: 'Waste Stream Name',
      ewcLevel1: '01',
      ewcLevel2: '02',
      ewcLevel3: '03',
      customer: {
        id: '50000000',
        name: 'Indaver Site Hooge Maey',
        address: 'Moerstraat 99, 2030 Antwerpen'
      },
      wasteProducer: {
        id: '60000000',
        name: 'Indaver',
        address: 'Pottery Road, Dublin A96 KW29'
      },
      pickUpAddress: {
        id: '70000000',
        name: 'Indaver Site Hooge Maey',
        address: 'Moerstraat 99, 2030 Antwerpen'
      },
      stateOfMatter: StateOfMatter.GASEOUS,
      packagingType: WastePackagingType.BULK,
      flashpoint: WasteFlashpointOption.LESS_THAN_23,
      ph: WastePhOption.LESS_THAN_2,
      specificGravity: 11.5,
      stableTemperatureType: StableTemperatureType.OTHER,
      minStableTemperature: 5,
      maxStableTemperature: 21,
      averageStableTemperature: null,
      sdsFiles: [],
      noSds: true,
      analysisReportFiles: [],
      noAnalysisReport: true,
      composition: [],
      isSampleAvailable: false,
      selectedLegislationOptions: [],
      svhcExtra: SvhcExtraOption.MORE_THAN_1,
      legislationRemarks: 'Legislation remarks',
      selectedPropertyOptions: [],
      propertyRemarks: 'Hazard remarks',
      expectedYearlyVolumeAmount: 100,
      expectedYearlyVolumeUnit: WasteMeasurementUnit.M3,
      expectedPerCollectionQuantity: 100,
      expectedPerCollectionUnit: WasteMeasurementUnit.M3,
      dischargeFrequency: WasteDischargeFrequency.ONCE_OFF_CAMPAIGN,
      firstCollectionDate: '2023-10-01',
      expectedEndDate: null,
      collectionRemarks: 'Collection remarks',
      isTransportByIndaver: true,
      isLoadingByIndaver: true,
      isRegulatedTransport: RegulatedTransportOption.UNKNOWN,
      unNumbers: [],
      packaging: [],
      transportType: null,
      transportIn: WasteTransportInOption.TANK_TRAILER,
      containerLoadingType: null,
      loadingType: WasteLoadingType.ON_WASTE_COLLECTION,
      loadingMethod: WasteLoadingMethod.GRAVITATIONAL,
      storedIn: WasteStoredInOption.STORAGE_TANK,
      transportVolumeAmount: 100,
      transportVolumeUnit: WasteMeasurementUnit.M3,
      isTankOwnedByCustomer: true,
      collectionRequirements: CollectionRequirementOption.TRACTOR_TRAILER_TANK,
      remarks: 'General remarks',
      additionalFiles: [],
      createdBy: 'John Doe',
      hazardInducer1: wasteInquiry.HazardInducer1,
      hazardInducer2: wasteInquiry.HazardInducer2,
      hazardInducer3: wasteInquiry.HazardInducer3
    })
  })
})
