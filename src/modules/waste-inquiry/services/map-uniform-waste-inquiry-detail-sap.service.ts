import dayjs from 'dayjs'
import { SapCustomer } from '../../customer/types/sap-customer.type.js'
import { SapPickUpAddress } from '../../pick-up-address/types/sap-pick-up-address.type.js'
import { SapFlashpoint, SapFrequencyDischarge, SapLoadingMethod, SapLoadingType, SapPersistentSubstance, SapPh, SapRequiredForCollection, SapStateOfMatter, SapStoredIn, SapTemperature, SapTransportRegulated, SapTransportType, SapTypeOfSvhc, SapWasteType } from '../../sap/use-cases/create-waste-inquiry/create-waste-inquiry-sap.command.js'
import { SapGetWasteInquiryIndexResponse } from '../../sap/use-cases/get-waste-inquiry-index/get-waste-inquiry-index.response.js'
import { SapWasteProducer } from '../../waste-producer/types/sap-waste-producer.type.js'
import { mapSapValueToCollectionRequirementOption } from '../enums/collection-requirement-option.enum.js'
import { mapSapValueToContainerLoadingType } from '../enums/container-loading-type.enum.js'
import { mapSapValueToRegulatedTransportOption } from '../enums/regulated-transport-option.enum.js'
import { mapSapValueToStableTemperatureType, StableTemperatureType } from '../enums/stable-temperature-type.enum.js'
import { mapSapValueToStateOfMatter } from '../enums/state-of-matter.enum.js'
import { mapSapValueToSvhcExtraOption } from '../enums/svhc-extra-option.enum.js'
import { mapSapValueToWasteDischargeFrequency } from '../enums/waste-discharge-frequency.enum.js'
import { mapSapValueToWasteFlashpointOption } from '../enums/waste-flashpoint-option.enum.js'
import { mapSapValueToWasteLoadingMethod } from '../enums/waste-loading-method.enum.js'
import { mapSapValueToWasteLoadingType } from '../enums/waste-loading-type.enum.js'
import { mapSapValueToWasteMeasurementUnit } from '../enums/waste-measurement-unit.enum.js'
import { mapSapValueToWastePackagingType } from '../enums/waste-packaging-type.enum.js'
import { mapSapValueToWastePhOption } from '../enums/waste-ph-option.enum.js'
import { mapSapValueToWasteStoredInOption } from '../enums/waste-stored-in-option.enum.js'
import { mapSapValueToWasteTransportInOption } from '../enums/waste-transport-in-option.enum.js'
import { mapSapValueToWasteTransportType } from '../enums/waste-transport-type.enum.js'
import { UniformFullWasteInquiry } from '../types/uniform-waste-inquiry-detail.type.js'
import { mapSapValueToWasteInquiryStatus, WasteInquiryStatus } from '../enums/waste-inquiry-status.enum.js'
import { Contact } from '../../contact/types/contact.type.js'
import { mapSapValueToWastePackagingOption } from '../enums/waste-packaging-option.enum.js'
import { WeightUnit } from '../enums/weight-unit.enum.js'
import { mapSapValueToPackingGroup } from '../enums/packaging-group.enum.js'
import { mapSapValueToWasteLegislationOption } from '../enums/waste-legislation-option.enum.js'
import { mapSapValueToWastePropertyOption } from '../enums/waste-property-option.enum.js'
import { SapFile } from '../../sap-files/entities/sap-file.entity.js'
import { EntityPart, WasteInquiryEntityPart } from '../../files/enums/entity-part.enum.js'
import { ExternalFile } from '../../files/types/external-file.type.js'

export class MapUniformWasteInquiryDetailSapService {
  static mapResultToUniformWasteInquiry (
    response: SapGetWasteInquiryIndexResponse,
    sapFiles: SapFile[] = []
  ): UniformFullWasteInquiry {
    const uniformWasteInquiry = new UniformFullWasteInquiry()

    uniformWasteInquiry.status = response.NewRequestStatus !== undefined
      ? mapSapValueToWasteInquiryStatus(response.NewRequestStatus)
      : WasteInquiryStatus.NEW
    uniformWasteInquiry.inquiryNumber = response.InquiryNumber ?? null
    uniformWasteInquiry.contractNumber = response.Contract ?? null
    uniformWasteInquiry.contractItem = response.ContractItem ?? null
    uniformWasteInquiry.customer = this.mapCustomer(response)
    uniformWasteInquiry.wasteProducer = this.mapWasteProducer(response)
    uniformWasteInquiry.pickUpAddress = this.mapPickUpAddress(response)
    uniformWasteInquiry.wasteStreamName = response.WasteName ?? null
    uniformWasteInquiry.wasteStreamDescription = response.WasteMaterialDescription ?? null
    uniformWasteInquiry.ewcLevel1 = response.EwcCustomerLevel1 ?? null
    uniformWasteInquiry.ewcLevel2 = response.EwcCustomerLevel2 ?? null
    uniformWasteInquiry.ewcLevel3 = response.EwcCustomerLevel3 ?? null
    uniformWasteInquiry.stateOfMatter = response.StateOfMatter != null
      ? mapSapValueToStateOfMatter(response.StateOfMatter as SapStateOfMatter)
      : null
    uniformWasteInquiry.packagingType = response.WasteType != null
      ? mapSapValueToWastePackagingType(response.WasteType as SapWasteType)
      : null
    uniformWasteInquiry.flashpoint = response.Flashpoint != null
      ? mapSapValueToWasteFlashpointOption(response.Flashpoint as SapFlashpoint)
      : null
    uniformWasteInquiry.ph = response.Ph != null
      ? mapSapValueToWastePhOption(response.Ph as SapPh)
      : null
    uniformWasteInquiry.specificGravity = response.Gravity ?? null
    const temperatureData = this.mapTemperature(response)
    uniformWasteInquiry.stableTemperatureType = temperatureData.stableTemperatureType
    uniformWasteInquiry.minStableTemperature = temperatureData.minStableTemperature
    uniformWasteInquiry.maxStableTemperature = temperatureData.maxStableTemperature
    uniformWasteInquiry.averageStableTemperature = temperatureData.averageStableTemperature
    uniformWasteInquiry.sdsFiles = this.getExternalFiles(EntityPart.SDS, sapFiles)
    uniformWasteInquiry.noSds = response.Sds != null
      ? response.Sds === false
      : true
    uniformWasteInquiry.analysisReportFiles = this.getExternalFiles(
      EntityPart.ANALYSIS_REPORT, sapFiles
    )
    uniformWasteInquiry.noAnalysisReport = response.AnalysisReport != null
      ? response.AnalysisReport === false
      : true
    uniformWasteInquiry.composition = (response._component ?? []).map(component => ({
      name: component.Component ?? null,
      minWeight: component.MinWeight ?? null,
      maxWeight: component.MaxWeight ?? null
    }))
    uniformWasteInquiry.isSampleAvailable = response.Sample ?? null
    uniformWasteInquiry.selectedLegislationOptions = (response._wasteSubjectLegislation ?? [])
      .filter(legislation =>
        legislation.WasteSubjectCodeValid === true
        && legislation.WasteSubjectCode !== undefined
      )
      .map(legislation => (mapSapValueToWasteLegislationOption(legislation.WasteSubjectCode!)))
      .filter(option => option !== null)
    uniformWasteInquiry.svhcExtra = response.TypeOfSvhc != null
      ? mapSapValueToSvhcExtraOption(
          response.TypeOfSvhc as SapTypeOfSvhc,
          response.PersistentSubstance as SapPersistentSubstance ?? null
        )
      : null
    uniformWasteInquiry.legislationRemarks = response.CommentSubjectLegislation ?? null
    uniformWasteInquiry.selectedPropertyOptions = (response._wasteSubjectHazard ?? [])
      .filter(option =>
        option.WasteSubjectCodeValid === true
        && option.WasteSubjectCode !== undefined
      )
      .map(option => (mapSapValueToWastePropertyOption(option.WasteSubjectCode!)))
      .filter(option => option !== null)
    uniformWasteInquiry.propertyRemarks = response.CommentSubjectHazard ?? null
    uniformWasteInquiry.expectedYearlyVolumeAmount = this.toNullableNumber(response.QuantityYear)
    uniformWasteInquiry.expectedYearlyVolumeUnit = response.QuantityYearUom != null && response.QuantityYearUom !== ''
      ? mapSapValueToWasteMeasurementUnit(response.QuantityYearUom)
      : null
    uniformWasteInquiry.expectedPerCollectionQuantity = this.toNullableNumber(
      response.CollectionQuantity
    )
    uniformWasteInquiry.expectedPerCollectionUnit = response.CollectionUom != null && response.CollectionUom !== ''
      ? mapSapValueToWasteMeasurementUnit(response.CollectionUom)
      : null
    uniformWasteInquiry.dischargeFrequency = response.FrequencyDischarge != null
      ? mapSapValueToWasteDischargeFrequency(response.FrequencyDischarge as SapFrequencyDischarge)
      : null
    uniformWasteInquiry.firstCollectionDate = response.CollectionDate ?? null
    uniformWasteInquiry.expectedEndDate = response.ExpectedEnd ?? null
    uniformWasteInquiry.collectionRemarks = response.InformationDelivery ?? null
    uniformWasteInquiry.isTransportByIndaver = response.TransportArranged ?? null
    uniformWasteInquiry.isLoadingByIndaver = response.LoadingByIndaver ?? null
    uniformWasteInquiry.isRegulatedTransport = response.TransportRegulated != null
      ? mapSapValueToRegulatedTransportOption(
          response.TransportRegulated as SapTransportRegulated
        )
      : null
    uniformWasteInquiry.unNumbers = (response._unNumber ?? []).map(unNumber => ({
      unNumber: unNumber.UnNumber ?? null,
      packingGroup: unNumber.UnNumberPackGroup != null
        ? mapSapValueToPackingGroup(unNumber.UnNumberPackGroup)
        : null
    }))
    uniformWasteInquiry.packaging = (response._packaging ?? []).map(packaging => ({
      type: packaging.TypePackaging != null
        ? mapSapValueToWastePackagingOption(packaging.TypePackaging)
        : null,
      size: packaging.SizePackaging ?? null,
      weightPerPieceValue: packaging.WeightPiece ?? null,
      weightPerPieceUnit: WeightUnit.KG,
      hasInnerPackaging: packaging.InnerPackaging ?? null,
      remarks: packaging.CommentPackaging ?? null
    }))
    uniformWasteInquiry.transportType = response.TransportType != null
      ? mapSapValueToWasteTransportType(response.TransportType as SapTransportType)
      : null
    uniformWasteInquiry.transportIn = response.TransportType != null
      ? mapSapValueToWasteTransportInOption(response.TransportType as SapTransportType)
      : null
    uniformWasteInquiry.containerLoadingType = response.LoadingType != null
      ? mapSapValueToContainerLoadingType(response.LoadingType as SapLoadingType)
      : null
    uniformWasteInquiry.loadingType = response.LoadingType != null
      ? mapSapValueToWasteLoadingType(response.LoadingType as SapLoadingType)
      : null
    uniformWasteInquiry.loadingMethod = response.LoadingMethod != null
      ? mapSapValueToWasteLoadingMethod(response.LoadingMethod as SapLoadingMethod)
      : null
    uniformWasteInquiry.storedIn = response.StoredIn != null
      ? mapSapValueToWasteStoredInOption(response.StoredIn as SapStoredIn)
      : null
    uniformWasteInquiry.transportVolumeAmount = response.Volume ?? null
    uniformWasteInquiry.transportVolumeUnit = response.VolumeUom != null && response.VolumeUom !== ''
      ? mapSapValueToWasteMeasurementUnit(response.VolumeUom)
      : null
    uniformWasteInquiry.isTankOwnedByCustomer = response.TankOwnedCustomer ?? null
    uniformWasteInquiry.collectionRequirements = response.RequiredForCollection != null
      ? mapSapValueToCollectionRequirementOption(
          response.RequiredForCollection as SapRequiredForCollection
        )
      : null
    uniformWasteInquiry.remarks = response.RequestDescription ?? null
    uniformWasteInquiry.sendCopyToContacts = this.mapContacts(response)
    uniformWasteInquiry.submittedOn = response.CreatedAt != null
      ? dayjs(response.CreatedAt).toDate()
      : null
    uniformWasteInquiry.additionalFiles = this.getExternalFiles(EntityPart.ADDITIONAL, sapFiles)
    uniformWasteInquiry.createdBy = this.mapCreatedBy(response)
    uniformWasteInquiry.sapFiles = sapFiles
    uniformWasteInquiry.hazardInducer1 = response.HazardInducer1 ?? null
    uniformWasteInquiry.hazardInducer2 = response.HazardInducer2 ?? null
    uniformWasteInquiry.hazardInducer3 = response.HazardInducer3 ?? null

    return uniformWasteInquiry
  }

  static mapResultsToUniformWasteInquiries (
    responses: SapGetWasteInquiryIndexResponse[]
  ): UniformFullWasteInquiry[] {
    return responses.map(response => this.mapResultToUniformWasteInquiry(response))
  }

  static mapCustomer (
    response: SapGetWasteInquiryIndexResponse
  ): SapCustomer | null {
    if (response.Customer === undefined) return null
    if (response.CustomerName === undefined) return null
    if (response.CustomerAddress === undefined) return null

    return {
      id: response.Customer,
      name: response.CustomerName,
      address: response.CustomerAddress
    }
  }

  static mapWasteProducer (
    response: SapGetWasteInquiryIndexResponse
  ): SapWasteProducer | null {
    if (response.WasteProducer === undefined) return null
    if (response.WasteProducerName === undefined) return null
    if (response.WasteProducerAddress === undefined) return null

    return {
      id: response.WasteProducer,
      name: response.WasteProducerName,
      address: response.WasteProducerAddress
    }
  }

  static mapPickUpAddress (
    response: SapGetWasteInquiryIndexResponse
  ): SapPickUpAddress | null {
    if (response.PickUpAddress === undefined) return null
    if (response.PickUpAddressName === undefined) return null
    if (response.PickUpAddressAddress === undefined) return null

    return {
      id: response.PickUpAddress,
      name: response.PickUpAddressName,
      address: response.PickUpAddressAddress
    }
  }

  static mapTemperature (
    response: SapGetWasteInquiryIndexResponse
  ): {
      stableTemperatureType: StableTemperatureType | null
      minStableTemperature: number | null
      maxStableTemperature: number | null
      averageStableTemperature: number | null
    } {
    const stableTemperatureType = response.Temperature != null
      ? mapSapValueToStableTemperatureType(response.Temperature as SapTemperature)
      : null

    if (
      stableTemperatureType === null
      || response.MinTemperature == null
      || response.MaxTemperature == null
    ) {
      return {
        stableTemperatureType: null,
        minStableTemperature: null,
        maxStableTemperature: null,
        averageStableTemperature: null
      }
    }

    if (response.MinTemperature === response.MaxTemperature) {
      return {
        stableTemperatureType: stableTemperatureType,
        minStableTemperature: null,
        maxStableTemperature: null,
        averageStableTemperature: response.MinTemperature
      }
    }

    return {
      stableTemperatureType: stableTemperatureType,
      minStableTemperature: response.MinTemperature,
      maxStableTemperature: response.MaxTemperature,
      averageStableTemperature: null
    }
  }

  static mapContacts (
    response: SapGetWasteInquiryIndexResponse
  ): Contact[] {
    const contacts: Contact[] = []

    for (const contact of response._contact ?? []) {
      if (contact.Email != null && contact.FirstName != null && contact.LastName != null) {
        contacts.push({
          firstName: contact.FirstName,
          lastName: contact.LastName,
          email: contact.Email
        })
      }
    }

    return contacts
  }

  private static toNullableNumber (value: number | undefined | null): number | null {
    return value != null && value !== 0 ? value : null
  }

  private static getExternalFiles (
    entityPart: WasteInquiryEntityPart,
    files: SapFile[]
  ): ExternalFile[] {
    return files
      .filter(file => file.entityPart === entityPart)
      .map((sapFile) => {
        return {
          uuid: sapFile.uuid,
          mimeType: sapFile.mimeType,
          name: sapFile.name,
          order: sapFile.order,
          url: null
        }
      })
  }

  private static mapCreatedBy (response: SapGetWasteInquiryIndexResponse): string | null {
    const isNotEmptyStringOrUndefined = (value?: string): value is string => value !== undefined && value !== ''

    const hasFirstAndLastName = isNotEmptyStringOrUndefined(response.FirstNameCustomerZone)
      && isNotEmptyStringOrUndefined(response.LastNameCustomerZone)

    if (hasFirstAndLastName) {
      return `${response.FirstNameCustomerZone} ${response.LastNameCustomerZone}`
    }

    return isNotEmptyStringOrUndefined(response.RequestorName)
      ? response.RequestorName
      : null
  }
}
