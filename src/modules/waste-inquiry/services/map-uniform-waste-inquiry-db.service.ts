import assert from 'assert'
import { WasteInquiry } from '../entities/waste-inquiry.entity.js'
import { UniformWasteInquiry } from '../types/uniform-waste-inquiry.type.js'
import { WasteInquiryStatus } from '../enums/waste-inquiry-status.enum.js'

export class MapUniformWasteInquiryDbService {
  static mapResultToUniformWasteInquiry (
    wasteInquiry: WasteInquiry
  ): UniformWasteInquiry {
    assert(wasteInquiry.createdByUser !== undefined)

    return {
      uuid: wasteInquiry.uuid,
      inquiryNumber: null,
      wasteStreamName: wasteInquiry.wasteStreamName,
      date: wasteInquiry.firstCollectionDate,
      contractId: null,
      contractItem: null,
      customerId: wasteInquiry.customerId,
      customerName: wasteInquiry.customerName,
      salesOrganisationId: null,
      salesOrganisationName: null,
      wasteProducerId: wasteInquiry.wasteProducerId,
      wasteProducerName: wasteInquiry.wasteProducerName,
      pickUpAddressId: wasteInquiry.pickUpAddressId,
      pickUpAddressName: wasteInquiry.pickUpAddressName,
      requestorName: wasteInquiry.createdByUser?.fullName,
      status: wasteInquiry.submittedOn === null
        ? WasteInquiryStatus.DRAFT
        : WasteInquiryStatus.NEW,
      ewcLevel1: wasteInquiry.ewcLevel1,
      ewcLevel2: wasteInquiry.ewcLevel2,
      ewcLevel3: wasteInquiry.ewcLevel3,
      isConformity: false
    }
  }

  static mapResultsToUniformWasteInquiries (
    wasteInquiries: WasteInquiry[]
  ): UniformWasteInquiry[] {
    return wasteInquiries.map(wasteInquiry => this.mapResultToUniformWasteInquiry(wasteInquiry))
  }
}
