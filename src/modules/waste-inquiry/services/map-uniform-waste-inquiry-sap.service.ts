import { SapGetWasteInquiryIndexResponse } from '../../sap/use-cases/get-waste-inquiry-index/get-waste-inquiry-index.response.js'
import { mapSapValueToTypeOfWasteRequest } from '../enums/type-of-waste-request.enum.js'
import { mapSapValueToWasteInquiryStatus, WasteInquiryStatus } from '../enums/waste-inquiry-status.enum.js'
import { UniformWasteInquiry } from '../types/uniform-waste-inquiry.type.js'

export class MapUniformWasteInquirySapService {
  static mapResultToUniformWasteInquiry (
    response: SapGetWasteInquiryIndexResponse
  ): UniformWasteInquiry {
    return {
      uuid: null,
      inquiryNumber: response.InquiryNumber !== '' && response.InquiryNumber !== undefined
        ? response.InquiryNumber
        : null,
      wasteStreamName: response.WasteName !== '' && response.WasteName !== undefined
        ? response.WasteName
        : null,
      date: response.RequestDate !== '' && response.RequestDate !== undefined
        ? response.RequestDate
        : null,
      contractId: response.Contract !== '' && response.Contract !== undefined
        ? response.Contract
        : null,
      contractItem: response.ContractItem !== '' && response.ContractItem !== undefined
        ? response.ContractItem
        : null,
      customerId: response.Customer !== '' && response.Customer !== undefined
        ? response.Customer
        : null,
      customerName: response.CustomerName !== '' && response.CustomerName !== undefined
        ? response.CustomerName
        : null,
      salesOrganisationId: response.SalesOrganization !== '' && response.SalesOrganization !== undefined
        ? response.SalesOrganization
        : null,
      salesOrganisationName: response.SalesOrganizationName !== '' && response.SalesOrganizationName !== undefined
        ? response.SalesOrganizationName
        : null,
      wasteProducerId: response.WasteProducer !== '' && response.WasteProducer !== undefined
        ? response.WasteProducer
        : null,
      wasteProducerName: response.WasteProducerName !== '' && response.WasteProducerName !== undefined
        ? response.WasteProducerName
        : null,
      pickUpAddressId: response.PickUpAddress !== '' && response.PickUpAddress !== undefined
        ? response.PickUpAddress
        : null,
      pickUpAddressName: response.PickUpAddressName !== '' && response.PickUpAddressName !== undefined
        ? response.PickUpAddressName
        : null,
      requestorName: response.RequestorName !== '' && response.RequestorName !== undefined
        ? response.RequestorName
        : null,
      status: response.NewRequestStatus !== undefined
        ? mapSapValueToWasteInquiryStatus(response.NewRequestStatus)
        : WasteInquiryStatus.NEW,
      ewcLevel1: response.EwcCustomerLevel1 !== '' && response.EwcCustomerLevel1 !== undefined
        ? response.EwcCustomerLevel1
        : null,
      ewcLevel2: response.EwcCustomerLevel2 !== '' && response.EwcCustomerLevel2 !== undefined
        ? response.EwcCustomerLevel2
        : null,
      ewcLevel3: response.EwcCustomerLevel3 !== '' && response.EwcCustomerLevel3 !== undefined
        ? response.EwcCustomerLevel3
        : null,
      typeOfRequest: response.NewRequestType !== undefined && response.NewRequestType !== ''
        ? mapSapValueToTypeOfWasteRequest(response.NewRequestType)
        : null
    }
  }

  static mapResultsToUniformWasteInquiries (
    responses: SapGetWasteInquiryIndexResponse[]
  ): UniformWasteInquiry[] {
    return responses.map(response => this.mapResultToUniformWasteInquiry(response))
  }
}
