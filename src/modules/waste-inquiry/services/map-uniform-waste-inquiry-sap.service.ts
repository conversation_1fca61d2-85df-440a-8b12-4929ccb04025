import { SapGetWasteInquiryIndexResponse } from '../../sap/use-cases/get-waste-inquiry-index/get-waste-inquiry-index.response.js'
import { mapSapValueToWasteInquiryStatus, WasteInquiryStatus } from '../enums/waste-inquiry-status.enum.js'
import { UniformWasteInquiry } from '../types/uniform-waste-inquiry.type.js'

export class MapUniformWasteInquirySapService {
  static mapResultToUniformWasteInquiry (
    response: SapGetWasteInquiryIndexResponse
  ): UniformWasteInquiry {
    return {
      uuid: null,
      inquiryNumber: response.InquiryNumber !== '' && response.InquiryNumber !== undefined
        ? response.InquiryNumber
        : null,
      wasteStreamName: response.WasteName !== '' && response.WasteName !== undefined
        ? response.WasteName
        : null,
      date: response.RequestDate !== '' && response.RequestDate !== undefined
        ? response.RequestDate
        : null,
      contractId: response.Contract !== '' && response.Contract !== undefined
        ? response.Contract
        : null,
      contractItem: response.ContractItem !== '' && response.ContractItem !== undefined
        ? response.ContractItem
        : null,
      customerId: response.Customer !== '' && response.Customer !== undefined
        ? response.Customer
        : null,
      customerName: response.CustomerName !== '' && response.CustomerName !== undefined
        ? response.CustomerName
        : null,
      salesOrganisationId: response.SalesOrganization !== '' && response.SalesOrganization !== undefined
        ? response.SalesOrganization
        : null,
      salesOrganisationName: response.SalesOrganizationName !== '' && response.SalesOrganizationName !== undefined
        ? response.SalesOrganizationName
        : null,
      wasteProducerId: response.WasteProducer !== '' && response.WasteProducer !== undefined
        ? response.WasteProducer
        : null,
      wasteProducerName: response.WasteProducerName !== '' && response.WasteProducerName !== undefined
        ? response.WasteProducerName
        : null,
      pickUpAddressId: response.PickUpAddress !== '' && response.PickUpAddress !== undefined
        ? response.PickUpAddress
        : null,
      requestorName: this.mapRequestorName(response),
      pickUpAddressName: this.mapPickUpAddressName(
        response.PickUpAddressName, response.PickUpAddressAddress
      ),
      status: response.NewRequestStatus !== undefined
        ? mapSapValueToWasteInquiryStatus(response.NewRequestStatus)
        : WasteInquiryStatus.NEW,
      ewcLevel1: response.EwcCustomerLevel1 !== '' && response.EwcCustomerLevel1 !== undefined
        ? response.EwcCustomerLevel1
        : null,
      ewcLevel2: response.EwcCustomerLevel2 !== '' && response.EwcCustomerLevel2 !== undefined
        ? response.EwcCustomerLevel2
        : null,
      ewcLevel3: response.EwcCustomerLevel3 !== '' && response.EwcCustomerLevel3 !== undefined
        ? response.EwcCustomerLevel3
        : null,
      isConformity: response.NewRequestType === '1'
    }
  }

  static mapResultsToUniformWasteInquiries (
    responses: SapGetWasteInquiryIndexResponse[]
  ): UniformWasteInquiry[] {
    return responses.map(response => this.mapResultToUniformWasteInquiry(response))
  }

  private static mapRequestorName (response: SapGetWasteInquiryIndexResponse): string | null {
    const isNotEmptyStringOrUndefined = (value?: string): value is string => value !== undefined && value !== ''

    const hasFirstAndLastName = isNotEmptyStringOrUndefined(response.FirstNameCustomerZone)
      && isNotEmptyStringOrUndefined(response.LastNameCustomerZone)

    if (hasFirstAndLastName) {
      return `${response.FirstNameCustomerZone} ${response.LastNameCustomerZone}`
    }

    return isNotEmptyStringOrUndefined(response.RequestorName)
      ? response.RequestorName
      : null
  }

  private static mapPickUpAddressName (name?: string, address?: string): string | null {
    const parts = [name, address].filter((part): part is string => part !== undefined && part !== '')

    return parts.length > 0
      ? parts.join(' - ')
      : null
  }
}
