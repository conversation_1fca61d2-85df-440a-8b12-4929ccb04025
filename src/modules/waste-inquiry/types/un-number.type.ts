import { ApiProperty } from '@nestjs/swagger'
import { IsNullable } from '@wisemen/validators'
import { IsAlphanumeric, IsBoolean, IsEnum, IsNotEmpty, IsString, MaxLength } from 'class-validator'
import { PackingGroup } from '../enums/packaging-group.enum.js'

export class UnNumber {
  @ApiProperty({ type: String, nullable: true, maxLength: 20 })
  @IsNullable()
  @IsString()
  @IsNotEmpty()
  @IsAlphanumeric()
  @MaxLength(20)
  unNumber: string | null

  @ApiProperty({ type: String, nullable: true, enum: PackingGroup, enumName: 'PackingGroup' })
  @IsNullable()
  @IsEnum(PackingGroup)
  packingGroup: PackingGroup | null

  @ApiProperty({ type: Boolean })
  @IsBoolean()
  isHazardous: boolean
}
