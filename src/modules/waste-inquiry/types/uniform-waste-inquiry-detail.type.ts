import { SapCustomer } from '../../customer/types/sap-customer.type.js'
import { SapPickUpAddress } from '../../pick-up-address/types/sap-pick-up-address.type.js'
import { SapWasteProducer } from '../../waste-producer/types/sap-waste-producer.type.js'
import { CollectionRequirementOption } from '../enums/collection-requirement-option.enum.js'
import { ContainerLoadingType } from '../enums/container-loading-type.enum.js'
import { RegulatedTransportOption } from '../enums/regulated-transport-option.enum.js'
import { StableTemperatureType } from '../enums/stable-temperature-type.enum.js'
import { StateOfMatter } from '../enums/state-of-matter.enum.js'
import { SvhcExtraOption } from '../enums/svhc-extra-option.enum.js'
import { WasteDischargeFrequency } from '../enums/waste-discharge-frequency.enum.js'
import { WasteFlashpointOption } from '../enums/waste-flashpoint-option.enum.js'
import { WasteLegislationOption } from '../enums/waste-legislation-option.enum.js'
import { WasteLoadingMethod } from '../enums/waste-loading-method.enum.js'
import { WasteLoadingType } from '../enums/waste-loading-type.enum.js'
import { WasteMeasurementUnit } from '../enums/waste-measurement-unit.enum.js'
import { WastePackagingType } from '../enums/waste-packaging-type.enum.js'
import { WastePhOption } from '../enums/waste-ph-option.enum.js'
import { WastePropertyOption } from '../enums/waste-property-option.enum.js'
import { WasteStoredInOption } from '../enums/waste-stored-in-option.enum.js'
import { WasteTransportInOption } from '../enums/waste-transport-in-option.enum.js'
import { WasteTransportType } from '../enums/waste-transport-type.enum.js'
import { FileLink } from '../../files/entities/file-link.entity.js'
import { Contact } from '../../contact/types/contact.type.js'
import { WasteInquiryStatus } from '../enums/waste-inquiry-status.enum.js'
import { SapFile } from '../../sap-files/entities/sap-file.entity.js'
import { ExternalFile } from '../../files/types/external-file.type.js'
import { UnNumber } from './un-number.type.js'
import { WasteComposition } from './waste-composition.type.js'
import { WastePackaging } from './waste-packaging.type.js'

export class UniformFullWasteInquiry {
  status: WasteInquiryStatus
  inquiryNumber: string | null
  contractNumber: string | null
  contractItem: string | null
  customer: SapCustomer | null
  wasteProducer: SapWasteProducer | null
  pickUpAddress: SapPickUpAddress | null
  wasteStreamName: string | null
  wasteStreamDescription: string | null
  ewcLevel1: string | null
  ewcLevel2: string | null
  ewcLevel3: string | null
  stateOfMatter: StateOfMatter | null
  packagingType: WastePackagingType | null
  flashpoint: WasteFlashpointOption | null
  ph: WastePhOption | null
  specificGravity: number | null
  stableTemperatureType: StableTemperatureType | null
  minStableTemperature: number | null
  maxStableTemperature: number | null
  averageStableTemperature: number | null
  sdsFiles: FileLink[] | ExternalFile[]
  noSds: boolean
  analysisReportFiles: FileLink[] | ExternalFile[]
  noAnalysisReport: boolean
  composition: WasteComposition[]
  isSampleAvailable: boolean | null
  selectedLegislationOptions: WasteLegislationOption[]
  svhcExtra: SvhcExtraOption | null
  legislationRemarks: string | null
  selectedPropertyOptions: WastePropertyOption[]
  propertyRemarks: string | null
  expectedYearlyVolumeAmount: number | null
  expectedYearlyVolumeUnit: WasteMeasurementUnit | null
  expectedPerCollectionQuantity: number | null
  expectedPerCollectionUnit: WasteMeasurementUnit | null
  dischargeFrequency: WasteDischargeFrequency | null
  firstCollectionDate: string | null
  expectedEndDate: string | null
  collectionRemarks: string | null
  isTransportByIndaver: boolean | null
  isLoadingByIndaver: boolean | null
  isRegulatedTransport: RegulatedTransportOption | null
  unNumbers: UnNumber[]
  packaging: WastePackaging[]
  transportType: WasteTransportType | null
  transportIn: WasteTransportInOption | null
  containerLoadingType: ContainerLoadingType | null
  loadingType: WasteLoadingType | null
  loadingMethod: WasteLoadingMethod | null
  storedIn: WasteStoredInOption | null
  transportVolumeAmount: number | null
  transportVolumeUnit: WasteMeasurementUnit | null
  isTankOwnedByCustomer: boolean | null
  collectionRequirements: CollectionRequirementOption | null
  remarks: string | null
  sendCopyToContacts: Contact[]
  submittedOn: Date | null
  additionalFiles: FileLink[] | ExternalFile[]
  createdBy: string | null
  hazardInducer1: string | null
  hazardInducer2: string | null
  hazardInducer3: string | null
  sapFiles?: SapFile[]
}
