import { TypeOfWasteRequest } from '../enums/type-of-waste-request.enum.js'
import { WasteInquiryStatus } from '../enums/waste-inquiry-status.enum.js'

export interface WasteInquiryDynamicTableFields {
  inquiryNumber: string | null
  wasteStreamName: string | null
  date: string | null
  contractId: string | null
  contractItem: string | null
  customerId: string | null
  customerName: string | null
  salesOrganisationId: string | null
  salesOrganisationName: string | null
  wasteProducerId: string | null
  wasteProducerName: string | null
  pickUpAddressId: string | null
  pickUpAddressName: string | null
  requestorName: string | null
  status: WasteInquiryStatus
  ewcLevel1: string | null
  ewcLevel2: string | null
  ewcLevel3: string | null
  typeOfRequest: TypeOfWasteRequest | null
}
