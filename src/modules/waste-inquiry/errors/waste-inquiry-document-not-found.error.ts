import { ApiProperty } from '@nestjs/swagger'
import { NotFoundApiError } from '../../exceptions/api-errors/not-found.api-error.js'
import { ApiErrorMeta } from '../../exceptions/api-errors/api-error-meta.decorator.js'
import { ApiErrorCode } from '../../exceptions/api-errors/api-error-code.decorator.js'
import { translateCurrent } from '../../localization/helpers/translate.helper.js'

export class WasteInquiryDocumentNotFoundErrorMeta {
  @ApiProperty({ type: String })
  wasteInquiryId: string

  constructor (id: string) {
    this.wasteInquiryId = id
  }
}

export class WasteInquiryDocumentNotFoundError extends NotFoundApiError {
  @ApiErrorCode('waste_inquiry_document_not_found')
  code = 'waste_inquiry_document_not_found'

  @ApiErrorMeta()
  meta: WasteInquiryDocumentNotFoundErrorMeta

  constructor (meta: WasteInquiryDocumentNotFoundErrorMeta) {
    super(translateCurrent('error.waste-inquiry.document_not_found'))
    this.meta = meta
  }
}
