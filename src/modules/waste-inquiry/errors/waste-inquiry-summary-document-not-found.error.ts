import { ApiProperty } from '@nestjs/swagger'
import { ApiErrorCode } from '../../exceptions/api-errors/api-error-code.decorator.js'
import { ApiErrorMeta } from '../../exceptions/api-errors/api-error-meta.decorator.js'
import { NotFoundApiError } from '../../exceptions/api-errors/not-found.api-error.js'
import { tc } from '../../localization/helpers/translate.helper.js'

export class WasteInquirySummaryDocumentNotFoundErrorMeta {
  @ApiProperty({ type: String })
  wasteInquiryId: string

  constructor (id: string) {
    this.wasteInquiryId = id
  }
}

export class WasteInquirySummaryDocumentNotFoundError extends NotFoundApiError {
  @ApiErrorCode('waste_inquiry_summary_document_not_found')
  code = 'waste_inquiry_summary_document_not_found'

  @ApiErrorMeta()
  meta: WasteInquirySummaryDocumentNotFoundErrorMeta

  constructor (meta: WasteInquirySummaryDocumentNotFoundErrorMeta) {
    super(tc('error.waste-inquiry.summary_document_not_found'))
    this.meta = meta
  }
}
