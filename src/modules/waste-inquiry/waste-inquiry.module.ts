import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { WasteInquiry } from './entities/waste-inquiry.entity.js'
import { CreateWasteInquiryModule } from './use-cases/create-waste-inquiry/create-waste-inquiry.module.js'
import { UpdateWasteInquiryModule } from './use-cases/update-waste-inquiry/update-waste-inquiry.module.js'
import { SubmitWasteInquiryModule } from './use-cases/submit-waste-inquiry/submit-waste-inquiry.module.js'
import { ViewWasteInquiryIndexModule } from './use-cases/view-waste-inquiry-index/view-waste-inquiry-index.module.js'
import { ViewWasteInquirySapModule } from './use-cases/view-waste-inquiry-sap/view-waste-inquiry-sap.module.js'
import { UploadDocumentSubmittedWasteInquiryModule } from './use-cases/upload-document-submitted-waste-inquiry/upload-document-submitted-waste-inquiry.module.js'
import { BulkDeleteWasteInquiryModule } from './use-cases/delete-waste-inquiry/bulk-delete-waste-inquiry.module.js'
import { CopyWasteInquirySapModule } from './use-cases/copy-waste-inquiry-sap/copy-waste-inquiry-sap.module.js'
import { DownloadWasteInquirySummarySapModule } from './use-cases/download-waste-inquiry-summary-sap/download-waste-inquiry-summary-sap.module.js'
import { DownloadWasteInquiryDocumentSapModule } from './use-cases/download-submitted-waste-inquiry-documents/download-waste-inquiry-document-sap.module.js'
import { ViewWasteInquiryModule } from './use-cases/view-waste-inquiry/view-waste-inquiry.module.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([WasteInquiry]),

    CreateWasteInquiryModule,
    DownloadWasteInquirySummarySapModule,
    DownloadWasteInquiryDocumentSapModule,
    ViewWasteInquiryModule,
    UpdateWasteInquiryModule,
    SubmitWasteInquiryModule,
    ViewWasteInquiryIndexModule,
    ViewWasteInquirySapModule,
    UploadDocumentSubmittedWasteInquiryModule,
    BulkDeleteWasteInquiryModule,
    CopyWasteInquirySapModule
  ]
})
export class WasteInquiryModule {}
