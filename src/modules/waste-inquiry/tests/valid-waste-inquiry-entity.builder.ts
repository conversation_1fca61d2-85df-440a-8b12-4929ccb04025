import { randomUUID } from 'node:crypto'
import dayjs from 'dayjs'
import { WasteInquiry } from '../entities/waste-inquiry.entity.js'
import { StateOfMatter } from '../enums/state-of-matter.enum.js'
import { WastePackagingType } from '../enums/waste-packaging-type.enum.js'
import { WasteFlashpointOption } from '../enums/waste-flashpoint-option.enum.js'
import { WastePhOption } from '../enums/waste-ph-option.enum.js'
import { StableTemperatureType } from '../enums/stable-temperature-type.enum.js'
import { WasteMeasurementUnit } from '../enums/waste-measurement-unit.enum.js'
import { WasteDischargeFrequency } from '../enums/waste-discharge-frequency.enum.js'
import { WasteTransportType } from '../enums/waste-transport-type.enum.js'
import { ContainerLoadingType } from '../enums/container-loading-type.enum.js'
import { RegulatedTransportOption } from '../enums/regulated-transport-option.enum.js'
import { WasteLegislationOption } from '../enums/waste-legislation-option.enum.js'
import { WastePropertyOption } from '../enums/waste-property-option.enum.js'

export class ValidWasteInquiryEntityBuilder {
  private wasteInquiry: WasteInquiry

  constructor () {
    this.reset()
  }

  reset (): this {
    this.wasteInquiry = new WasteInquiry()

    this.wasteInquiry.uuid = randomUUID()
    this.wasteInquiry.createdAt = new Date()
    this.wasteInquiry.updatedAt = new Date()
    this.wasteInquiry.customerId = randomUUID()
    this.wasteInquiry.wasteProducerId = randomUUID()
    this.wasteInquiry.isUnknownWasteProducer = false
    this.wasteInquiry.pickUpAddressId = randomUUID()
    this.wasteInquiry.isUnknownPickUpAddress = false
    this.wasteInquiry.wasteStreamName = 'TechnoScrap E-Waste Stream'
    this.wasteInquiry.wasteStreamDescription = 'Disposal and recycling of electronic waste, including outdated smartphones, broken circuit boards, used batteries, and obsolete computer components.'
    this.wasteInquiry.ewcLevel1 = '01'
    // this.wasteInquiry.ewcLevel1 = '16'
    this.wasteInquiry.ewcLevel2 = '01'
    // this.wasteInquiry.ewcLevel2 = '02'
    this.wasteInquiry.ewcLevel3 = '01'
    // this.wasteInquiry.ewcLevel3 = '13'
    this.wasteInquiry.stateOfMatter = StateOfMatter.SOLID
    this.wasteInquiry.packagingType = WastePackagingType.BULK
    this.wasteInquiry.flashpoint = WasteFlashpointOption.MORE_THAN_60
    this.wasteInquiry.ph = WastePhOption.MORE_THAN_10
    this.wasteInquiry.specificGravity = null
    this.wasteInquiry.stableTemperatureType = StableTemperatureType.AMBIENT
    this.wasteInquiry.minStableTemperature = null
    this.wasteInquiry.maxStableTemperature = null
    this.wasteInquiry.averageStableTemperature = null
    this.wasteInquiry.noSds = true
    this.wasteInquiry.noAnalysisReport = true
    this.wasteInquiry.composition = [{
      name: 'Electronics',
      minWeight: 100,
      maxWeight: 100
    }]
    this.wasteInquiry.selectedLegislationOptions = [WasteLegislationOption.NONE]
    this.wasteInquiry.legislationRemarks = null
    this.wasteInquiry.svhcExtra = null
    this.wasteInquiry.selectedPropertyOptions = [WastePropertyOption.NONE]
    this.wasteInquiry.propertyRemarks = null
    this.wasteInquiry.isSampleAvailable = true
    this.wasteInquiry.expectedYearlyVolumeAmount = 100
    this.wasteInquiry.expectedYearlyVolumeUnit = WasteMeasurementUnit.TO
    this.wasteInquiry.expectedPerCollectionQuantity = 1
    this.wasteInquiry.expectedPerCollectionUnit = WasteMeasurementUnit.TO
    this.wasteInquiry.dischargeFrequency = WasteDischargeFrequency.REGULAR_STREAM
    this.wasteInquiry.firstCollectionDate = dayjs().add(1, 'month').format('YYYY-MM-DD')
    this.wasteInquiry.expectedEndDate = dayjs().add(1, 'month').add(1, 'year').format('YYYY-MM-DD')
    this.wasteInquiry.collectionRemarks = null
    this.wasteInquiry.isTransportByIndaver = true
    this.wasteInquiry.isLoadingByIndaver = null
    this.wasteInquiry.isRegulatedTransport = RegulatedTransportOption.NO
    this.wasteInquiry.unNumbers = []
    this.wasteInquiry.transportType = WasteTransportType.CONTAINER
    this.wasteInquiry.containerLoadingType = ContainerLoadingType.CHAIN
    this.wasteInquiry.transportVolumeAmount = 1000
    this.wasteInquiry.transportVolumeUnit = WasteMeasurementUnit.M3
    this.wasteInquiry.packaging = []
    this.wasteInquiry.loadingType = null
    this.wasteInquiry.transportIn = null
    this.wasteInquiry.loadingMethod = null
    this.wasteInquiry.storedIn = null
    this.wasteInquiry.isTankOwnedByCustomer = null
    this.wasteInquiry.collectionRequirements = null
    this.wasteInquiry.remarks = null
    this.wasteInquiry.sendCopyToContacts = []
    this.wasteInquiry.submittedOn = null
    this.wasteInquiry.remarks = null
    this.wasteInquiry.hazardInducer1 = '123'
    this.wasteInquiry.hazardInducer2 = '123'
    this.wasteInquiry.hazardInducer3 = '123'

    this.wasteInquiry.sdsFiles = []
    this.wasteInquiry.analysisReportFiles = []

    return this
  }

  createdBy (userUuid: string): this {
    this.wasteInquiry.createdByUserUuid = userUuid

    return this
  }

  withCustomerId (customerId: string): this {
    this.wasteInquiry.customerId = customerId

    return this
  }

  withWasteProducerId (wasteProducerId: string | null): this {
    this.wasteInquiry.wasteProducerId = wasteProducerId
    this.wasteInquiry.isUnknownWasteProducer = wasteProducerId === null

    return this
  }

  withPickUpAddressId (pickUpAddressId: string | null): this {
    this.wasteInquiry.pickUpAddressId = pickUpAddressId
    this.wasteInquiry.isUnknownPickUpAddress = pickUpAddressId === null

    return this
  }

  build (): WasteInquiry {
    const result = this.wasteInquiry

    this.reset()

    return result
  }
}
