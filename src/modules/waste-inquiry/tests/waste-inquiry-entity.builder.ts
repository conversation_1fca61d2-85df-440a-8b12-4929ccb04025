import { randomUUID } from 'node:crypto'
import { WasteInquiry } from '../entities/waste-inquiry.entity.js'
import { RegulatedTransportOption } from '../enums/regulated-transport-option.enum.js'
import { UnNumber } from '../types/un-number.type.js'
import { WastePackagingType } from '../enums/waste-packaging-type.enum.js'
import { WasteTransportType } from '../enums/waste-transport-type.enum.js'
import { StateOfMatter } from '../enums/state-of-matter.enum.js'
import { ContainerLoadingType } from '../enums/container-loading-type.enum.js'
import { WasteTransportInOption } from '../enums/waste-transport-in-option.enum.js'
import { WasteLoadingType } from '../enums/waste-loading-type.enum.js'
import { CollectionRequirementOption } from '../enums/collection-requirement-option.enum.js'
import { WasteLoadingMethod } from '../enums/waste-loading-method.enum.js'
import { WasteStoredInOption } from '../enums/waste-stored-in-option.enum.js'
import { WasteMeasurementUnit } from '../enums/waste-measurement-unit.enum.js'
import { WastePackaging } from '../types/waste-packaging.type.js'
import { StableTemperatureType } from '../enums/stable-temperature-type.enum.js'
import { FileLink } from '../../files/entities/file-link.entity.js'
import { WasteLegislationOption } from '../enums/waste-legislation-option.enum.js'
import { SvhcExtraOption } from '../enums/svhc-extra-option.enum.js'
import { WastePropertyOption } from '../enums/waste-property-option.enum.js'
import { WasteFlashpointOption } from '../enums/waste-flashpoint-option.enum.js'
import { WastePhOption } from '../enums/waste-ph-option.enum.js'
import { WasteComposition } from '../types/waste-composition.type.js'
import { WasteDischargeFrequency } from '../enums/waste-discharge-frequency.enum.js'
import { Contact } from '../../contact/types/contact.type.js'

export class WasteInquiryEntityBuilder {
  private wasteInquiry: WasteInquiry

  constructor () {
    this.reset()
  }

  reset (): this {
    this.wasteInquiry = new WasteInquiry()

    this.wasteInquiry.uuid = randomUUID()
    this.wasteInquiry.createdAt = new Date()
    this.wasteInquiry.updatedAt = new Date()
    this.wasteInquiry.inquiryNumber = null
    this.wasteInquiry.submittedOn = null
    this.wasteInquiry.createdByUserUuid = randomUUID()
    this.wasteInquiry.customerId = null
    this.wasteInquiry.customerName = null
    this.wasteInquiry.wasteProducerId = null
    this.wasteInquiry.wasteProducerName = null
    this.wasteInquiry.isUnknownWasteProducer = false
    this.wasteInquiry.pickUpAddressId = null
    this.wasteInquiry.pickUpAddressName = null
    this.wasteInquiry.isUnknownPickUpAddress = false
    this.wasteInquiry.wasteStreamName = null
    this.wasteInquiry.wasteStreamDescription = null
    this.wasteInquiry.ewcLevel1 = null
    this.wasteInquiry.ewcLevel2 = null
    this.wasteInquiry.ewcLevel3 = null
    this.wasteInquiry.stateOfMatter = null
    this.wasteInquiry.packagingType = null
    this.wasteInquiry.flashpoint = null
    this.wasteInquiry.ph = null
    this.wasteInquiry.specificGravity = null
    this.wasteInquiry.composition = []
    this.wasteInquiry.isSampleAvailable = null
    this.wasteInquiry.stableTemperatureType = null
    this.wasteInquiry.minStableTemperature = null
    this.wasteInquiry.maxStableTemperature = null
    this.wasteInquiry.averageStableTemperature = null
    this.wasteInquiry.sdsFiles = []
    this.wasteInquiry.analysisReportFiles = []
    this.wasteInquiry.additionalFiles = []
    this.wasteInquiry.noSds = false
    this.wasteInquiry.noAnalysisReport = false
    this.wasteInquiry.selectedLegislationOptions = []
    this.wasteInquiry.svhcExtra = null
    this.wasteInquiry.legislationRemarks = null
    this.wasteInquiry.selectedPropertyOptions = []
    this.wasteInquiry.propertyRemarks = null
    this.wasteInquiry.expectedYearlyVolumeAmount = null
    this.wasteInquiry.expectedYearlyVolumeUnit = null
    this.wasteInquiry.expectedPerCollectionQuantity = null
    this.wasteInquiry.expectedPerCollectionUnit = null
    this.wasteInquiry.dischargeFrequency = null
    this.wasteInquiry.firstCollectionDate = null
    this.wasteInquiry.expectedEndDate = null
    this.wasteInquiry.collectionRemarks = null
    this.wasteInquiry.isTransportByIndaver = null
    this.wasteInquiry.isLoadingByIndaver = null
    this.wasteInquiry.isRegulatedTransport = null
    this.wasteInquiry.unNumbers = []
    this.wasteInquiry.packaging = []
    this.wasteInquiry.transportType = null
    this.wasteInquiry.containerLoadingType = null
    this.wasteInquiry.loadingType = null
    this.wasteInquiry.transportIn = null
    this.wasteInquiry.loadingMethod = null
    this.wasteInquiry.storedIn = null
    this.wasteInquiry.transportVolumeAmount = null
    this.wasteInquiry.transportVolumeUnit = null
    this.wasteInquiry.isTankOwnedByCustomer = null
    this.wasteInquiry.collectionRequirements = null
    this.wasteInquiry.remarks = null
    this.wasteInquiry.sendCopyToContacts = []
    this.wasteInquiry.hazardInducer1 = null
    this.wasteInquiry.hazardInducer2 = null
    this.wasteInquiry.hazardInducer3 = null

    return this
  }

  withUuid (uuid: string): this {
    this.wasteInquiry.uuid = uuid
    return this
  }

  createdByUserUuid (userUuid: string): this {
    this.wasteInquiry.createdByUserUuid = userUuid
    return this
  }

  withCustomerId (customerId: string | null): this {
    this.wasteInquiry.customerId = customerId
    return this
  }

  withCustomerName (customerName: string | null): this {
    this.wasteInquiry.customerName = customerName
    return this
  }

  withWasteProducerId (wasteProducerId: string | null): this {
    this.wasteInquiry.wasteProducerId = wasteProducerId
    return this
  }

  withWasteProducerName (wasteProducerName: string | null): this {
    this.wasteInquiry.wasteProducerName = wasteProducerName
    return this
  }

  withIsUnknownWasteProducer (isUnknownProducer: boolean): this {
    this.wasteInquiry.isUnknownWasteProducer = isUnknownProducer
    return this
  }

  withPickUpAddressId (pickUpAddressId: string | null): this {
    this.wasteInquiry.pickUpAddressId = pickUpAddressId
    return this
  }

  withPickUpAddressName (pickUpAddressName: string | null): this {
    this.wasteInquiry.pickUpAddressName = pickUpAddressName
    return this
  }

  withIsUnknownPickUpAddress (isUnknownPickUpAddress: boolean): this {
    this.wasteInquiry.isUnknownPickUpAddress = isUnknownPickUpAddress
    return this
  }

  withWasteStreamName (wasteStreamName: string | null): this {
    this.wasteInquiry.wasteStreamName = wasteStreamName
    return this
  }

  withWasteStreamDescription (wasteStreamDescription: string | null): this {
    this.wasteInquiry.wasteStreamDescription = wasteStreamDescription
    return this
  }

  withEwcLevel1 (ewcLevel1: string | null): this {
    this.wasteInquiry.ewcLevel1 = ewcLevel1
    return this
  }

  withEwcLevel2 (ewcLevel2: string | null): this {
    this.wasteInquiry.ewcLevel2 = ewcLevel2
    return this
  }

  withEwcLevel3 (ewcLevel3: string | null): this {
    this.wasteInquiry.ewcLevel3 = ewcLevel3
    return this
  }

  withStateOfMatter (stateOfMatter: StateOfMatter | null): this {
    this.wasteInquiry.stateOfMatter = stateOfMatter
    return this
  }

  withPackagingType (packagingType: WastePackagingType | null): this {
    this.wasteInquiry.packagingType = packagingType
    return this
  }

  withFlashpoint (flashpoint: WasteFlashpointOption | null): this {
    this.wasteInquiry.flashpoint = flashpoint
    return this
  }

  withPh (ph: WastePhOption | null): this {
    this.wasteInquiry.ph = ph
    return this
  }

  withSpecificGravity (specificGravity: number | null): this {
    this.wasteInquiry.specificGravity = specificGravity
    return this
  }

  withStableTemperatureType (stableTemperatureType: StableTemperatureType | null): this {
    this.wasteInquiry.stableTemperatureType = stableTemperatureType
    return this
  }

  withMinStableTemperature (minStableTemperature: number | null): this {
    this.wasteInquiry.minStableTemperature = minStableTemperature
    return this
  }

  withMaxStableTemperature (maxStableTemperature: number | null): this {
    this.wasteInquiry.maxStableTemperature = maxStableTemperature
    return this
  }

  withAverageStableTemperature (averageStableTemperature: number | null): this {
    this.wasteInquiry.averageStableTemperature = averageStableTemperature
    return this
  }

  withNoSds (noSds: boolean): this {
    this.wasteInquiry.noSds = noSds
    return this
  }

  withNoAnalysisReport (noAnalysisReport: boolean): this {
    this.wasteInquiry.noAnalysisReport = noAnalysisReport
    return this
  }

  withComposition (composition: WasteComposition[]): this {
    this.wasteInquiry.composition = composition
    return this
  }

  withIsSampleAvailable (isSampleAvailable: boolean | null): this {
    this.wasteInquiry.isSampleAvailable = isSampleAvailable
    return this
  }

  withSelectedLegislationOptions (options: WasteLegislationOption[]): this {
    this.wasteInquiry.selectedLegislationOptions = options
    return this
  }

  withSvhcExtra (svhcExtra: SvhcExtraOption | null): this {
    this.wasteInquiry.svhcExtra = svhcExtra
    return this
  }

  withLegislationRemarks (legislationRemarks: string | null): this {
    this.wasteInquiry.legislationRemarks = legislationRemarks
    return this
  }

  withSelectedPropertyOptions (options: WastePropertyOption[]): this {
    this.wasteInquiry.selectedPropertyOptions = options
    return this
  }

  withPropertyRemarks (propertyRemarks: string | null): this {
    this.wasteInquiry.propertyRemarks = propertyRemarks
    return this
  }

  withExpectedYearlyVolumeAmount (amount: number | null): this {
    this.wasteInquiry.expectedYearlyVolumeAmount = amount
    return this
  }

  withExpectedYearlyVolumeUnit (unit: WasteMeasurementUnit | null): this {
    this.wasteInquiry.expectedYearlyVolumeUnit = unit
    return this
  }

  withExpectedPerCollectionQuantity (quantity: number | null): this {
    this.wasteInquiry.expectedPerCollectionQuantity = quantity

    return this
  }

  withExpectedPerCollectionUnit (unit: WasteMeasurementUnit | null): this {
    this.wasteInquiry.expectedPerCollectionUnit = unit

    return this
  }

  withDischargeFrequency (dischargeFrequency: WasteDischargeFrequency | null): this {
    this.wasteInquiry.dischargeFrequency = dischargeFrequency
    return this
  }

  withFirstCollectionDate (date: string | null): this {
    this.wasteInquiry.firstCollectionDate = date
    return this
  }

  withExpectedEndDate (date: string | null): this {
    this.wasteInquiry.expectedEndDate = date
    return this
  }

  withCollectionRemarks (collectionRemarks: string | null): this {
    this.wasteInquiry.collectionRemarks = collectionRemarks
    return this
  }

  withIsTransportByIndaver (isTransportByIndaver: boolean | null): this {
    this.wasteInquiry.isTransportByIndaver = isTransportByIndaver
    return this
  }

  withIsLoadingByIndaver (isLoadingByIndaver: boolean | null): this {
    this.wasteInquiry.isLoadingByIndaver = isLoadingByIndaver
    return this
  }

  withIsRegulatedTransport (isRegulatedTransport: RegulatedTransportOption | null): this {
    this.wasteInquiry.isRegulatedTransport = isRegulatedTransport
    return this
  }

  withUnNumbers (unNumbers: UnNumber[]): this {
    this.wasteInquiry.unNumbers = unNumbers
    return this
  }

  withPackaging (packaging: WastePackaging[]): this {
    this.wasteInquiry.packaging = packaging
    return this
  }

  withTransportType (transportType: WasteTransportType | null): this {
    this.wasteInquiry.transportType = transportType
    return this
  }

  withContainerLoadingType (containerLoadingType: ContainerLoadingType | null): this {
    this.wasteInquiry.containerLoadingType = containerLoadingType
    return this
  }

  withLoadingType (loadingType: WasteLoadingType | null): this {
    this.wasteInquiry.loadingType = loadingType
    return this
  }

  withTransportIn (transportIn: WasteTransportInOption | null): this {
    this.wasteInquiry.transportIn = transportIn
    return this
  }

  withLoadingMethod (loadingMethod: WasteLoadingMethod | null): this {
    this.wasteInquiry.loadingMethod = loadingMethod
    return this
  }

  withStoredIn (storedIn: WasteStoredInOption | null): this {
    this.wasteInquiry.storedIn = storedIn
    return this
  }

  withTransportVolume (amount: number, unit: WasteMeasurementUnit): this {
    this.wasteInquiry.transportVolumeAmount = amount
    this.wasteInquiry.transportVolumeUnit = unit

    return this
  }

  withTransportVolumeAmount (amount: number | null): this {
    this.wasteInquiry.transportVolumeAmount = amount
    return this
  }

  withTransportVolumeUnit (unit: WasteMeasurementUnit | null): this {
    this.wasteInquiry.transportVolumeUnit = unit
    return this
  }

  withIsTankOwnedByCustomer (isTankOwnedByCustomer: boolean | null): this {
    this.wasteInquiry.isTankOwnedByCustomer = isTankOwnedByCustomer
    return this
  }

  withCollectionRequirements (collectionRequirements: CollectionRequirementOption | null): this {
    this.wasteInquiry.collectionRequirements = collectionRequirements
    return this
  }

  withRemarks (remarks: string | null): this {
    this.wasteInquiry.remarks = remarks
    return this
  }

  withSendCopyToContacts (contacts: Contact[]): this {
    this.wasteInquiry.sendCopyToContacts = contacts
    return this
  }

  withSubmittedOn (submittedOn: Date | null): this {
    this.wasteInquiry.submittedOn = submittedOn
    return this
  }

  withInquiryNumber (inquiryNumber: string | null): this {
    this.wasteInquiry.inquiryNumber = inquiryNumber
    return this
  }

  withSdsFiles (sdsFiles: FileLink[]): this {
    this.wasteInquiry.sdsFiles = sdsFiles
    return this
  }

  withAnalysisReportFiles (analysisReportFiles: FileLink[]): this {
    this.wasteInquiry.analysisReportFiles = analysisReportFiles
    return this
  }

  withAdditionalFiles (additionalFiles: FileLink[]): this {
    this.wasteInquiry.additionalFiles = additionalFiles
    return this
  }

  withHazardInducer1 (inducer: string | null): this {
    this.wasteInquiry.hazardInducer1 = inducer
    return this
  }

  withHazardInducer2 (inducer: string | null): this {
    this.wasteInquiry.hazardInducer2 = inducer
    return this
  }

  withHazardInducer3 (inducer: string | null): this {
    this.wasteInquiry.hazardInducer3 = inducer
    return this
  }

  build (): WasteInquiry {
    const result = this.wasteInquiry
    this.reset()
    return result
  }
}
