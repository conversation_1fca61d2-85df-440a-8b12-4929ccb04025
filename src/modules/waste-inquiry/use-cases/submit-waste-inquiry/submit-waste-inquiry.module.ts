import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { WasteInquiry } from '../../entities/waste-inquiry.entity.js'
import { EwcCodeModule } from '../../../ewc-code/ewc-code.module.js'
import { FileModule } from '../../../files/file.module.js'
import { WasteInquiryGeneralInfoValidator } from '../../validators/waste-inquiry-general-info.validator.js'
import { WasteInquiryTypeValidator } from '../../validators/waste-inquiry-type.validator.js'
import { WasteInquiryCharacteristicsValidator } from '../../validators/waste-inquiry-characteristics.validator.js'
import { WasteInquiryCompositionValidator } from '../../validators/waste-inquiry-composition.validator.js'
import { WasteInquiryLegislationAndPropertiesValidator } from '../../validators/waste-inquiry-legislation-and-properties.validator.js'
import { WasteInquiryCollectionValidator } from '../../validators/waste-inquiry-collection.validator.js'
import { WasteInquiryTransportValidator } from '../../validators/waste-inquiry-transport.validator.js'
import { CreateWasteInquirySapUseCase } from '../create-waste-inquiry-sap/create-waste-inquiry-sap.use-case.js'
import { SapModule } from '../../../sap/sap.module.js'
import { DomainEventEmitterModule } from '../../../domain-events/domain-event-emitter.module.js'
import { CustomerModule } from '../../../customer/customer.module.js'
import { SapFile } from '../../../sap-files/entities/sap-file.entity.js'
import { SubmitWasteInquiryValidator } from './submit-waste-inquiry.validator.js'
import { SubmitWasteInquiryController } from './submit-waste-inquiry.controller.js'
import { SubmitWasteInquiryUseCase } from './submit-waste-inquiry.use-case.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([
      SapFile,
      WasteInquiry
    ]),
    DomainEventEmitterModule,
    EwcCodeModule,
    FileModule,
    SapModule,
    CustomerModule
  ],
  controllers: [
    SubmitWasteInquiryController
  ],
  providers: [
    SubmitWasteInquiryUseCase,
    CreateWasteInquirySapUseCase,

    SubmitWasteInquiryValidator,

    WasteInquiryGeneralInfoValidator,
    WasteInquiryTypeValidator,
    WasteInquiryCharacteristicsValidator,
    WasteInquiryCompositionValidator,
    WasteInquiryLegislationAndPropertiesValidator,
    WasteInquiryCollectionValidator,
    WasteInquiryTransportValidator
  ]
})
export class SubmitWasteInquiryModule {}
