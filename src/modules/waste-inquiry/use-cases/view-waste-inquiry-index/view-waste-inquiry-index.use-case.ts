import { Injectable } from '@nestjs/common'
import { typeormPagination } from '@wisemen/pagination'
import { SapGetWasteInquiryIndexResponse } from '../../../sap/use-cases/get-waste-inquiry-index/get-waste-inquiry-index.response.js'
import { MapUniformWasteInquirySapService } from '../../services/map-uniform-waste-inquiry-sap.service.js'
import { MapUniformWasteInquiryDbService } from '../../services/map-uniform-waste-inquiry-db.service.js'
import { mapWasteInquiryStatusToSapValue, WasteInquiryStatus } from '../../enums/waste-inquiry-status.enum.js'
import { SapGetWasteInquiryIndexUseCase } from '../../../sap/use-cases/get-waste-inquiry-index/get-waste-inquiry-index.use-case.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { SapQuery } from '../../../sap/query/sap-query.js'
import { UserWasteProducerAuthService } from '../../../auth/services/user-waste-producer-auth.service.js'
import { FilterOperator } from '../../../sap/enums/odata-filter-operator.enum.js'
import { toBoolean } from '../../../../utils/transformers/to-boolean.js'
import { ViewWasteInquiryIndexResponse } from './view-waste-inquiry-index.response.js'
import { ViewWasteInquiryIndexValidator } from './view-waste-inquiry-index.validator.js'
import { ViewWasteInquiryIndexQuery } from './query/view-waste-inquiry-index.query.js'
import { ViewWasteInquiryIndexRepository } from './view-waste-inquiry-index.repository.js'
import { ViewWasteInquiryIndexSortQueryKey } from './query/view-waste-inquiry-index.sort-query.js'

@Injectable()
export class ViewWasteInquiryIndexUseCase {
  constructor (
    private readonly validator: ViewWasteInquiryIndexValidator,
    private readonly authContext: AuthContext,
    private readonly repository: ViewWasteInquiryIndexRepository,
    private readonly userWasteProducerAuthService: UserWasteProducerAuthService,
    private readonly sapGetWasteInquiryIndex: SapGetWasteInquiryIndexUseCase
  ) {}

  public async execute (
    query: ViewWasteInquiryIndexQuery
  ): Promise<ViewWasteInquiryIndexResponse> {
    await this.validator.validate(query)

    const user = this.authContext.getAuthOrFail()
    const pagination = typeormPagination(query.pagination)

    if (query.filter.statuses.includes(WasteInquiryStatus.DRAFT)) {
      const dbWasteInquiries = await this.repository.getDraftWasteInquiriesByUserUuid(
        user.uuid,
        query
      )

      const uniformWasteInquiries = MapUniformWasteInquiryDbService
        .mapResultsToUniformWasteInquiries(
          dbWasteInquiries
        )

      return new ViewWasteInquiryIndexResponse(
        uniformWasteInquiries,
        null,
        pagination.take,
        pagination.skip
      )
    } else {
      const sapQuery = await this.getSapQuery(query)
      const sapResponse = await this.sapGetWasteInquiryIndex.execute(sapQuery)

      const uniformWasteInquiries = MapUniformWasteInquirySapService
        .mapResultsToUniformWasteInquiries(
          sapResponse.items
        )

      return new ViewWasteInquiryIndexResponse(
        uniformWasteInquiries,
        null,
        pagination.take,
        pagination.skip
      )
    }
  }

  private async getSapQuery (
    query: ViewWasteInquiryIndexQuery
  ): Promise<SapQuery<SapGetWasteInquiryIndexResponse>> {
    const sapStatuses: string[] = []
    for (const status of query.filter.statuses) {
      sapStatuses.push(...mapWasteInquiryStatusToSapValue(status))
    }

    const sapQuery = new SapQuery<SapGetWasteInquiryIndexResponse>(query, {
      defaultOrderBy: {
        column: 'CollectionDate',
        direction: 'desc'
      },
      keyMapper: (key: string) => this.mapSortKeyToSapKey(key as ViewWasteInquiryIndexSortQueryKey)
    })
      .addSelect([
        'Contract',
        'ContractItem',
        'Customer',
        'CustomerName',
        'EwcCustomerLevel1',
        'EwcCustomerLevel2',
        'EwcCustomerLevel3',
        'FirstNameCustomerZone',
        'InquiryNumber',
        'LastNameCustomerZone',
        'NewRequestStatus',
        'NewRequestType',
        'PickUpAddress',
        'PickUpAddressAddress',
        'PickUpAddressName',
        'RequestDate',
        'RequestorName',
        'SalesOrganization',
        'SalesOrganizationName',
        'WasteName',
        'WasteProducer',
        'WasteProducerName'
      ])
      .where((qb) => {
        qb.where('NewRequestStatus', sapStatuses[0])
        for (let i = 1; i < sapStatuses.length; i++) {
          qb.orWhere('NewRequestStatus', sapStatuses[i])
        }
        return qb
      })

    // Add filter for only the ones available in CZ
    sapQuery.andWhere('RequestCategory', '4')
      .andWhere('InternalScreening', false)

    // Add filters based on selected customer and accessible waste producers
    const selectedCustomerId = this.authContext.getSelectedCustomerId()
    if (selectedCustomerId != null) {
      sapQuery.andWhere('Customer', selectedCustomerId)

      const restrictedWasteProducerIds = await this.userWasteProducerAuthService
        .getRestrictedWasteProducerIds(
          this.authContext.getAzureEntraUpn(),
          selectedCustomerId
        )
      if (restrictedWasteProducerIds !== undefined && restrictedWasteProducerIds.length > 0) {
        sapQuery.andWhere((qb) => {
          for (let i = 0; i < restrictedWasteProducerIds.length; i++) {
            if (i === 0) {
              qb.where('WasteProducer', restrictedWasteProducerIds[i])
            } else {
              qb.orWhere('WasteProducer', restrictedWasteProducerIds[i])
            }
          }
          return qb
        })
      }
    }

    // Add filters based on query parameters
    if (query.filter.contractId !== undefined) {
      sapQuery.andWhere('Contract', query.filter.contractId)
    }
    if (query.filter.contractItem !== undefined) {
      sapQuery.andWhere('ContractItem', query.filter.contractItem)
    }
    if (query.filter.customerId !== undefined) {
      sapQuery.andWhere('Customer', query.filter.customerId)
    }
    if (query.filter.salesOrganisationId !== undefined) {
      sapQuery.andWhere('SalesOrganization', query.filter.salesOrganisationId)
    }
    if (query.filter.wasteProducerId !== undefined) {
      sapQuery.andWhere('WasteProducer', query.filter.wasteProducerId)
    }
    if (query.filter.pickUpAddressId !== undefined) {
      sapQuery.andWhere('PickUpAddress', query.filter.pickUpAddressId)
    }
    if (query.filter.wasteStreamName !== undefined) {
      sapQuery.andWhere('WasteName', query.filter.wasteStreamName, FilterOperator.SUBSTRING_OF)
    }
    if (query.filter.ewcCode !== undefined) {
      sapQuery.andWhere('EwcCustomer', query.filter.ewcCode.replace(' ', ''))
    }
    if (query.filter.inquiryNumber !== undefined) {
      sapQuery.andWhere('InquiryNumber', query.filter.inquiryNumber)
    }
    if (query.filter.date !== undefined) {
      // TODO adjust back to datetime(xxx) format when SAP fixes field
      const from = `date${query.filter.date.from}`
      const to = `date${query.filter.date.to}`

      if (query.filter.date.from !== undefined) {
        sapQuery.andWhere('RequestDate', from, FilterOperator.GREATER_THAN_OR_EQUAL)
      }
      if (query.filter.date.to !== undefined) {
        sapQuery.andWhere('RequestDate', to, FilterOperator.LESS_THAN_OR_EQUAL)
      }
    }
    if (query.filter.conformityCheck !== undefined) {
      const isChecked = toBoolean(query.filter.conformityCheck)
      const sapValue = isChecked ? '1' : '2'
      sapQuery.andWhere('NewRequestType', sapValue)
    }
    if (query.filter.requestorName !== undefined) {
      sapQuery.andWhere((qb) => {
        return qb.where('FirstNameCustomerZone', query.filter.requestorName, FilterOperator.SUBSTRING_OF)
          .orWhere('LastNameCustomerZone', query.filter.requestorName, FilterOperator.SUBSTRING_OF)
          .orWhere('RequestorName', query.filter.requestorName, FilterOperator.SUBSTRING_OF)
      })
    }

    return sapQuery
  }

  private mapSortKeyToSapKey (
    key: ViewWasteInquiryIndexSortQueryKey
  ): keyof SapGetWasteInquiryIndexResponse {
    const mapping: Record<
      ViewWasteInquiryIndexSortQueryKey,
      keyof SapGetWasteInquiryIndexResponse
    > = {
      [ViewWasteInquiryIndexSortQueryKey.CONTRACT_ID]: 'Contract',
      [ViewWasteInquiryIndexSortQueryKey.CONTRACT_ITEM]: 'ContractItem',
      [ViewWasteInquiryIndexSortQueryKey.DATE]: 'RequestDate',
      [ViewWasteInquiryIndexSortQueryKey.CUSTOMER_NAME]: 'CustomerName',
      [ViewWasteInquiryIndexSortQueryKey.PICK_UP_ADDRESS_ID]: 'PickUpAddress',
      [ViewWasteInquiryIndexSortQueryKey.INQUIRY_NUMBER]: 'InquiryNumber',
      [ViewWasteInquiryIndexSortQueryKey.SALES_ORGANISATION_ID]: 'SalesOrganization',
      [ViewWasteInquiryIndexSortQueryKey.WASTE_STREAM_NAME]: 'WasteName',
      [ViewWasteInquiryIndexSortQueryKey.WASTE_PRODUCER_ID]: 'WasteProducer',
      [ViewWasteInquiryIndexSortQueryKey.WASTE_PRODUCER_NAME]: 'WasteProducerName',
      [ViewWasteInquiryIndexSortQueryKey.CONFORMITY_CHECK]: 'NewRequestType'
    }

    return mapping[key]
  }
}
