import { before, describe, it } from 'node:test'
import { expect } from 'expect'
import { createStubInstance, SinonStubbedInstance } from 'sinon'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { ViewWasteInquiryIndexValidator } from '../view-waste-inquiry-index.validator.js'
import { WasteInquiryStatus } from '../../../enums/waste-inquiry-status.enum.js'
import { InvalidWasteInquiryStatusCombinationError } from '../../../errors/invalid-waste-inquiry-status-combination.error.js'
import { GeneralInfoValidator } from '../../../../auth/validators/general-info.validator.js'
import { ViewWasteInquiryIndexQueryBuilder } from './view-waste-inquiry-index-query.builder.js'
import { ViewWasteInquiryIndexFilterQueryBuilder } from './view-waste-inquiry-index.filter.builder.js'

describe('View waste inquiry index validator unit test', () => {
  let validator: ViewWasteInquiryIndexValidator

  let generalInfoValidator: SinonStubbedInstance<GeneralInfoValidator>

  before(() => {
    TestBench.setupUnitTest()

    generalInfoValidator = createStubInstance(GeneralInfoValidator)
    generalInfoValidator.validate.resolves()

    validator = new ViewWasteInquiryIndexValidator(generalInfoValidator)
  })

  it('doesn\'t throw an error when validation passes', async () => {
    const query = new ViewWasteInquiryIndexQueryBuilder()
      .withFilter(
        new ViewWasteInquiryIndexFilterQueryBuilder()
          .addStatus(WasteInquiryStatus.DRAFT)
          .build()
      )
      .build()

    await expect(validator.validate(query)).resolves.not.toThrow()
  })

  it('throws an error when filtering on status draft alongside another status', async () => {
    const query = new ViewWasteInquiryIndexQueryBuilder()
      .withFilter(
        new ViewWasteInquiryIndexFilterQueryBuilder()
          .addStatus(WasteInquiryStatus.DRAFT)
          .addStatus(WasteInquiryStatus.COMPLETED)
          .build()
      )
      .build()

    await expect(validator.validate(query))
      .rejects.toThrow(InvalidWasteInquiryStatusCombinationError)
  })
})
