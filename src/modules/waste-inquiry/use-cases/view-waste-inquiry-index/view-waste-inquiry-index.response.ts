import { ApiProperty } from '@nestjs/swagger'
import { PaginatedOffsetResponse } from '@wisemen/pagination'
import { UniformWasteInquiry } from '../../types/uniform-waste-inquiry.type.js'
import { WasteInquiryStatus } from '../../enums/waste-inquiry-status.enum.js'
import { WasteInquiryDynamicTableFields } from '../../types/waste-inquiry.dynamic-table-fields.type.js'
import { calculatePageSize } from '../../../../utils/helpers/unknown-page-size.helper.js'

export class WasteInquiryResponse implements WasteInquiryDynamicTableFields {
  @ApiProperty({ type: String, nullable: true, description: 'UUID is null when retrieved from SAP' })
  uuid: string | null

  @ApiProperty({ type: String, nullable: true })
  inquiryNumber: string | null

  @ApiProperty({ type: String, nullable: true })
  wasteStreamName: string | null

  @ApiProperty({ type: String, nullable: true, format: 'date' })
  date: string | null

  @ApiProperty({ type: String, nullable: true })
  contractId: string | null

  @ApiProperty({ type: String, nullable: true })
  contractItem: string | null

  @ApiProperty({ type: String, nullable: true })
  customerId: string | null

  @ApiProperty({ type: String, nullable: true })
  customerName: string | null

  @ApiProperty({ type: String, nullable: true })
  salesOrganisationId: string | null

  @ApiProperty({ type: String, nullable: true })
  salesOrganisationName: string | null

  @ApiProperty({ type: String, nullable: true })
  wasteProducerId: string | null

  @ApiProperty({ type: String, nullable: true })
  wasteProducerName: string | null

  @ApiProperty({ type: String, nullable: true })
  pickUpAddressId: string | null

  @ApiProperty({ type: String, nullable: true })
  pickUpAddressName: string | null

  @ApiProperty({ type: String, nullable: true })
  requestorName: string | null

  @ApiProperty({ enum: WasteInquiryStatus, enumName: 'WasteInquiryStatus' })
  status: WasteInquiryStatus

  @ApiProperty({ type: String, nullable: true })
  ewcLevel1: string | null

  @ApiProperty({ type: String, nullable: true })
  ewcLevel2: string | null

  @ApiProperty({ type: String, nullable: true })
  ewcLevel3: string | null

  @ApiProperty({ type: Boolean })
  conformityCheck: boolean

  constructor (wasteInquiry: UniformWasteInquiry) {
    this.uuid = wasteInquiry.uuid
    this.inquiryNumber = wasteInquiry.inquiryNumber
    this.wasteStreamName = wasteInquiry.wasteStreamName
    this.date = wasteInquiry.date
    this.contractId = wasteInquiry.contractId
    this.contractItem = wasteInquiry.contractItem
    this.customerId = wasteInquiry.customerId
    this.customerName = wasteInquiry.customerName
    this.salesOrganisationId = wasteInquiry.salesOrganisationId
    this.salesOrganisationName = wasteInquiry.salesOrganisationName
    this.wasteProducerId = wasteInquiry.wasteProducerId
    this.wasteProducerName = wasteInquiry.wasteProducerName
    this.pickUpAddressId = wasteInquiry.pickUpAddressId
    this.pickUpAddressName = wasteInquiry.pickUpAddressName
    this.requestorName = wasteInquiry.requestorName
    this.status = wasteInquiry.status
    this.ewcLevel1 = wasteInquiry.ewcLevel1
    this.ewcLevel2 = wasteInquiry.ewcLevel2
    this.ewcLevel3 = wasteInquiry.ewcLevel3
    this.conformityCheck = wasteInquiry.isConformity
  }
}
export class ViewWasteInquiryIndexResponse extends PaginatedOffsetResponse<WasteInquiryResponse> {
  @ApiProperty({ type: WasteInquiryResponse, isArray: true })
  declare items: WasteInquiryResponse[]

  constructor (items: UniformWasteInquiry[], total: number | null, limit: number, offset: number) {
    const result = items.map(contact => new WasteInquiryResponse(contact))

    if (total === null) {
      total = calculatePageSize(items, offset, limit)
    }

    super(result, total, limit, offset)
  }
}
