import { ApiProperty } from '@nestjs/swagger'
import { SortQuery, SortDirectionApiProperty, SortDirection } from '@wisemen/pagination'
import { IsEnum } from 'class-validator'

export enum ViewWasteInquiryIndexSortQueryKey {
  CONTRACT_ID = 'contractId',
  CONTRACT_ITEM = 'contractItem',
  DATE = 'date',
  CUSTOMER_NAME = 'customerName',
  PICK_UP_ADDRESS_ID = 'pickUpAddressId',
  INQUIRY_NUMBER = 'inquiryNumber',
  SALES_ORGANISATION_ID = 'salesOrganisationId',
  WASTE_STREAM_NAME = 'wasteStreamName',
  WASTE_PRODUCER_ID = 'wasteProducerId',
  WASTE_PRODUCER_NAME = 'wasteProducerName',
  TYPE_OF_REQUEST = 'typeOfRequest'
}

export class ViewWasteInquiryIndexSortQuery extends SortQuery {
  @ApiProperty({ enum: ViewWasteInquiryIndexSortQueryKey, enumName: 'ViewWasteInquiryIndexSortQueryKey' })
  @IsEnum(ViewWasteInquiryIndexSortQueryKey)
  key: ViewWasteInquiryIndexSortQueryKey

  @SortDirectionApiProperty()
  @IsEnum(SortDirection)
  order: SortDirection
}
