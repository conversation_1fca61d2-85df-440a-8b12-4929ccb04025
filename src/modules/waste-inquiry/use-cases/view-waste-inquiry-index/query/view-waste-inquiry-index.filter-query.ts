import { ApiProperty } from '@nestjs/swagger'
import { ArrayMinSize, ArrayUnique, IsArray, IsEnum, IsNotEmpty, IsObject, IsString, ValidateNested } from 'class-validator'
import { FilterQuery } from '@wisemen/pagination'
import { IsUndefinable } from '@wisemen/validators'
import { Type } from 'class-transformer'
import { WasteInquiryStatus } from '../../../enums/waste-inquiry-status.enum.js'
import { DateRange } from '../../../../../utils/types/date-range.js'
import { TypeOfWasteRequest, TypeOfWasteRequestApiProperty } from '../../../enums/type-of-waste-request.enum.js'

export class ViewWasteInquiryIndexFilterQuery extends FilterQuery {
  @ApiProperty({ type: String, enum: WasteInquiryStatus, enumName: 'WasteInquiryStatus', isArray: true })
  @IsNotEmpty()
  @IsArray()
  @ArrayMinSize(1)
  @ArrayUnique()
  @IsEnum(WasteInquiryStatus, { each: true })
  statuses: WasteInquiryStatus[]

  @ApiProperty({ type: String, required: false })
  @IsString()
  @IsUndefinable()
  @IsNotEmpty()
  contractId?: string

  @ApiProperty({ type: String, required: false })
  @IsString()
  @IsUndefinable()
  @IsNotEmpty()
  contractItem?: string

  @ApiProperty({ type: String, required: false })
  @IsString()
  @IsUndefinable()
  @IsNotEmpty()
  customerId?: string

  @ApiProperty({ type: String, required: false })
  @IsString()
  @IsUndefinable()
  @IsNotEmpty()
  salesOrganisationId?: string

  @ApiProperty({ type: String, required: false })
  @IsString()
  @IsUndefinable()
  @IsNotEmpty()
  wasteProducerId?: string

  @ApiProperty({ type: String, required: false })
  @IsString()
  @IsUndefinable()
  @IsNotEmpty()
  pickUpAddressId?: string

  @ApiProperty({ type: String, required: false })
  @IsString()
  @IsUndefinable()
  @IsNotEmpty()
  wasteStreamName?: string

  @ApiProperty({ type: String, required: false })
  @IsString()
  @IsUndefinable()
  @IsNotEmpty()
  ewcCode?: string

  @ApiProperty({ type: String, required: false })
  @IsString()
  @IsUndefinable()
  @IsNotEmpty()
  inquiryNumber?: string

  @ApiProperty({ type: DateRange, required: false })
  @Type(() => DateRange)
  @IsUndefinable()
  @IsObject()
  @ValidateNested()
  date?: DateRange

  @TypeOfWasteRequestApiProperty({ required: false })
  @IsString()
  @IsEnum(TypeOfWasteRequest)
  @IsUndefinable()
  typeOfRequest?: TypeOfWasteRequest
}
