import { Controller, Get, Query } from '@nestjs/common'
import { ApiTags, ApiOAuth2, ApiOkResponse } from '@nestjs/swagger'
import { Permission } from '../../../permission/permission.enum.js'
import { Permissions } from '../../../permission/permission.decorator.js'
import { GlobalCustomerRequired } from '../../../auth/decorators/no-customer-required.decorator.js'
import { ViewWasteInquiryIndexResponse } from './view-waste-inquiry-index.response.js'
import { ViewWasteInquiryIndexUseCase } from './view-waste-inquiry-index.use-case.js'
import { ViewWasteInquiryIndexQuery } from './query/view-waste-inquiry-index.query.js'

@ApiTags('Waste inquiry')
@ApiOAuth2([])
@Controller('waste-inquiries')
export class ViewWasteInquiryIndexController {
  constructor (
    private readonly useCase: ViewWasteInquiryIndexUseCase
  ) { }

  @Get()
  @GlobalCustomerRequired()
  @Permissions(Permission.WASTE_INQUIRY_READ, Permission.WASTE_INQUIRY_MANAGE)
  @ApiOkResponse({ type: ViewWasteInquiryIndexResponse })
  public async viewWasteInquiryIndex (
    @Query() query: ViewWasteInquiryIndexQuery
  ): Promise<ViewWasteInquiryIndexResponse> {
    return await this.useCase.execute(query)
  }
}
