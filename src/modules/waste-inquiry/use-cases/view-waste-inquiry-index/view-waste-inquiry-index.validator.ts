import { Injectable } from '@nestjs/common'
import { WasteInquiryStatus } from '../../enums/waste-inquiry-status.enum.js'
import { InvalidWasteInquiryStatusCombinationError } from '../../errors/invalid-waste-inquiry-status-combination.error.js'
import { GeneralInfoValidator } from '../../../auth/validators/general-info.validator.js'
import { ViewWasteInquiryIndexQuery } from './query/view-waste-inquiry-index.query.js'

@Injectable()
export class ViewWasteInquiryIndexValidator {
  constructor (
    private readonly generalInfoValidator: GeneralInfoValidator
  ) {}

  async validate (query: ViewWasteInquiryIndexQuery): Promise<void> {
    if (query.filter.statuses != null) {
      const statuses = query.filter.statuses

      if (statuses.length > 1 && statuses.includes(WasteInquiryStatus.DRAFT)) {
        throw new InvalidWasteInquiryStatusCombinationError('error.waste-inquiry.invalid_waste_inquiry_status_combination')
      }
    }

    await this.generalInfoValidator.validate({
      customerId: query.filter?.customerId,
      wasteProducerId: query.filter?.wasteProducerId,
      pickUpAddressIds: query.filter?.pickUpAddressId
    })
  }
}
