import { before, describe, it, beforeEach } from 'node:test'
import { randomUUID } from 'crypto'
import { createStubInstance, SinonStubbedInstance } from 'sinon'
import { expect } from 'expect'
import { Repository } from 'typeorm'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { DownloadWasteInquiryDocumentSapUseCase } from '../download-waste-inquiry-document-sap.use-case.js'
import { SapGetWasteInquiryIndexUseCase } from '../../../../sap/use-cases/get-waste-inquiry-index/get-waste-inquiry-index.use-case.js'
import { SapGetDocumentsIndexUseCase } from '../../../../sap/use-cases/get-documents-index/get-documents-index.use-case.js'
import { WasteInquirySapValidator } from '../../../validators/waste-inquiry-sap.validator.js'
import { SapFile } from '../../../../sap-files/entities/sap-file.entity.js'
import { SapFileEntityBuilder } from '../../../../sap-files/tests/sap-files-entity.builder.js'
import { WasteInquiryNotFoundError } from '../../../errors/waste-inquiry-not-found-error.js'
import { WasteInquiryDocumentNotFoundError } from '../../../errors/waste-inquiry-document-not-found.error.js'
import { SapGetWasteInquiryIndexResponseBuilder } from '../../../../sap/use-cases/get-waste-inquiry-index/tests/get-waste-inquiry-index.response.builder.js'
import { SapGetDocumentIndexResponseBuilder } from '../../../../sap/use-cases/get-documents-index/tests/get-documents-index.response.builder.js'
import { SapPaginatedResponseBuilder } from '../../../../sap/tests/builders/responses/sap-paginated.response.builder.js'
import { SapGetWasteInquiryIndexResponse } from '../../../../sap/use-cases/get-waste-inquiry-index/get-waste-inquiry-index.response.js'
import { SapFileNotFoundError } from '../../../../sap-files/errors/sap-file-not-found.error.js'

describe('Download waste inquiry document unit tests', () => {
  let useCase: DownloadWasteInquiryDocumentSapUseCase
  let inquiryNumber: string
  let sapFileUuid: string

  let sapGetWasteInquiryIndexUseCase: SinonStubbedInstance<SapGetWasteInquiryIndexUseCase>
  let sapGetDocumentsIndexUseCase: SinonStubbedInstance<SapGetDocumentsIndexUseCase>
  let validator: SinonStubbedInstance<WasteInquirySapValidator>
  let sapFileRepository: SinonStubbedInstance<Repository<SapFile>>

  before(() => {
    TestBench.setupUnitTest()

    inquiryNumber = randomUUID()
    sapFileUuid = randomUUID()

    sapGetWasteInquiryIndexUseCase = createStubInstance(SapGetWasteInquiryIndexUseCase)
    sapGetDocumentsIndexUseCase = createStubInstance(SapGetDocumentsIndexUseCase)
    validator = createStubInstance(WasteInquirySapValidator)
    sapFileRepository = createStubInstance<Repository<SapFile>>(Repository<SapFile>)

    useCase = new DownloadWasteInquiryDocumentSapUseCase(
      sapFileRepository,
      sapGetWasteInquiryIndexUseCase,
      sapGetDocumentsIndexUseCase,
      validator
    )
  })

  beforeEach(() => {
    sapFileRepository.findOneBy.resolves(new SapFileEntityBuilder().build())
    sapGetWasteInquiryIndexUseCase.execute.resolves(
      new SapPaginatedResponseBuilder<SapGetWasteInquiryIndexResponse>().build()
    )
    sapGetDocumentsIndexUseCase.execute.resolves([])
    validator.validate.resolves()
  })

  it('throws SapFileNotFoundError when no sap file found', async () => {
    sapGetWasteInquiryIndexUseCase.execute.resolves(
      new SapPaginatedResponseBuilder<SapGetWasteInquiryIndexResponse>()
        .addItem(
          new SapGetWasteInquiryIndexResponseBuilder()
            .withInquiryNumber(inquiryNumber)
            .build()
        )
        .build()
    )

    sapFileRepository.findOneBy.resolves(null)

    await expect(useCase.execute(inquiryNumber, sapFileUuid))
      .rejects
      .toThrow(SapFileNotFoundError)
  })

  it('throws WasteInquiryNotFoundError when waste inquiry not found', async () => {
    await expect(useCase.execute(inquiryNumber, sapFileUuid))
      .rejects
      .toThrow(WasteInquiryNotFoundError)
  })

  it('throws WasteInquiryDocumentNotFoundError when no documents found', async () => {
    sapGetWasteInquiryIndexUseCase.execute.resolves(
      new SapPaginatedResponseBuilder<SapGetWasteInquiryIndexResponse>()
        .addItem(
          new SapGetWasteInquiryIndexResponseBuilder()
            .withInquiryNumber(inquiryNumber)
            .build()
        )
        .build()
    )

    await expect(useCase.execute(inquiryNumber, sapFileUuid))
      .rejects
      .toThrow(WasteInquiryDocumentNotFoundError)
  })

  it('throws WasteInquiryDocumentNotFoundError when no matching document found', async () => {
    sapGetWasteInquiryIndexUseCase.execute.resolves(
      new SapPaginatedResponseBuilder<SapGetWasteInquiryIndexResponse>()
        .addItem(
          new SapGetWasteInquiryIndexResponseBuilder()
            .withInquiryNumber(inquiryNumber)
            .build()
        )
        .build()
    )

    sapGetDocumentsIndexUseCase.execute.resolves([
      new SapGetDocumentIndexResponseBuilder()
        .withArcDocId(randomUUID())
        .build()
    ])

    await expect(useCase.execute(inquiryNumber, sapFileUuid))
      .rejects
      .toThrow(WasteInquiryDocumentNotFoundError)
  })

  it('returns the document url when found', async () => {
    const sapFileId = randomUUID()

    sapFileRepository.findOneBy.resolves(
      new SapFileEntityBuilder()
        .withSapFileId(sapFileId)
        .withSapObjectId(inquiryNumber)
        .build()
    )

    sapGetWasteInquiryIndexUseCase.execute.resolves(
      new SapPaginatedResponseBuilder<SapGetWasteInquiryIndexResponse>()
        .addItem(
          new SapGetWasteInquiryIndexResponseBuilder()
            .withInquiryNumber(inquiryNumber)
            .build()
        )
        .build()
    )

    const documentResponse = new SapGetDocumentIndexResponseBuilder()
      .withArcDocId(sapFileId)
      .withArObject(inquiryNumber)
      .build()

    sapGetDocumentsIndexUseCase.execute.resolves([
      documentResponse,
      new SapGetDocumentIndexResponseBuilder()
        .withArcDocId(randomUUID())
        .build()
    ])

    const url = await useCase.execute(inquiryNumber, sapFileUuid)

    expect(url).toBe(documentResponse.ArUrl)
  })
})
