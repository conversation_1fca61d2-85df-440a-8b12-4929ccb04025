import { after, before, describe, it, mock } from 'node:test'
import { randomUUID } from 'crypto'
import request from 'supertest'
import { expect } from 'expect'
import { HttpStatus } from '@nestjs/common'
import { Repository } from 'typeorm'
import { EndToEndTestSetup } from '../../../../../../test/setup/end-to-end-test-setup.js'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { Permission } from '../../../../permission/permission.enum.js'
import { SapFile } from '../../../../sap-files/entities/sap-file.entity.js'
import { TestUser } from '../../../../../app/users/tests/setup-user.type.js'
import { SapFileEntityBuilder } from '../../../../sap-files/tests/sap-files-entity.builder.js'
import { EntityType } from '../../../../sap-files/enums/entity-type.enum.js'
import { SapGetDocumentIndexResponseBuilder } from '../../../../sap/use-cases/get-documents-index/tests/get-documents-index.response.builder.js'
import { SapGetDocumentsIndexUseCase } from '../../../../sap/use-cases/get-documents-index/get-documents-index.use-case.js'
import { SapPaginatedResponseBuilder } from '../../../../sap/tests/builders/responses/sap-paginated.response.builder.js'
import { SapGetWasteInquiryIndexResponseBuilder } from '../../../../sap/use-cases/get-waste-inquiry-index/tests/get-waste-inquiry-index.response.builder.js'
import { SapGetWasteInquiryIndexResponse } from '../../../../sap/use-cases/get-waste-inquiry-index/get-waste-inquiry-index.response.js'
import { SapGetWasteInquiryIndexUseCase } from '../../../../sap/use-cases/get-waste-inquiry-index/get-waste-inquiry-index.use-case.js'

describe('Download waste inquiry document e2e tests', () => {
  let setup: EndToEndTestSetup

  let sapFileRepository: Repository<SapFile>

  let unauthorizedUser: TestUser
  let authorizedUser: TestUser

  before(async () => {
    setup = await TestBench.setupEndToEndTest()

    sapFileRepository = setup.dataSource.getRepository(SapFile)

    const [_unauthorizedUser, _authorizedUser] = await Promise.all([
      await setup.authContext.getUser([]),
      await setup.authContext.getUser([Permission.WASTE_INQUIRY_READ])
    ])

    unauthorizedUser = _unauthorizedUser
    authorizedUser = _authorizedUser
  })

  after(async () => await setup.teardown())

  it('returns 401 when not authenticated', async () => {
    const response = await request(setup.httpServer)
      .get(`/waste-inquiries/sap/${randomUUID()}/documents/${randomUUID()}`)

    expect(response).toHaveStatus(401)
  })

  it('returns 403 when unauthorized', async () => {
    const response = await request(setup.httpServer)
      .get(`/waste-inquiries/sap/${randomUUID()}/documents/${randomUUID()}`)
      .set('Authorization', `Bearer ${unauthorizedUser.token}`)

    expect(response).toHaveStatus(403)
  })

  it('downloads the waste inquiry document', async () => {
    const inquiryNumber = randomUUID()
    const sapFileUuid = randomUUID()
    const sapFileId = randomUUID()

    await sapFileRepository.save(
      new SapFileEntityBuilder()
        .withUuid(sapFileUuid)
        .withSapFileId(sapFileId)
        .withSapObjectId(inquiryNumber)
        .withEntityType(EntityType.WASTE_INQUIRY)
        .build()
    )

    mock.method(SapGetWasteInquiryIndexUseCase.prototype, 'execute', () => {
      return new SapPaginatedResponseBuilder<SapGetWasteInquiryIndexResponse>()
        .addItem(
          new SapGetWasteInquiryIndexResponseBuilder()
            .withInquiryNumber(inquiryNumber)
            .build()
        )
        .build()
    })

    const documentResponse = new SapGetDocumentIndexResponseBuilder()
      .withArcDocId(sapFileId)
      .build()

    mock.method(SapGetDocumentsIndexUseCase.prototype, 'execute', () => {
      return [documentResponse]
    })

    const response = await request(setup.httpServer)
      .get(`/waste-inquiries/sap/${inquiryNumber}/documents/${sapFileUuid}`)
      .set('Authorization', `Bearer ${authorizedUser.token}`)

    expect(response).toHaveStatus(HttpStatus.OK)
  })
})
