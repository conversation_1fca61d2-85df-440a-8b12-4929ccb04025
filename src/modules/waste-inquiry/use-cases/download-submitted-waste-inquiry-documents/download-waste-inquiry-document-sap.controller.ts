import { <PERSON>, <PERSON>, Para<PERSON>, <PERSON><PERSON> } from '@nestjs/common'
import { ApiTags, ApiOAuth2, ApiOkResponse } from '@nestjs/swagger'
import { Response } from 'express'
import { UuidParam } from '@wisemen/decorators'
import { ApiNotFoundErrorResponse } from '../../../exceptions/api-errors/api-error-response.decorator.js'
import { Permission } from '../../../permission/permission.enum.js'
import { Permissions } from '../../../permission/permission.decorator.js'
import { WasteInquiryDocumentNotFoundError } from '../../errors/waste-inquiry-document-not-found.error.js'
import { WasteInquiryNotFoundError } from '../../errors/waste-inquiry-not-found-error.js'
import { SapFileNotFoundError } from '../../../sap-files/errors/sap-file-not-found.error.js'
import { FileStreamUtil } from '../../../../utils/streams/file-stream.util.js'
import { DownloadWasteInquiryDocumentSapUseCase } from './download-waste-inquiry-document-sap.use-case.js'

@ApiTags('Waste Inquiry')
@ApiOAuth2([])
@Controller('waste-inquiries/sap/:inquiryNumber/documents/:sapFileUuid')
export class DownloadWasteInquiryDocumentSapController {
  constructor (
    private readonly useCase: DownloadWasteInquiryDocumentSapUseCase
  ) { }

  @Get()
  @Permissions(Permission.WASTE_INQUIRY_READ, Permission.WASTE_INQUIRY_MANAGE)
  @ApiOkResponse()
  @ApiNotFoundErrorResponse(
    WasteInquiryDocumentNotFoundError,
    WasteInquiryNotFoundError,
    SapFileNotFoundError
  )
  public async downloadWasteInquiryDocumentSap (
    @Param('inquiryNumber') inquiryNumber: string,
    @UuidParam('sapFileUuid') sapFileUuid: string,
    @Res() res: Response
  ): Promise<void> {
    const url = await this.useCase.execute(inquiryNumber, sapFileUuid)
    await FileStreamUtil.pipeFileResponse(res, url)
  }
}
