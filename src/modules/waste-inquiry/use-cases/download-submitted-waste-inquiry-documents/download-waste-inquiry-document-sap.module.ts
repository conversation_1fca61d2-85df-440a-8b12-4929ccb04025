import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { SapModule } from '../../../sap/sap.module.js'
import { WasteInquirySapValidator } from '../../validators/waste-inquiry-sap.validator.js'
import { SapFile } from '../../../sap-files/entities/sap-file.entity.js'
import { DownloadWasteInquiryDocumentSapController } from './download-waste-inquiry-document-sap.controller.js'
import { DownloadWasteInquiryDocumentSapUseCase } from './download-waste-inquiry-document-sap.use-case.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([
      SapFile
    ]),
    SapModule
  ],
  controllers: [
    DownloadWasteInquiryDocumentSapController
  ],
  providers: [
    DownloadWasteInquiryDocumentSapUseCase,
    WasteInquirySapValidator
  ]
})
export class DownloadWasteInquiryDocumentSapModule {}
