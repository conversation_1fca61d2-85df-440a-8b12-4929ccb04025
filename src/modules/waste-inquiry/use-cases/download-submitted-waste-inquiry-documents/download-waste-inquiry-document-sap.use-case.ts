import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { Repository } from 'typeorm'
import { SapQuery } from '../../../sap/query/sap-query.js'
import { SapGetDocumentResponse } from '../../../sap/use-cases/get-documents-index/get-documents-index.response.js'
import { SapGetDocumentsIndexUseCase } from '../../../sap/use-cases/get-documents-index/get-documents-index.use-case.js'
import { SapFile } from '../../../sap-files/entities/sap-file.entity.js'
import { EntityType } from '../../../sap-files/enums/entity-type.enum.js'
import { WasteInquirySapValidator } from '../../validators/waste-inquiry-sap.validator.js'
import { SapGetWasteInquiryIndexUseCase } from '../../../sap/use-cases/get-waste-inquiry-index/get-waste-inquiry-index.use-case.js'
import { SapGetWasteInquiryIndexResponse } from '../../../sap/use-cases/get-waste-inquiry-index/get-waste-inquiry-index.response.js'
import { WasteInquiryNotFoundError } from '../../errors/waste-inquiry-not-found-error.js'
import { WasteInquiryDocumentNotFoundError } from '../../errors/waste-inquiry-document-not-found.error.js'
import { SapGetDocumentObject } from '../../../sap/use-cases/get-documents-index/get-document-sap-object.enum.js'
import { SapFileNotFoundError } from '../../../sap-files/errors/sap-file-not-found.error.js'

@Injectable()
export class DownloadWasteInquiryDocumentSapUseCase {
  constructor (
    @InjectRepository(SapFile)
    private readonly sapFileRepository: Repository<SapFile>,
    private readonly sapGetWasteInquiryUseCase: SapGetWasteInquiryIndexUseCase,
    private readonly sapGetDocumentsIndexUseCase: SapGetDocumentsIndexUseCase,
    private readonly validator: WasteInquirySapValidator
  ) {}

  async execute (
    wasteInquiryId: string,
    sapFileUuid: string
  ): Promise<string> {
    const [sapFile, wasteInquiry] = await Promise.all([
      this.findSapFile(
        sapFileUuid,
        wasteInquiryId
      ),
      this.getWasteInquiry(wasteInquiryId)
    ])

    if (sapFile === null) {
      throw new SapFileNotFoundError({ sapFileUuid })
    }

    await this.validator.validate(wasteInquiry)

    const documents = await this.getDocuments(wasteInquiryId)

    return this.findDocumentDownloadUrl(
      wasteInquiryId,
      sapFile.sapFileId,
      documents
    )
  }

  private findDocumentDownloadUrl (
    wasteInquiryId: string,
    documentId: string,
    documents: SapGetDocumentResponse[]
  ): string {
    const url = documents.find((document) => {
      return document.ArcDocId === documentId
    })?.ArUrl

    if (url === undefined) {
      throw new WasteInquiryDocumentNotFoundError({ wasteInquiryId })
    }

    return url
  }

  private async findSapFile (
    sapFileUuid: string,
    wasteInquiryId: string
  ): Promise<SapFile | null> {
    return await this.sapFileRepository.findOneBy({
      uuid: sapFileUuid,
      sapObjectId: wasteInquiryId,
      entityType: EntityType.WASTE_INQUIRY
    })
  }

  private async getDocuments (wasteInquiryId: string): Promise<SapGetDocumentResponse[]> {
    const documentResponse = await this.sapGetDocumentsIndexUseCase.execute(
      wasteInquiryId,
      SapGetDocumentObject.BUS2030
    )

    if (documentResponse.length === 0) {
      throw new WasteInquiryDocumentNotFoundError({ wasteInquiryId })
    }

    return documentResponse
  }

  private async getWasteInquiry (wasteInquiryId: string): Promise<SapGetWasteInquiryIndexResponse> {
    const sapQuery = new SapQuery<SapGetWasteInquiryIndexResponse>()
      .where('InquiryNumber', wasteInquiryId)
      .addSelect(['InquiryNumber', 'Customer', 'WasteProducer'])
    const response = await this.sapGetWasteInquiryUseCase.execute(sapQuery)
    const inquiry = response.items.at(0)

    if (inquiry === undefined) {
      throw new WasteInquiryNotFoundError({ inquiryNumber: wasteInquiryId })
    }

    return inquiry
  }
}
