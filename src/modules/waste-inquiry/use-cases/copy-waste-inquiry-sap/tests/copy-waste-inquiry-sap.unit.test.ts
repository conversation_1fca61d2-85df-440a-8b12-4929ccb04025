import { describe, it, before } from 'node:test'
import { randomUUID } from 'crypto'
import { createStubInstance } from 'sinon'
import { Repository } from 'typeorm'
import { expect } from 'expect'
import { AuthContext } from '../../../../auth/auth.context.js'
import { stubDataSource } from '../../../../../../test/utils/stub-datasource.js'
import { DomainEventEmitter } from '../../../../domain-events/domain-event-emitter.js'
import { SapGetWasteInquiryIndexUseCase } from '../../../../sap/use-cases/get-waste-inquiry-index/get-waste-inquiry-index.use-case.js'
import { WasteInquiry } from '../../../entities/waste-inquiry.entity.js'
import { CopyWasteInquirySapUseCase } from '../copy-waste-inquiry-sap.use-case.js'
import { SapPaginatedResponseBuilder } from '../../../../sap/tests/builders/responses/sap-paginated.response.builder.js'
import { SapGetWasteInquiryIndexResponse } from '../../../../sap/use-cases/get-waste-inquiry-index/get-waste-inquiry-index.response.js'
import { WasteInquiryNotFoundError } from '../../../errors/waste-inquiry-not-found-error.js'
import { GetWasteInquiryIndexResponseBuilder } from '../../../../sap/use-cases/get-waste-inquiry-index/tests/get-waste-inquiry-index.response.builder.js'
import { WasteInquirySapCopiedEvent } from '../waste-inquiry-sap-copied.event.js'
import { CopyWasteInquirySapValidator } from '../copy-waste-inquiry-sap.validator.js'
import { TestBench } from '../../../../../../test/setup/test-bench.js'

describe('CopyWasteInquirySapResponse - Unit Tests', () => {
  before(() => TestBench.setupUnitTest())

  it('throws WasteInquiryNotFoundError when SAP calls returns an empty array', () => {
    const getWasteInquiryIndexUseCase = createStubInstance(SapGetWasteInquiryIndexUseCase)

    const useCase = new CopyWasteInquirySapUseCase(
      createStubInstance(AuthContext),
      stubDataSource(),
      createStubInstance(DomainEventEmitter),
      getWasteInquiryIndexUseCase,
      createStubInstance(Repository<WasteInquiry>),
      createStubInstance(CopyWasteInquirySapValidator)
    )

    getWasteInquiryIndexUseCase.execute
      .resolves(new SapPaginatedResponseBuilder<SapGetWasteInquiryIndexResponse>().build())

    expect(() => useCase.execute(randomUUID())).rejects.toThrow(WasteInquiryNotFoundError)
  })

  it('inserts entity and emits WasteInquirySapCopiedEvent', async () => {
    const getWasteInquiryIndexUseCase = createStubInstance(SapGetWasteInquiryIndexUseCase)
    const authContext = createStubInstance(AuthContext)
    const eventEmitter = createStubInstance(DomainEventEmitter)
    const repository = createStubInstance(Repository<WasteInquiry>)
    const validator = createStubInstance(CopyWasteInquirySapValidator)

    const useCase = new CopyWasteInquirySapUseCase(
      authContext,
      stubDataSource(),
      eventEmitter,
      getWasteInquiryIndexUseCase,
      repository,
      validator
    )

    const inquiryNumber = randomUUID()
    const inquiryResponse = new GetWasteInquiryIndexResponseBuilder()
      .withInquiryNumber(inquiryNumber)
      .build()

    getWasteInquiryIndexUseCase.execute
      .resolves(new SapPaginatedResponseBuilder<SapGetWasteInquiryIndexResponse>()
        .addItem(inquiryResponse)
        .build())

    await useCase.execute(inquiryNumber)

    const insertedEntity = repository.insert.args[0][0] as WasteInquiry

    expect(repository.insert.calledOnce).toBe(true)
    expect(eventEmitter)
      .toHaveEmitted(new WasteInquirySapCopiedEvent(inquiryNumber, insertedEntity.uuid))
  })
})
