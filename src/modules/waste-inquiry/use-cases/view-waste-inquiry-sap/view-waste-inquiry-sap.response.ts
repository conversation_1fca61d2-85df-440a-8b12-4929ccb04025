import assert from 'assert'
import { ApiProperty } from '@nestjs/swagger'
import { AbstractViewWasteInquiryResponse } from '../../responses/abstract-view-waste-inquiry.response.js'
import { UniformFullWasteInquiry } from '../../types/uniform-waste-inquiry-detail.type.js'

export class ViewWasteInquirySapResponse extends AbstractViewWasteInquiryResponse {
  @ApiProperty({ type: String, nullable: true })
  inquiryNumber: string | null

  @ApiProperty({ type: String, nullable: true })
  createdBy: string | null

  @ApiProperty({ type: String, nullable: true })
  contractNumber: string | null

  @ApiProperty({ type: String, nullable: true })
  contractItem: string | null

  constructor (wasteInquiry: UniformFullWasteInquiry) {
    assert(wasteInquiry.sapFiles !== undefined)

    super(wasteInquiry)

    this.inquiryNumber = wasteInquiry.inquiryNumber
    this.createdBy = wasteInquiry.createdBy
    this.contractNumber = wasteInquiry.contractNumber
    this.contractItem = wasteInquiry.contractItem
  }
}
