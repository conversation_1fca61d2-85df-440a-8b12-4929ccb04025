import { Modu<PERSON> } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { SapModule } from '../../../sap/sap.module.js'
import { WasteInquirySapValidator } from '../../validators/waste-inquiry-sap.validator.js'
import { SapFile } from '../../../sap-files/entities/sap-file.entity.js'
import { ViewWasteInquirySapController } from './view-waste-inquiry-sap.controller.js'
import { ViewWasteInquirySapUseCase } from './view-waste-inquiry-sap.use-case.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([SapFile]),
    SapModule
  ],
  controllers: [
    ViewWasteInquirySapController
  ],
  providers: [
    ViewWasteInquirySapUseCase,
    WasteInquirySapValidator
  ]
})
export class ViewWasteInquirySapModule {}
