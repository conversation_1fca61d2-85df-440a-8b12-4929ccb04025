import { after, before, describe, it, mock } from 'node:test'
import { randomUUID } from 'crypto'
import request from 'supertest'
import { expect } from 'expect'
import { EndToEndTestSetup } from '../../../../../../test/setup/end-to-end-test-setup.js'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { Permission } from '../../../../permission/permission.enum.js'
import { SapGetWasteInquiryIndexUseCase } from '../../../../sap/use-cases/get-waste-inquiry-index/get-waste-inquiry-index.use-case.js'
import { SapGetWasteInquiryIndexResponseBuilder } from '../../../../sap/use-cases/get-waste-inquiry-index/tests/get-waste-inquiry-index.response.builder.js'
import { SapPaginatedResponseBuilder } from '../../../../sap/tests/builders/responses/sap-paginated.response.builder.js'
import { SapGetWasteInquiryIndexResponse } from '../../../../sap/use-cases/get-waste-inquiry-index/get-waste-inquiry-index.response.js'

describe('View waste inquiry SAP e2e test', () => {
  let setup: EndToEndTestSetup

  before(async () => {
    setup = await TestBench.setupEndToEndTest()
  })

  after(async () => await setup.teardown())

  it('returns 401 when not authenticated', async () => {
    const response = await request(setup.httpServer)
      .get(`/waste-inquiries/sap/${randomUUID()}`)

    expect(response).toHaveStatus(401)
  })

  it('returns 403 when unauthorized', async () => {
    const user = await setup.authContext.getUser([])

    const response = await request(setup.httpServer)
      .get(`/waste-inquiries/sap/${randomUUID()}`)
      .set('Authorization', `Bearer ${user.token}`)

    expect(response).toHaveStatus(403)
  })

  it('returns waste inquiry when authorized', async () => {
    const user = await setup.authContext.getUser([Permission.WASTE_INQUIRY_READ])

    const inquiryNumber = randomUUID()
    mock.method(SapGetWasteInquiryIndexUseCase.prototype, 'execute', () => {
      return new SapPaginatedResponseBuilder<SapGetWasteInquiryIndexResponse>()
        .addItem(new SapGetWasteInquiryIndexResponseBuilder()
          .withInquiryNumber(inquiryNumber)
          .build())
        .build()
    })

    const response = await request(setup.httpServer)
      .get(`/waste-inquiries/sap/${inquiryNumber}`)
      .set('Authorization', `Bearer ${user.token}`)

    expect(response).toHaveStatus(200)
  })
})
