import assert from 'assert'
import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { Any, Repository } from 'typeorm'
import { SapGetWasteInquiryIndexResponse } from '../../../sap/use-cases/get-waste-inquiry-index/get-waste-inquiry-index.response.js'
import { SapGetWasteInquiryIndexUseCase } from '../../../sap/use-cases/get-waste-inquiry-index/get-waste-inquiry-index.use-case.js'
import { SapQuery } from '../../../sap/query/sap-query.js'
import { WasteInquiryNotFoundError } from '../../errors/waste-inquiry-not-found-error.js'
import { File } from '../../../files/entities/file.entity.js'
import { FileNotFoundError } from '../../../files/errors/file.not-found.error.js'
import { SapUploadAndCreateDocumentUseCase } from '../../../sap/use-cases/upload-and-create-document/upload-and-create-document.use-case.js'
import { SapFile } from '../../../sap-files/entities/sap-file.entity.js'
import { EntityType } from '../../../sap-files/enums/entity-type.enum.js'
import { SapFileEntityBuilder } from '../../../sap-files/tests/sap-files-entity.builder.js'
import { UploadDocumentSubmittedWasteInquiryValidator } from './upload-document-submitted-waste-inquiry.validator.js'
import { UploadDocumentSubmittedWasteInquiryCommand, UploadDocumentWasteInquiryCommand } from './upload-document-submitted-waste-inquiry.command.js'

@Injectable()
export class UploadDocumentSubmittedWasteInquiryUseCase {
  constructor (
    @InjectRepository(SapFile)
    private readonly sapFileRepository: Repository<SapFile>,
    @InjectRepository(File)
    private readonly fileRepository: Repository<File>,
    private readonly sapGetWasteInquiryIndexUseCase: SapGetWasteInquiryIndexUseCase,
    private readonly validator: UploadDocumentSubmittedWasteInquiryValidator,
    private readonly sapUploadAndCreateDocument: SapUploadAndCreateDocumentUseCase
  ) {}

  async execute (
    inquiryNumber: string,
    command: UploadDocumentSubmittedWasteInquiryCommand
  ): Promise<void> {
    const sapQuery = this.getSapQuery(inquiryNumber)
    const sapResponse = await this.sapGetWasteInquiryIndexUseCase.execute(sapQuery)

    if (sapResponse.items.length === 0) {
      throw new WasteInquiryNotFoundError({ inquiryNumber })
    }

    const options = {
      customerId: sapResponse.items[0].Customer,
      wasteProducerId: sapResponse.items[0].WasteProducer
    }

    await this.validator.validate(options)

    const fileUuids = command.documents.map(document => document.fileUuid)

    const files = await this.validateAndLoadFiles(fileUuids)

    await this.uploadAndCreateFiles(
      inquiryNumber,
      command.documents,
      files
    )
  }

  private async uploadAndCreateFiles (
    inquiryNumber: string,
    documents: UploadDocumentWasteInquiryCommand[],
    files: File[]
  ): Promise<void> {
    const sapFiles: SapFile[] = []

    await Promise.all(
      documents.map(async (document) => {
        const file = files.find(file => file.uuid === document.fileUuid)

        assert(file !== undefined)

        const sapFileId = await this.sapUploadAndCreateDocument.execute(
          inquiryNumber,
          file,
          document.entityPart
        )

        const sapFile = new SapFileEntityBuilder()
          .withEntityType(EntityType.WASTE_INQUIRY)
          .withSapFileId(sapFileId)
          .withSapObjectId(inquiryNumber)
          .withName(file.name)
          .withEntityPart(document.entityPart)
          .build()

        sapFiles.push(sapFile)
      })
    )

    await this.sapFileRepository.insert(sapFiles)
  }

  private async validateAndLoadFiles (
    fileUuids: string[]
  ): Promise<File[]> {
    const files = await this.fileRepository.find({
      where: {
        uuid: Any(fileUuids)
      }
    })

    if (files.length !== fileUuids.length) {
      const foundFileUuids = files.map(file => file.uuid)
      const missingFileUuids = fileUuids.filter(
        uuid => !foundFileUuids.includes(uuid)
      )

      throw new FileNotFoundError(missingFileUuids)
    }

    return files
  }

  private getSapQuery (
    inquiryNumber: string
  ): SapQuery<SapGetWasteInquiryIndexResponse> {
    const sapQuery = new SapQuery<SapGetWasteInquiryIndexResponse>()
      .addSelect(
        'InquiryNumber'
      )
      .where('InquiryNumber', inquiryNumber)
      .setTop(1)

    return sapQuery
  }
}
