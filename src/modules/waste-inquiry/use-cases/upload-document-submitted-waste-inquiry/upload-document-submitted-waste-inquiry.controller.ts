import { Body, Controller, HttpCode, Param, Post } from '@nestjs/common'
import { ApiOAuth2, ApiOkResponse, ApiTags } from '@nestjs/swagger'
import { Permissions } from '../../../permission/permission.decorator.js'
import { Permission } from '../../../permission/permission.enum.js'
import { ApiNotFoundErrorResponse } from '../../../exceptions/api-errors/api-error-response.decorator.js'
import { GlobalCustomerRequired } from '../../../auth/decorators/no-customer-required.decorator.js'
import { WasteInquiryNotFoundError } from '../../errors/waste-inquiry-not-found-error.js'
import { UploadDocumentSubmittedWasteInquiryCommand } from './upload-document-submitted-waste-inquiry.command.js'
import { UploadDocumentSubmittedWasteInquiryUseCase } from './upload-document-submitted-waste-inquiry.use-case.js'

@ApiTags('Waste inquiry')
@ApiOAuth2([])
@Controller('waste-inquiries/:inquiryNumber/upload-documents')
export class UploadDocumentSubmittedWasteInquiryController {
  constructor (
    private readonly useCase: UploadDocumentSubmittedWasteInquiryUseCase
  ) { }

  @Post()
  @GlobalCustomerRequired()
  @HttpCode(200)
  @Permissions(Permission.WASTE_INQUIRY_MANAGE)
  @ApiOkResponse({
    description: 'Documents uploaded to the submitted waste inquiry'
  })
  @ApiNotFoundErrorResponse(WasteInquiryNotFoundError)
  public async uploadDocument (
    @Param('inquiryNumber') inquiryNumber: string,
    @Body() command: UploadDocumentSubmittedWasteInquiryCommand
  ): Promise<void> {
    return await this.useCase.execute(inquiryNumber, command)
  }
}
