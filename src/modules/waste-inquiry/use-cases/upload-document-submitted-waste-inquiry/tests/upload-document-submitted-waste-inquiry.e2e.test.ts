import { after, before, describe, it, mock } from 'node:test'
import { randomUUID } from 'crypto'
import request from 'supertest'
import { Repository } from 'typeorm'
import { expect } from 'expect'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { EndToEndTestSetup } from '../../../../../../test/setup/end-to-end-test-setup.js'
import { Permission } from '../../../../permission/permission.enum.js'
import { TestUser } from '../../../../../app/users/tests/setup-user.type.js'
import { File } from '../../../../files/entities/file.entity.js'
import { FileEntityBuilder } from '../../../../files/entities/file-entity.builder.js'
import { EntityPart } from '../../../../files/enums/entity-part.enum.js'
import { SapGetWasteInquiryIndexUseCase } from '../../../../sap/use-cases/get-waste-inquiry-index/get-waste-inquiry-index.use-case.js'
import { SapPaginatedResponseBuilder } from '../../../../sap/tests/builders/responses/sap-paginated.response.builder.js'
import { SapGetWasteInquiryIndexResponse } from '../../../../sap/use-cases/get-waste-inquiry-index/get-waste-inquiry-index.response.js'
import { SapGetWasteInquiryIndexResponseBuilder } from '../../../../sap/use-cases/get-waste-inquiry-index/tests/get-waste-inquiry-index.response.builder.js'
import { UploadDocumentSubmittedWasteInquiryCommandBuilder } from './upload-document-submitted-waste-inquiry.command.builder.js'

describe('Upload document waste inquiry e2e test', () => {
  let setup: EndToEndTestSetup

  let fileRepository: Repository<File>

  let unauthorizedUser: TestUser
  let authorizedUser: TestUser

  before(async () => {
    setup = await TestBench.setupEndToEndTest()

    fileRepository = setup.dataSource.getRepository(File)

    const [_unauthorizedUser, _authorizedUser] = await Promise.all([
      await setup.authContext.getUser([]),
      await setup.authContext.getUser([Permission.WASTE_INQUIRY_MANAGE])
    ])

    unauthorizedUser = _unauthorizedUser
    authorizedUser = _authorizedUser
  })

  after(async () => await setup.teardown())

  it('returns 401 when not authenticated', async () => {
    const response = await request(setup.httpServer)
      .post(`/waste-inquiries/${randomUUID()}/upload-documents`)

    expect(response).toHaveStatus(401)
  })

  it('returns 403 when unauthorized', async () => {
    const response = await request(setup.httpServer)
      .post(`/waste-inquiries/${randomUUID()}/upload-documents`)
      .set('Authorization', `Bearer ${unauthorizedUser.token}`)

    expect(response).toHaveStatus(403)
  })

  it('returns 200 when authorized and file exists', async () => {
    const sdsFile = await fileRepository.save(new FileEntityBuilder().build())
    const additionalFile = await fileRepository.save(new FileEntityBuilder().build())
    const analysisReportFile = await fileRepository.save(new FileEntityBuilder().build())

    const inquiryNumber = randomUUID()

    mock.method(SapGetWasteInquiryIndexUseCase.prototype, 'execute', () => {
      return new SapPaginatedResponseBuilder<SapGetWasteInquiryIndexResponse>()
        .addItem(new SapGetWasteInquiryIndexResponseBuilder()
          .withInquiryNumber(inquiryNumber)
          .build())
        .build()
    })

    const command = new UploadDocumentSubmittedWasteInquiryCommandBuilder()
      .addUploadDocumentCommand(sdsFile.uuid, EntityPart.SDS)
      .addUploadDocumentCommand(additionalFile.uuid, EntityPart.SDS)
      .addUploadDocumentCommand(analysisReportFile.uuid, EntityPart.SDS)
      .build()

    const response = await request(setup.httpServer)
      .post(`/waste-inquiries/${inquiryNumber}/upload-documents`)
      .set('Authorization', `Bearer ${authorizedUser.token}`)
      .send(command)

    expect(response).toHaveStatus(200)
  })
})
