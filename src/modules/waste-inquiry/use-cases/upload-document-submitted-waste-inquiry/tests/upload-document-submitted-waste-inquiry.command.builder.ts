import { EntityPart } from '../../../../files/enums/entity-part.enum.js'
import { UploadDocumentSubmittedWasteInquiryCommand, UploadDocumentWasteInquiryCommand } from '../upload-document-submitted-waste-inquiry.command.js'

export class UploadDocumentSubmittedWasteInquiryCommandBuilder {
  private command: UploadDocumentSubmittedWasteInquiryCommand

  constructor () {
    this.reset()
  }

  reset (): this {
    this.command = new UploadDocumentSubmittedWasteInquiryCommand()
    this.command.documents = []

    return this
  }

  addUploadDocumentCommand (fileUuid: string, entityPart: EntityPart): this {
    const uploadDocumentCommand = new UploadDocumentWasteInquiryCommand()
    uploadDocumentCommand.fileUuid = fileUuid
    uploadDocumentCommand.entityPart = entityPart

    this.command.documents.push(uploadDocumentCommand)

    return this
  }

  build (): UploadDocumentSubmittedWasteInquiryCommand {
    const command = this.command
    this.reset()
    return command
  }
}
