import { before, describe, it, afterEach } from 'node:test'
import { randomUUID } from 'crypto'
import { Repository } from 'typeorm'
import { expect } from 'expect'
import { assert, createStubInstance, SinonStubbedInstance } from 'sinon'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { SapUploadAndCreateDocumentUseCase } from '../../../../sap/use-cases/upload-and-create-document/upload-and-create-document.use-case.js'
import { FileEntityBuilder } from '../../../../files/entities/file-entity.builder.js'
import { File } from '../../../../files/entities/file.entity.js'
import { EntityPart } from '../../../../files/enums/entity-part.enum.js'
import { FileNotFoundError } from '../../../../files/errors/file.not-found.error.js'
import { UploadDocumentSubmittedWasteInquiryValidator } from '../upload-document-submitted-waste-inquiry.validator.js'
import { UploadDocumentSubmittedWasteInquiryUseCase } from '../upload-document-submitted-waste-inquiry.use-case.js'
import { SapGetWasteInquiryIndexUseCase } from '../../../../sap/use-cases/get-waste-inquiry-index/get-waste-inquiry-index.use-case.js'
import { SapPaginatedResponseBuilder } from '../../../../sap/tests/builders/responses/sap-paginated.response.builder.js'
import { SapGetWasteInquiryIndexResponse } from '../../../../sap/use-cases/get-waste-inquiry-index/get-waste-inquiry-index.response.js'
import { WasteInquiryNotFoundError } from '../../../errors/waste-inquiry-not-found-error.js'
import { SapGetWasteInquiryIndexResponseBuilder } from '../../../../sap/use-cases/get-waste-inquiry-index/tests/get-waste-inquiry-index.response.builder.js'
import { SapFile } from '../../../../sap-files/entities/sap-file.entity.js'
import { SapFileEntityBuilder } from '../../../../sap-files/tests/sap-files-entity.builder.js'
import { UploadDocumentSubmittedWasteInquiryCommandBuilder } from './upload-document-submitted-waste-inquiry.command.builder.js'

describe('Upload document submitted waste inquiry use-case unit test', () => {
  let useCase: UploadDocumentSubmittedWasteInquiryUseCase

  let validator: SinonStubbedInstance<UploadDocumentSubmittedWasteInquiryValidator>
  let sapUploadAndCreateDocument: SinonStubbedInstance<SapUploadAndCreateDocumentUseCase>
  let sapGetWasteInquiryIndexUseCase: SinonStubbedInstance<SapGetWasteInquiryIndexUseCase>
  let fileRepository: SinonStubbedInstance<Repository<File>>
  let sapFileRepository: SinonStubbedInstance<Repository<SapFile>>

  let file: File

  before(() => {
    TestBench.setupUnitTest()

    file = new FileEntityBuilder().build()

    validator = createStubInstance(UploadDocumentSubmittedWasteInquiryValidator, {
      validate: Promise.resolve()
    })

    sapUploadAndCreateDocument = createStubInstance(SapUploadAndCreateDocumentUseCase, {
      execute: Promise.resolve(randomUUID())
    })

    sapGetWasteInquiryIndexUseCase = createStubInstance(SapGetWasteInquiryIndexUseCase)
    fileRepository = createStubInstance<Repository<File>>(Repository<File>)
    sapFileRepository = createStubInstance<Repository<SapFile>>(Repository<SapFile>)

    useCase = new UploadDocumentSubmittedWasteInquiryUseCase(
      sapFileRepository,
      fileRepository,
      sapGetWasteInquiryIndexUseCase,
      validator,
      sapUploadAndCreateDocument
    )

    mockMethods()
  })

  afterEach(() => {
    mockMethods()
  })

  function mockMethods () {
    sapGetWasteInquiryIndexUseCase.execute.resolves(
      new SapPaginatedResponseBuilder<SapGetWasteInquiryIndexResponse>()
        .addItem(new SapGetWasteInquiryIndexResponseBuilder()
          .build()
        )
        .build()
    )
    fileRepository.find.resolves([file])
    sapFileRepository.create.resolves(
      new SapFileEntityBuilder().build()
    )
    sapFileRepository.save.resolves()
  }

  it('Calls all methods', async () => {
    const command = new UploadDocumentSubmittedWasteInquiryCommandBuilder()
      .addUploadDocumentCommand(file.uuid, EntityPart.ADDITIONAL)
      .build()

    const requestNumber = randomUUID()

    sapGetWasteInquiryIndexUseCase.execute.resolves(
      new SapPaginatedResponseBuilder<SapGetWasteInquiryIndexResponse>()
        .addItem(new SapGetWasteInquiryIndexResponseBuilder()
          .build())
        .build())

    await useCase.execute(requestNumber, command)

    assert.calledOnce(sapGetWasteInquiryIndexUseCase.execute)
    assert.calledOnce(sapUploadAndCreateDocument.execute)
    assert.calledOnce(fileRepository.find)
    assert.calledOnce(validator.validate)
  })

  it('Throws error when waste inquiry is not found', async () => {
    const command = new UploadDocumentSubmittedWasteInquiryCommandBuilder()
      .addUploadDocumentCommand(file.uuid, EntityPart.ADDITIONAL)
      .build()

    const inquiryNumber = randomUUID()

    sapGetWasteInquiryIndexUseCase.execute.resolves(
      new SapPaginatedResponseBuilder<SapGetWasteInquiryIndexResponse>()
        .build())

    await expect(useCase.execute(inquiryNumber, command))
      .rejects
      .toThrow(WasteInquiryNotFoundError)
  })

  it('Throws error when file is not found', async () => {
    const command = new UploadDocumentSubmittedWasteInquiryCommandBuilder()
      .addUploadDocumentCommand(file.uuid, EntityPart.ADDITIONAL)
      .build()

    const requestNumber = randomUUID()

    sapGetWasteInquiryIndexUseCase.execute.resolves(
      new SapPaginatedResponseBuilder<SapGetWasteInquiryIndexResponse>()
        .addItem(new SapGetWasteInquiryIndexResponseBuilder()
          .build())
        .build()
    )

    fileRepository.find.resolves([])

    await expect(useCase.execute(requestNumber, command))
      .rejects
      .toThrow(FileNotFoundError)
  })
})
