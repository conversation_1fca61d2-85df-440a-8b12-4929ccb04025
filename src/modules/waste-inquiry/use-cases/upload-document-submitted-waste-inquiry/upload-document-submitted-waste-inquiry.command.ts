import { ApiProperty } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { ArrayMinSize, ArrayUnique, ValidateNested } from 'class-validator'
import { UploadDocumentCommand } from '../../../files/commands/upload-file-sap.command.js'

export class UploadDocumentWasteInquiryCommand extends UploadDocumentCommand {}

export class UploadDocumentSubmittedWasteInquiryCommand {
  @ApiProperty({ type: UploadDocumentWasteInquiryCommand, isArray: true })
  @ValidateNested({ each: true })
  @Type(() => UploadDocumentWasteInquiryCommand)
  @ArrayUnique()
  @ArrayMinSize(1)
  documents: UploadDocumentWasteInquiryCommand[]
}
