import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { SapModule } from '../../../sap/sap.module.js'
import { File } from '../../../files/entities/file.entity.js'
import { SapFile } from '../../../sap-files/entities/sap-file.entity.js'
import { UploadDocumentSubmittedWasteInquiryController } from './upload-document-submitted-waste-inquiry.controller.js'
import { UploadDocumentSubmittedWasteInquiryUseCase } from './upload-document-submitted-waste-inquiry.use-case.js'
import { UploadDocumentSubmittedWasteInquiryValidator } from './upload-document-submitted-waste-inquiry.validator.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([
      SapFile,
      File
    ]),
    SapModule
  ],
  controllers: [
    UploadDocumentSubmittedWasteInquiryController
  ],
  providers: [
    UploadDocumentSubmittedWasteInquiryUseCase,
    UploadDocumentSubmittedWasteInquiryValidator
  ]
})
export class UploadDocumentSubmittedWasteInquiryModule {}
