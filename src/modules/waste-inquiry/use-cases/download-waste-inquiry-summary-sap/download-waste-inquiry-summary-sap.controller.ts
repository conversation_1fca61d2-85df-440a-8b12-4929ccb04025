import { <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from '@nestjs/common'
import { ApiTags, ApiOAuth2, ApiOkResponse } from '@nestjs/swagger'
import { Response } from 'express'
import { Permissions } from '../../../../modules/permission/permission.decorator.js'
import { Permission } from '../../../../modules/permission/permission.enum.js'
import { ApiNotFoundErrorResponse } from '../../../exceptions/api-errors/api-error-response.decorator.js'
import { WasteInquirySummaryDocumentNotFoundError } from '../../errors/waste-inquiry-summary-document-not-found.error.js'
import { WasteInquiryNotFoundError } from '../../errors/waste-inquiry-not-found-error.js'
import { FileStreamUtil } from '../../../../utils/streams/file-stream.util.js'
import { DownloadWasteInquirySummarySapUseCase } from './download-waste-inquiry-summary-sap.use-case.js'

@ApiTags('Waste Inquiry')
@ApiOAuth2([])
@Controller('waste-inquiries/sap/:inquiryNumber/summary')
export class DownloadWasteInquirySummarySapController {
  constructor (
    private readonly useCase: DownloadWasteInquirySummarySapUseCase
  ) { }

  @Get()
  @Permissions(Permission.WASTE_INQUIRY_READ, Permission.WASTE_INQUIRY_MANAGE)
  @ApiOkResponse()
  @ApiNotFoundErrorResponse(
    WasteInquirySummaryDocumentNotFoundError,
    WasteInquiryNotFoundError
  )
  public async downloadWasteInquirySummarySap (
    @Param('inquiryNumber') inquiryNumber: string,
    @Res() res: Response
  ): Promise<void> {
    const url = await this.useCase.execute(inquiryNumber)
    await FileStreamUtil.pipeFileResponse(res, url)
  }
}
