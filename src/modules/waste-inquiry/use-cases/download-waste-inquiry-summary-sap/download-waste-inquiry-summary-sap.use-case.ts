import assert from 'assert'
import { Injectable } from '@nestjs/common'
import { SapGetDocumentsIndexUseCase } from '../../../sap/use-cases/get-documents-index/get-documents-index.use-case.js'
import { WasteInquirySummaryDocumentNotFoundError } from '../../errors/waste-inquiry-summary-document-not-found.error.js'
import { SapGetWasteInquiryIndexUseCase } from '../../../sap/use-cases/get-waste-inquiry-index/get-waste-inquiry-index.use-case.js'
import { SapQuery } from '../../../sap/query/sap-query.js'
import { SapGetWasteInquiryIndexResponse } from '../../../sap/use-cases/get-waste-inquiry-index/get-waste-inquiry-index.response.js'
import { WasteInquiryNotFoundError } from '../../errors/waste-inquiry-not-found-error.js'
import { ArchiveLinkAttribute, SapGetDocumentResponse } from '../../../sap/use-cases/get-documents-index/get-documents-index.response.js'
import { WasteInquirySapValidator } from '../../validators/waste-inquiry-sap.validator.js'
import { SapGetDocumentObject } from '../../../sap/use-cases/get-documents-index/get-document-sap-object.enum.js'

@Injectable()
export class DownloadWasteInquirySummarySapUseCase {
  constructor (
    private readonly sapGetWasteInquiryUseCase: SapGetWasteInquiryIndexUseCase,
    private readonly sapGetDocumentsIndexUseCase: SapGetDocumentsIndexUseCase,
    private readonly validator: WasteInquirySapValidator
  ) {}

  async execute (wasteInquiryId: string): Promise<string> {
    const formattedWasteInquiryId = wasteInquiryId.padStart(10, '0')
    const inquiry = await this.getWasteInquiry(formattedWasteInquiryId)
    await this.validator.validate(inquiry)

    const documents = await this.getDocuments(formattedWasteInquiryId)

    const matchingDocuments = documents.filter((document) => {
      return this.isWasteInquiryDocument(document) && this.hasSummaryDocument(document, inquiry)
    })

    if (matchingDocuments.length === 0) {
      throw new WasteInquirySummaryDocumentNotFoundError({ wasteInquiryId })
    }

    return matchingDocuments[0].ArUrl
  }

  private async getDocuments (wasteInquiryId: string): Promise<SapGetDocumentResponse[]> {
    const documentResponse = await this.sapGetDocumentsIndexUseCase.execute(
      wasteInquiryId,
      SapGetDocumentObject.BUS2030
    )

    if (documentResponse.length === 0) {
      throw new WasteInquirySummaryDocumentNotFoundError({ wasteInquiryId })
    }

    return documentResponse
  }

  private async getWasteInquiry (wasteInquiryId: string): Promise<SapGetWasteInquiryIndexResponse> {
    const sapQuery = new SapQuery<SapGetWasteInquiryIndexResponse>()
      .where('InquiryNumber', wasteInquiryId)
      .addSelect(['InquiryNumber', 'Customer', 'WasteProducer', 'WasteName'])
    const response = await this.sapGetWasteInquiryUseCase.execute(sapQuery)
    const inquiry = response.items.at(0)

    if (inquiry === undefined) {
      throw new WasteInquiryNotFoundError({ inquiryNumber: wasteInquiryId })
    }

    return inquiry
  }

  private isWasteInquiryDocument (response: SapGetDocumentResponse): boolean {
    const wasteInquiryDocumentValue = 'ZCI_SAP'
    return response.ArObject === wasteInquiryDocumentValue
  }

  private hasSummaryDocument (
    documentResponse: SapGetDocumentResponse,
    inquiryResponse: SapGetWasteInquiryIndexResponse
  ): boolean {
    assert(inquiryResponse.WasteName !== undefined, 'WasteName not loaded in')

    return documentResponse.DocToAttributeNav.results.some((nav) => {
      return this.isSummaryDocument(nav, inquiryResponse.WasteName!)
    })
  }

  private isSummaryDocument (attribute: ArchiveLinkAttribute, wasteName: string): boolean {
    const fieldLabelKeyWord = 'description'
    const nonPaddedInquiryNumber = Number(attribute.ObjectId)
    const fieldValueKeyWord = `${nonPaddedInquiryNumber} - ${wasteName}.pdf`
    return attribute.FieldLabel.toLocaleLowerCase() === fieldLabelKeyWord.toLowerCase()
      && attribute.FieldValue.toLocaleLowerCase() === fieldValueKeyWord.toLocaleLowerCase()
  }
}
