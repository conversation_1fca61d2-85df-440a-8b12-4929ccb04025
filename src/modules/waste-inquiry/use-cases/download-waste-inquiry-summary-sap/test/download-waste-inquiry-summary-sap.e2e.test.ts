import { after, before, describe, it, mock } from 'node:test'
import request from 'supertest'
import { expect } from 'expect'
import { HttpStatus } from '@nestjs/common'
import { EndToEndTestSetup } from '../../../../../../test/setup/end-to-end-test-setup.js'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { Permission } from '../../../../permission/permission.enum.js'
import { SapPaginatedResponseBuilder } from '../../../../sap/tests/builders/responses/sap-paginated.response.builder.js'
import { SapGetWasteInquiryIndexResponse } from '../../../../sap/use-cases/get-waste-inquiry-index/get-waste-inquiry-index.response.js'
import { SapGetWasteInquiryIndexUseCase } from '../../../../sap/use-cases/get-waste-inquiry-index/get-waste-inquiry-index.use-case.js'
import { SapGetWasteInquiryIndexResponseBuilder } from '../../../../sap/use-cases/get-waste-inquiry-index/tests/get-waste-inquiry-index.response.builder.js'
import { SapGetDocumentsIndexUseCase } from '../../../../sap/use-cases/get-documents-index/get-documents-index.use-case.js'
import { SapGetDocumentsIndexAttributeResponseBuilder } from '../../../../sap/use-cases/get-documents-index/tests/get-documents-index-attribute-response.builder.js'
import { SapGetDocumentIndexResponseBuilder } from '../../../../sap/use-cases/get-documents-index/tests/get-documents-index.response.builder.js'

describe('DownloadWasteInquirySummarySap - E2E Tests', () => {
  let setup: EndToEndTestSetup

  before(async () => {
    setup = await TestBench.setupEndToEndTest()
  })

  after(async () => await setup.teardown())

  it('downloads the document summary', async () => {
    const user = await setup.authContext.getUser([Permission.WASTE_INQUIRY_READ])

    const inquiryNumber = '10'.padStart(10, '0')
    const wasteInquiry = new SapGetWasteInquiryIndexResponseBuilder()
      .withInquiryNumber(inquiryNumber)
      .build()
    const wasteInquiryMock = mock.method(SapGetWasteInquiryIndexUseCase.prototype, 'execute', () => {
      return new SapPaginatedResponseBuilder<SapGetWasteInquiryIndexResponse>()
        .addItem(wasteInquiry)
        .build()
    })
    const documentMock = mock.method(SapGetDocumentsIndexUseCase.prototype, 'execute', () => {
      return [
        new SapGetDocumentIndexResponseBuilder()
          .withArObject('ZCI_SAP')
          .addDocsToAttributeNavItem(
            new SapGetDocumentsIndexAttributeResponseBuilder()
              .withObjectId(inquiryNumber)
              .withFieldLabel('Description')
              .withFieldValue(`${Number(inquiryNumber)} - ${wasteInquiry.WasteName}.pdf`)
              .build())
          .build()
      ]
    })

    const response = await request(setup.httpServer)
      .get(`/waste-inquiries/sap/${inquiryNumber}/summary`)
      .set('Authorization', `Bearer ${user.token}`)

    wasteInquiryMock.mock.restore()
    documentMock.mock.restore()

    expect(response).toHaveStatus(HttpStatus.OK)
  })
})
