import { before, describe, it, beforeEach } from 'node:test'
import { randomUUID } from 'crypto'
import { createStubInstance, SinonStubbedInstance } from 'sinon'
import { expect } from 'expect'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { DownloadWasteInquirySummarySapUseCase } from '../download-waste-inquiry-summary-sap.use-case.js'
import { SapGetWasteInquiryIndexUseCase } from '../../../../sap/use-cases/get-waste-inquiry-index/get-waste-inquiry-index.use-case.js'
import { SapGetDocumentsIndexUseCase } from '../../../../sap/use-cases/get-documents-index/get-documents-index.use-case.js'
import { WasteInquirySapValidator } from '../../../validators/waste-inquiry-sap.validator.js'
import { WasteInquiryNotFoundError } from '../../../errors/waste-inquiry-not-found-error.js'
import { SapPaginatedResponseBuilder } from '../../../../sap/tests/builders/responses/sap-paginated.response.builder.js'
import { SapGetWasteInquiryIndexResponse } from '../../../../sap/use-cases/get-waste-inquiry-index/get-waste-inquiry-index.response.js'
import { WasteInquirySummaryDocumentNotFoundError } from '../../../errors/waste-inquiry-summary-document-not-found.error.js'
import { SapGetWasteInquiryIndexResponseBuilder } from '../../../../sap/use-cases/get-waste-inquiry-index/tests/get-waste-inquiry-index.response.builder.js'
import { SapGetDocumentIndexResponseBuilder } from '../../../../sap/use-cases/get-documents-index/tests/get-documents-index.response.builder.js'
import { SapGetDocumentsIndexAttributeResponseBuilder } from '../../../../sap/use-cases/get-documents-index/tests/get-documents-index-attribute-response.builder.js'

describe('DownloadWasteInquirySummarySapUseCase - Unit Tests', () => {
  let useCase: DownloadWasteInquirySummarySapUseCase

  let wasteInquiryId: string

  let sapGetWasteInquiryUseCase: SinonStubbedInstance<SapGetWasteInquiryIndexUseCase>
  let sapDocumentIndexUseCase: SinonStubbedInstance<SapGetDocumentsIndexUseCase>
  let validator: SinonStubbedInstance<WasteInquirySapValidator>

  before(() => {
    TestBench.setupUnitTest()

    wasteInquiryId = randomUUID()

    sapGetWasteInquiryUseCase = createStubInstance(SapGetWasteInquiryIndexUseCase)
    sapDocumentIndexUseCase = createStubInstance(SapGetDocumentsIndexUseCase)
    validator = createStubInstance(WasteInquirySapValidator)

    useCase = new DownloadWasteInquirySummarySapUseCase(
      sapGetWasteInquiryUseCase,
      sapDocumentIndexUseCase,
      validator
    )
  })

  beforeEach(() => {
    mockMethods()
  })

  function mockMethods (): void {
    sapGetWasteInquiryUseCase.execute.resolves(
      new SapPaginatedResponseBuilder<SapGetWasteInquiryIndexResponse>().build()
    )
    sapDocumentIndexUseCase.execute.resolves([])
    validator.validate.resolves()
  }

  it('throws WasteInquiryNotFoundError when the waste inquiry does not exists', () => {
    expect(useCase.execute(wasteInquiryId)).rejects.toThrow(WasteInquiryNotFoundError)
  })

  it('throws WasteInquirySummaryNotFoundError when SAP returns no document results', () => {
    sapGetWasteInquiryUseCase.execute.resolves(
      new SapPaginatedResponseBuilder<SapGetWasteInquiryIndexResponse>()
        .addItem(new SapGetWasteInquiryIndexResponseBuilder().build())
        .build()
    )
    expect(useCase.execute(wasteInquiryId))
      .rejects
      .toThrow(WasteInquirySummaryDocumentNotFoundError)
  })

  it('throws WasteInquirySummaryNotFoundError when SAP returns no matching documents because of ArObject mismatch', () => {
    sapGetWasteInquiryUseCase.execute.resolves(
      new SapPaginatedResponseBuilder<SapGetWasteInquiryIndexResponse>()
        .addItem(new SapGetWasteInquiryIndexResponseBuilder().build())
        .build()
    )
    sapDocumentIndexUseCase.execute.resolves([
      new SapGetDocumentIndexResponseBuilder()
        .withArObject('ZCI_SD_ALL')
        .build()
    ])
    expect(useCase.execute(wasteInquiryId))
      .rejects
      .toThrow(WasteInquirySummaryDocumentNotFoundError)
  })

  it('throws WasteInquirySummaryNotFoundError when SAP returns no matching documents because of FieldLabel and FieldValue mismatch', () => {
    sapGetWasteInquiryUseCase.execute.resolves(
      new SapPaginatedResponseBuilder<SapGetWasteInquiryIndexResponse>()
        .addItem(new SapGetWasteInquiryIndexResponseBuilder().build())
        .build()
    )
    sapDocumentIndexUseCase.execute.resolves([
      new SapGetDocumentIndexResponseBuilder()
        .withArObject('ZCI_SAP')
        .addDocsToAttributeNavItem(
          new SapGetDocumentsIndexAttributeResponseBuilder()
            .withFieldLabel('Description')
            .withFieldValue('12312313s')
            .build())
        .addDocsToAttributeNavItem(
          new SapGetDocumentsIndexAttributeResponseBuilder()
            .withFieldLabel('123123')
            .withFieldValue(`${wasteInquiryId} - customer.pdf}`)
            .build())
        .build()

    ])
    expect(useCase.execute(wasteInquiryId))
      .rejects
      .toThrow(WasteInquirySummaryDocumentNotFoundError)
  })

  it('return the url of the matching summary document', async () => {
    const inquiryNumber = '10'.padStart(10, '0')
    const wasteInquiry = new SapGetWasteInquiryIndexResponseBuilder()
      .withInquiryNumber(inquiryNumber)
      .build()
    sapGetWasteInquiryUseCase.execute.resolves(
      new SapPaginatedResponseBuilder<SapGetWasteInquiryIndexResponse>()
        .addItem(wasteInquiry)
        .build()
    )

    const documentResponse = new SapGetDocumentIndexResponseBuilder()
      .withArObject('ZCI_SAP')
      .addDocsToAttributeNavItem(
        new SapGetDocumentsIndexAttributeResponseBuilder()
          .withFieldLabel('Description')
          .withFieldValue(`${Number(wasteInquiryId)} - ${wasteInquiry.WasteName}.pdf`)
          .withObjectId(wasteInquiryId)
          .build())
      .build()

    sapDocumentIndexUseCase.execute.resolves([documentResponse])
    const url = await useCase.execute(wasteInquiryId)

    expect(url).toBe(documentResponse.ArUrl)
  })
})
