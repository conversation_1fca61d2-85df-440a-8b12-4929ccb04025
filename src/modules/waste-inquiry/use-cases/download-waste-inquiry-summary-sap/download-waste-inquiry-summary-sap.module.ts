import { Modu<PERSON> } from '@nestjs/common'
import { SapModule } from '../../../sap/sap.module.js'
import { WasteInquirySapValidator } from '../../validators/waste-inquiry-sap.validator.js'
import { DownloadWasteInquirySummarySapController } from './download-waste-inquiry-summary-sap.controller.js'
import { DownloadWasteInquirySummarySapUseCase } from './download-waste-inquiry-summary-sap.use-case.js'

@Module({
  imports: [
    SapModule
  ],
  controllers: [
    DownloadWasteInquirySummarySapController
  ],
  providers: [
    DownloadWasteInquirySummarySapUseCase,
    WasteInquirySapValidator
  ]
})
export class DownloadWasteInquirySummarySapModule {}
