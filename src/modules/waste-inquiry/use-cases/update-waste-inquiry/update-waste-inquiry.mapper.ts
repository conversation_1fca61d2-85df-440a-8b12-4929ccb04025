import { WasteInquiry } from '../../entities/waste-inquiry.entity.js'
import { UpdateWasteInquiryCommand } from './update-waste-inquiry.command.js'

export class UpdateWasteInquiryMapper {
  static mapToWasteInquiry (command: UpdateWasteInquiryCommand): Partial<WasteInquiry> {
    return {
      customerId: command.customerId,
      wasteProducerId: command.wasteProducerId,
      isUnknownWasteProducer: command.isUnknownWasteProducer,
      pickUpAddressId: command.pickUpAddressId,
      isUnknownPickUpAddress: command.isUnknownPickUpAddress,
      wasteStreamName: command.wasteStreamName,
      wasteStreamDescription: command.wasteStreamDescription,
      ewcLevel1: command.ewcLevel1Name,
      ewcLevel2: command.ewcLevel2Name,
      ewcLevel3: command.ewcLevel3Name,
      stateOfMatter: command.stateOfMatter,
      packagingType: command.packagingType,
      flashpoint: command.flashpoint,
      ph: command.ph,
      specificGravity: command.specificGravity,
      stableTemperatureType: command.stableTemperatureType,
      minStableTemperature: command.minStableTemperature,
      maxStableTemperature: command.maxStableTemperature,
      averageStableTemperature: command.averageStableTemperature,
      noSds: command.noSds,
      noAnalysisReport: command.noAnalysisReport,
      composition: command.composition,
      isSampleAvailable: command.isSampleAvailable,
      selectedLegislationOptions: command.selectedLegislationOptions,
      svhcExtra: command.svhcExtra,
      legislationRemarks: command.legislationRemarks,
      selectedPropertyOptions: command.selectedPropertyOptions,
      propertyRemarks: command.propertyRemarks,
      expectedYearlyVolumeAmount: command.expectedYearlyVolumeAmount,
      expectedYearlyVolumeUnit: command.expectedYearlyVolumeUnit,
      expectedPerCollectionQuantity: command.expectedPerCollectionQuantity,
      expectedPerCollectionUnit: command.expectedPerCollectionUnit,
      dischargeFrequency: command.dischargeFrequency,
      firstCollectionDate: command.firstCollectionDate,
      expectedEndDate: command.expectedEndDate,
      collectionRemarks: command.collectionRemarks,
      isTransportByIndaver: command.isTransportByIndaver,
      isLoadingByIndaver: command.isLoadingByIndaver,
      isRegulatedTransport: command.isRegulatedTransport,
      unNumbers: command.unNumbers,
      packaging: command.packaging,
      transportType: command.transportType,
      loadingType: command.loadingType,
      transportIn: command.transportIn,
      loadingMethod: command.loadingMethod,
      storedIn: command.storedIn,
      containerLoadingType: command.containerLoadingType,
      transportVolumeAmount: command.transportVolumeAmount,
      transportVolumeUnit: command.transportVolumeUnit,
      isTankOwnedByCustomer: command.isTankOwnedByCustomer,
      collectionRequirements: command.collectionRequirements,
      remarks: command.remarks,
      sendCopyToContacts: command.sendCopyToContacts,
      hazardInducer1: command.hazardInducer1,
      hazardInducer2: command.hazardInducer2,
      hazardInducer3: command.hazardInducer3
    }
  }
}
