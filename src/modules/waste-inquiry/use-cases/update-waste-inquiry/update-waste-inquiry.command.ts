import { ApiProperty } from '@nestjs/swagger'
import { IsArray, IsBoolean, IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString, <PERSON>, <PERSON>, <PERSON>, ValidateNested } from 'class-validator'
import { IsAfterTodayString, IsDateWithoutTimeString, IsNullable } from '@wisemen/validators'
import { Type } from 'class-transformer'
import { StateOfMatter } from '../../enums/state-of-matter.enum.js'
import { WastePackagingType } from '../../enums/waste-packaging-type.enum.js'
import { WasteMeasurementUnit } from '../../enums/waste-measurement-unit.enum.js'
import { WasteFlashpointOption } from '../../enums/waste-flashpoint-option.enum.js'
import { WastePhOption } from '../../enums/waste-ph-option.enum.js'
import { StableTemperatureType } from '../../enums/stable-temperature-type.enum.js'
import { CreateFileLinkCommand } from '../../../files/commands/create-file-link.command.js'
import { WasteComposition } from '../../types/waste-composition.type.js'
import { WasteLegislationOption } from '../../enums/waste-legislation-option.enum.js'
import { WastePropertyOption } from '../../enums/waste-property-option.enum.js'
import { WasteDischargeFrequency } from '../../enums/waste-discharge-frequency.enum.js'
import { UnNumber } from '../../types/un-number.type.js'
import { WasteTransportType } from '../../enums/waste-transport-type.enum.js'
import { ContainerLoadingType } from '../../enums/container-loading-type.enum.js'
import { SvhcExtraOption } from '../../enums/svhc-extra-option.enum.js'
import { WasteLoadingType } from '../../enums/waste-loading-type.enum.js'
import { WasteTransportInOption } from '../../enums/waste-transport-in-option.enum.js'
import { WasteLoadingMethod } from '../../enums/waste-loading-method.enum.js'
import { WasteStoredInOption } from '../../enums/waste-stored-in-option.enum.js'
import { Contact } from '../../../contact/types/contact.type.js'
import { RegulatedTransportOption } from '../../enums/regulated-transport-option.enum.js'
import { WastePackaging } from '../../types/waste-packaging.type.js'
import { CollectionRequirementOption } from '../../enums/collection-requirement-option.enum.js'

export class UpdateWasteInquiryCommand {
  @ApiProperty({ type: String, required: false, nullable: true })
  @IsOptional()
  @IsNullable()
  @IsString()
  @IsNotEmpty()
  customerId?: string | null

  @ApiProperty({ type: String, required: false, nullable: true })
  @IsOptional()
  @IsNullable()
  @IsString()
  @IsNotEmpty()
  customerName?: string | null

  @ApiProperty({ type: String, required: false, nullable: true })
  @IsOptional()
  @IsNullable()
  @IsString()
  @IsNotEmpty()
  wasteProducerId?: string | null

  @ApiProperty({ type: String, required: false, nullable: true })
  @IsOptional()
  @IsNullable()
  @IsString()
  @IsNotEmpty()
  wasteProducerName?: string | null

  @ApiProperty({ type: Boolean, required: false })
  @IsOptional()
  @IsBoolean()
  isUnknownWasteProducer?: boolean

  @ApiProperty({ type: String, required: false, nullable: true })
  @IsOptional()
  @IsNullable()
  @IsString()
  @IsNotEmpty()
  pickUpAddressId?: string | null

  @ApiProperty({ type: String, required: false, nullable: true })
  @IsOptional()
  @IsNullable()
  @IsString()
  @IsNotEmpty()
  pickUpAddressName?: string | null

  @ApiProperty({ type: Boolean, required: false })
  @IsOptional()
  @IsBoolean()
  isUnknownPickUpAddress?: boolean

  @ApiProperty({ type: String, required: false, nullable: true, maxLength: 40 })
  @IsOptional()
  @IsNullable()
  @IsString()
  @IsNotEmpty()
  @MaxLength(40)
  wasteStreamName?: string | null

  @ApiProperty({ type: String, required: false, nullable: true, maxLength: 1333 })
  @IsOptional()
  @IsNullable()
  @IsString()
  @IsNotEmpty()
  @MaxLength(1333)
  wasteStreamDescription?: string | null

  @ApiProperty({ type: String, required: false, nullable: true })
  @IsOptional()
  @IsNullable()
  @IsString()
  @IsNotEmpty()
  ewcLevel1Name?: string | null

  @ApiProperty({ type: String, required: false, nullable: true })
  @IsOptional()
  @IsNullable()
  @IsString()
  @IsNotEmpty()
  ewcLevel2Name?: string | null

  @ApiProperty({ type: String, required: false, nullable: true })
  @IsOptional()
  @IsNullable()
  @IsString()
  @IsNotEmpty()
  ewcLevel3Name?: string | null

  @ApiProperty({ enum: StateOfMatter, enumName: 'StateOfMatter', required: false, nullable: true })
  @IsOptional()
  @IsNullable()
  @IsNotEmpty()
  @IsEnum(StateOfMatter)
  stateOfMatter?: StateOfMatter | null

  @ApiProperty({ type: String, enum: WastePackagingType, enumName: 'WastePackagingType', required: false, nullable: true })
  @IsOptional()
  @IsNullable()
  @IsNotEmpty()
  @IsEnum(WastePackagingType)
  packagingType?: WastePackagingType | null

  @ApiProperty({ type: String, enum: WasteFlashpointOption, enumName: 'WasteFlashpointOption', required: false, nullable: true })
  @IsOptional()
  @IsNullable()
  @IsNotEmpty()
  @IsEnum(WasteFlashpointOption)
  flashpoint?: WasteFlashpointOption | null

  @ApiProperty({ type: String, enum: WastePhOption, enumName: 'WastePhOption', required: false, nullable: true })
  @IsOptional()
  @IsNullable()
  @IsNotEmpty()
  @IsEnum(WastePhOption)
  ph?: WastePhOption | null

  @ApiProperty({
    type: Number,
    minimum: -9999999.9,
    maximum: 9999999.9,
    multipleOf: 0.1,
    required: false,
    nullable: true
  })
  @IsOptional()
  @IsNullable()
  @IsNumber({ maxDecimalPlaces: 1 })
  @Min(-9999999.9)
  @Max(9999999.9)
  specificGravity?: number | null

  @ApiProperty({ type: String, enum: StableTemperatureType, enumName: 'StableTemperatureType', required: false, nullable: true })
  @IsOptional()
  @IsNullable()
  @IsNotEmpty()
  @IsEnum(StableTemperatureType)
  stableTemperatureType?: StableTemperatureType | null

  @ApiProperty({
    type: Number,
    minimum: -99999,
    maximum: 99999,
    required: false,
    nullable: true
  })
  @IsOptional()
  @IsNullable()
  @IsNumber()
  @Min(-99999)
  @Max(99999)
  minStableTemperature?: number | null

  @ApiProperty({
    type: Number,
    minimum: -99999,
    maximum: 99999,
    required: false,
    nullable: true
  })
  @IsOptional()
  @IsNullable()
  @IsNumber()
  @Min(-99999)
  @Max(99999)
  maxStableTemperature?: number | null

  @ApiProperty({
    type: Number,
    minimum: -99999,
    maximum: 99999,
    required: false,
    nullable: true
  })
  @IsOptional()
  @IsNullable()
  @IsNumber()
  @Min(-99999)
  @Max(99999)
  averageStableTemperature?: number | null

  @ApiProperty({ type: CreateFileLinkCommand, required: false, isArray: true })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateFileLinkCommand)
  @IsArray()
  sdsFiles?: CreateFileLinkCommand[]

  @ApiProperty({ type: Boolean, required: false })
  @IsOptional()
  @IsBoolean()
  noSds?: boolean

  @ApiProperty({ type: CreateFileLinkCommand, required: false, isArray: true })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateFileLinkCommand)
  @IsArray()
  analysisReportFiles?: CreateFileLinkCommand[]

  @ApiProperty({ type: Boolean, required: false })
  @IsOptional()
  @IsBoolean()
  noAnalysisReport?: boolean

  @ApiProperty({ type: WasteComposition, required: false, isArray: true })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => WasteComposition)
  @IsArray()
  composition?: WasteComposition[]

  @ApiProperty({ type: Boolean, required: false, nullable: true })
  @IsOptional()
  @IsNullable()
  @IsBoolean()
  isSampleAvailable?: boolean | null

  @ApiProperty({
    type: String,
    isArray: true,
    required: false,
    enum: WasteLegislationOption,
    enumName: 'WasteLegislationOption'
  })
  @IsOptional()
  @IsArray()
  @IsEnum(WasteLegislationOption, { each: true })
  selectedLegislationOptions?: WasteLegislationOption[]

  @ApiProperty({ type: String, enum: SvhcExtraOption, enumName: 'SvhcExtraOption', required: false, nullable: true })
  @IsOptional()
  @IsNullable()
  @IsNotEmpty()
  @IsEnum(SvhcExtraOption)
  svhcExtra?: SvhcExtraOption | null

  @ApiProperty({ type: String, required: false, nullable: true, maximum: 1333 })
  @IsOptional()
  @IsNullable()
  @IsString()
  @IsNotEmpty()
  @MaxLength(1333)
  legislationRemarks?: string | null

  @ApiProperty({
    type: String,
    isArray: true,
    required: false,
    enum: WastePropertyOption,
    enumName: 'WastePropertyOption'
  })
  @IsOptional()
  @IsArray()
  @IsEnum(WastePropertyOption, { each: true })
  selectedPropertyOptions?: WastePropertyOption[]

  @ApiProperty({ type: String, required: false, nullable: true, maximum: 1333 })
  @IsOptional()
  @IsNullable()
  @IsString()
  @IsNotEmpty()
  @MaxLength(1333)
  propertyRemarks?: string | null

  @ApiProperty({
    type: Number,
    required: false,
    nullable: true,
    minimum: 0.01,
    maximum: 99999999999.99,
    multipleOf: 0.01
  })
  @IsOptional()
  @IsNullable()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0.01)
  @Max(99999999999.99)
  expectedYearlyVolumeAmount?: number | null

  @ApiProperty({ type: String, enum: WasteMeasurementUnit, enumName: 'WasteMeasurementUnit', required: false, nullable: true })
  @IsOptional()
  @IsNullable()
  @IsNotEmpty()
  @IsEnum(WasteMeasurementUnit)
  expectedYearlyVolumeUnit?: WasteMeasurementUnit | null

  @ApiProperty({
    type: Number,
    required: false,
    nullable: true,
    minimum: 0.01,
    maximum: 99999999999.99,
    multipleOf: 0.01
  })
  @IsOptional()
  @IsNullable()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0.01)
  @Max(99999999999.99)
  expectedPerCollectionQuantity?: number | null

  @ApiProperty({ type: String, enum: WasteMeasurementUnit, enumName: 'WasteMeasurementUnit', required: false, nullable: true })
  @IsOptional()
  @IsNullable()
  @IsNotEmpty()
  @IsEnum(WasteMeasurementUnit)
  expectedPerCollectionUnit?: WasteMeasurementUnit | null

  @ApiProperty({ type: String, enum: WasteDischargeFrequency, enumName: 'WasteDischargeFrequency', required: false, nullable: true })
  @IsOptional()
  @IsNullable()
  @IsNotEmpty()
  @IsEnum(WasteDischargeFrequency)
  dischargeFrequency?: WasteDischargeFrequency | null

  @ApiProperty({ type: String, required: false, nullable: true, format: 'date' })
  @IsOptional()
  @IsNullable()
  @IsDateWithoutTimeString()
  @IsNotEmpty()
  @IsAfterTodayString()
  firstCollectionDate?: string | null

  @ApiProperty({ type: String, required: false, nullable: true, format: 'date' })
  @IsOptional()
  @IsNullable()
  @IsDateWithoutTimeString()
  @IsNotEmpty()
  @IsAfterTodayString()
  expectedEndDate?: string | null

  @ApiProperty({ type: String, required: false, nullable: true, maxLength: 1333 })
  @IsOptional()
  @IsNullable()
  @IsString()
  @IsNotEmpty()
  @MaxLength(1333)
  collectionRemarks?: string | null

  @ApiProperty({ type: Boolean, required: false, nullable: true })
  @IsOptional()
  @IsNullable()
  @IsBoolean()
  isTransportByIndaver?: boolean | null

  @ApiProperty({ type: Boolean, required: false, nullable: true })
  @IsOptional()
  @IsNullable()
  @IsBoolean()
  isLoadingByIndaver?: boolean | null

  @ApiProperty({ type: String, enum: RegulatedTransportOption, enumName: 'RegulatedTransportOption', required: false, nullable: true })
  @IsOptional()
  @IsNullable()
  @IsNotEmpty()
  @IsEnum(RegulatedTransportOption)
  isRegulatedTransport?: RegulatedTransportOption | null

  @ApiProperty({ type: UnNumber, required: false, isArray: true })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => UnNumber)
  @IsArray()
  unNumbers?: UnNumber[]

  @ApiProperty({ type: WastePackaging, required: false, isArray: true })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => WastePackaging)
  @IsArray()
  packaging?: WastePackaging[]

  @ApiProperty({ type: String, enum: WasteTransportType, enumName: 'WasteTransportType', required: false, nullable: true })
  @IsOptional()
  @IsNullable()
  @IsNotEmpty()
  @IsEnum(WasteTransportType)
  transportType?: WasteTransportType | null

  @ApiProperty({ type: String, enum: WasteLoadingType, enumName: 'WasteLoadingType', required: false, nullable: true })
  @IsOptional()
  @IsNullable()
  @IsNotEmpty()
  @IsEnum(WasteLoadingType)
  loadingType?: WasteLoadingType | null

  @ApiProperty({ type: String, enum: WasteTransportInOption, enumName: 'WasteTransportInOption', required: false, nullable: true })
  @IsOptional()
  @IsNullable()
  @IsNotEmpty()
  @IsEnum(WasteTransportInOption)
  transportIn?: WasteTransportInOption | null

  @ApiProperty({ type: String, enum: WasteLoadingMethod, enumName: 'WasteLoadingMethod', required: false, nullable: true })
  @IsOptional()
  @IsNullable()
  @IsNotEmpty()
  @IsEnum(WasteLoadingMethod)
  loadingMethod?: WasteLoadingMethod | null

  @ApiProperty({ type: String, enum: WasteStoredInOption, enumName: 'WasteStoredInOption', required: false, nullable: true })
  @IsOptional()
  @IsNullable()
  @IsNotEmpty()
  @IsEnum(WasteStoredInOption)
  storedIn?: WasteStoredInOption | null

  @ApiProperty({ type: String, enum: ContainerLoadingType, enumName: 'ContainerLoadingType', required: false, nullable: true })
  @IsOptional()
  @IsNullable()
  @IsNotEmpty()
  @IsEnum(ContainerLoadingType)
  containerLoadingType?: ContainerLoadingType | null

  @ApiProperty({
    type: Number,
    required: false,
    nullable: true,
    minimum: 0.01,
    maximum: 99999999999.99,
    multipleOf: 0.01
  })
  @IsOptional()
  @IsNullable()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0.01)
  @Max(99999999999.99)
  transportVolumeAmount?: number | null

  @ApiProperty({ type: String, enum: WasteMeasurementUnit, enumName: 'WasteMeasurementUnit', required: false, nullable: true })
  @IsOptional()
  @IsNullable()
  @IsNotEmpty()
  @IsEnum(WasteMeasurementUnit)
  transportVolumeUnit?: WasteMeasurementUnit | null

  @ApiProperty({ type: Boolean, required: false, nullable: true })
  @IsOptional()
  @IsNullable()
  @IsBoolean()
  isTankOwnedByCustomer?: boolean | null

  @ApiProperty({ type: String, enum: CollectionRequirementOption, enumName: 'CollectionRequirementOption', required: false, nullable: true })
  @IsOptional()
  @IsNullable()
  @IsNotEmpty()
  @IsEnum(CollectionRequirementOption)
  collectionRequirements?: CollectionRequirementOption | null

  @ApiProperty({ type: String, required: false, nullable: true, maxLength: 1333 })
  @IsOptional()
  @IsNullable()
  @IsString()
  @IsNotEmpty()
  @MaxLength(1333)
  remarks?: string | null

  @ApiProperty({ type: Contact, required: false, isArray: true })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => Contact)
  @IsArray()
  sendCopyToContacts?: Contact[]

  @ApiProperty({ type: CreateFileLinkCommand, required: false, isArray: true })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateFileLinkCommand)
  @IsArray()
  additionalFiles?: CreateFileLinkCommand[]

  @ApiProperty({ type: String, nullable: true, required: false })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  hazardInducer1?: string | null

  @ApiProperty({ type: String, nullable: true, required: false })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  hazardInducer2?: string | null

  @ApiProperty({ type: String, nullable: true, required: false })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  hazardInducer3?: string | null
}
