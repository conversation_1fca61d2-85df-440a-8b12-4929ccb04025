import { WasteInquiry } from '../../../entities/waste-inquiry.entity.js'
import { UpdateWasteInquiryCommand } from '../update-waste-inquiry.command.js'

export class UpdateWasteInquiryMapper {
  static mapToMergedWasteInquiry (
    command: UpdateWasteInquiryCommand,
    wasteInquiry: WasteInquiry
  ): Partial<WasteInquiry> {
    return {
      wasteStreamName: command.wasteStreamName !== undefined
        ? command.wasteStreamName
        : wasteInquiry.wasteStreamName,
      wasteStreamDescription: command.wasteStreamDescription !== undefined
        ? command.wasteStreamDescription
        : wasteInquiry.wasteStreamDescription,
      ewcLevel1: command.ewcLevel1Name !== undefined
        ? command.ewcLevel1Name
        : wasteInquiry.ewcLevel1,
      ewcLevel2: command.ewcLevel2Name !== undefined
        ? command.ewcLevel2Name
        : wasteInquiry.ewcLevel2,
      ewcLevel3: command.ewcLevel3Name !== undefined
        ? command.ewcLevel3Name
        : wasteInquiry.ewcLevel3,
      stateOfMatter: command.stateOfMatter !== undefined
        ? command.stateOfMatter
        : wasteInquiry.stateOfMatter,
      packagingType: command.packagingType !== undefined
        ? command.packagingType
        : wasteInquiry.packagingType,
      flashpoint: command.flashpoint !== undefined
        ? command.flashpoint
        : wasteInquiry.flashpoint,
      ph: command.ph !== undefined
        ? command.ph
        : wasteInquiry.ph,
      specificGravity: command.specificGravity !== undefined
        ? command.specificGravity
        : wasteInquiry.specificGravity,
      stableTemperatureType: command.stableTemperatureType !== undefined
        ? command.stableTemperatureType
        : wasteInquiry.stableTemperatureType,
      minStableTemperature: command.minStableTemperature !== undefined
        ? command.minStableTemperature
        : wasteInquiry.minStableTemperature,
      maxStableTemperature: command.maxStableTemperature !== undefined
        ? command.maxStableTemperature
        : wasteInquiry.maxStableTemperature,
      averageStableTemperature: command.averageStableTemperature !== undefined
        ? command.averageStableTemperature
        : wasteInquiry.averageStableTemperature,
      noSds: command.noSds !== undefined
        ? command.noSds
        : wasteInquiry.noSds,
      noAnalysisReport: command.noAnalysisReport !== undefined
        ? command.noAnalysisReport
        : wasteInquiry.noAnalysisReport,
      composition: command.composition !== undefined
        ? command.composition
        : wasteInquiry.composition,
      isSampleAvailable: command.isSampleAvailable !== undefined
        ? command.isSampleAvailable
        : wasteInquiry.isSampleAvailable,
      selectedLegislationOptions: command.selectedLegislationOptions !== undefined
        ? command.selectedLegislationOptions
        : wasteInquiry.selectedLegislationOptions,
      svhcExtra: command.svhcExtra !== undefined
        ? command.svhcExtra
        : wasteInquiry.svhcExtra,
      legislationRemarks: command.legislationRemarks !== undefined
        ? command.legislationRemarks
        : wasteInquiry.legislationRemarks,
      selectedPropertyOptions: command.selectedPropertyOptions !== undefined
        ? command.selectedPropertyOptions
        : wasteInquiry.selectedPropertyOptions,
      propertyRemarks: command.propertyRemarks !== undefined
        ? command.propertyRemarks
        : wasteInquiry.propertyRemarks,
      expectedYearlyVolumeAmount: command.expectedYearlyVolumeAmount !== undefined
        ? command.expectedYearlyVolumeAmount
        : wasteInquiry.expectedYearlyVolumeAmount,
      expectedYearlyVolumeUnit: command.expectedYearlyVolumeUnit !== undefined
        ? command.expectedYearlyVolumeUnit
        : wasteInquiry.expectedYearlyVolumeUnit,
      expectedPerCollectionQuantity: command.expectedPerCollectionQuantity !== undefined
        ? command.expectedPerCollectionQuantity
        : wasteInquiry.expectedPerCollectionQuantity,
      expectedPerCollectionUnit: command.expectedPerCollectionUnit !== undefined
        ? command.expectedPerCollectionUnit
        : wasteInquiry.expectedPerCollectionUnit,
      dischargeFrequency: command.dischargeFrequency !== undefined
        ? command.dischargeFrequency
        : wasteInquiry.dischargeFrequency,
      firstCollectionDate: command.firstCollectionDate !== undefined
        ? command.firstCollectionDate
        : wasteInquiry.firstCollectionDate,
      expectedEndDate: command.expectedEndDate !== undefined
        ? command.expectedEndDate
        : wasteInquiry.expectedEndDate,
      collectionRemarks: command.collectionRemarks !== undefined
        ? command.collectionRemarks
        : wasteInquiry.collectionRemarks,
      isTransportByIndaver: command.isTransportByIndaver !== undefined
        ? command.isTransportByIndaver
        : wasteInquiry.isTransportByIndaver,
      isLoadingByIndaver: command.isLoadingByIndaver !== undefined
        ? command.isLoadingByIndaver
        : wasteInquiry.isLoadingByIndaver,
      isRegulatedTransport: command.isRegulatedTransport !== undefined
        ? command.isRegulatedTransport
        : wasteInquiry.isRegulatedTransport,
      unNumbers: command.unNumbers !== undefined
        ? command.unNumbers
        : wasteInquiry.unNumbers,
      packaging: command.packaging !== undefined
        ? command.packaging
        : wasteInquiry.packaging,
      transportType: command.transportType !== undefined
        ? command.transportType
        : wasteInquiry.transportType,
      containerLoadingType: command.containerLoadingType !== undefined
        ? command.containerLoadingType
        : wasteInquiry.containerLoadingType,
      loadingType: command.loadingType !== undefined
        ? command.loadingType
        : wasteInquiry.loadingType,
      transportIn: command.transportIn !== undefined
        ? command.transportIn
        : wasteInquiry.transportIn,
      loadingMethod: command.loadingMethod !== undefined
        ? command.loadingMethod
        : wasteInquiry.loadingMethod,
      storedIn: command.storedIn !== undefined
        ? command.storedIn
        : wasteInquiry.storedIn,
      transportVolumeAmount: command.transportVolumeAmount !== undefined
        ? command.transportVolumeAmount
        : wasteInquiry.transportVolumeAmount,
      transportVolumeUnit: command.transportVolumeUnit !== undefined
        ? command.transportVolumeUnit
        : wasteInquiry.transportVolumeUnit,
      isTankOwnedByCustomer: command.isTankOwnedByCustomer !== undefined
        ? command.isTankOwnedByCustomer
        : wasteInquiry.isTankOwnedByCustomer,
      collectionRequirements: command.collectionRequirements !== undefined
        ? command.collectionRequirements
        : wasteInquiry.collectionRequirements,
      remarks: command.remarks !== undefined
        ? command.remarks
        : wasteInquiry.remarks,
      sendCopyToContacts: command.sendCopyToContacts !== undefined
        ? command.sendCopyToContacts
        : wasteInquiry.sendCopyToContacts,
      customerId: command.customerId !== undefined
        ? command.customerId
        : wasteInquiry.customerId,
      customerName: command.customerName !== undefined
        ? command.customerName
        : wasteInquiry.customerName,
      wasteProducerId: command.wasteProducerId !== undefined
        ? command.wasteProducerId
        : wasteInquiry.wasteProducerId,
      wasteProducerName: command.wasteProducerName !== undefined
        ? command.wasteProducerName
        : wasteInquiry.wasteProducerName,
      isUnknownWasteProducer: command.isUnknownWasteProducer !== undefined
        ? command.isUnknownWasteProducer
        : wasteInquiry.isUnknownWasteProducer,
      pickUpAddressId: command.pickUpAddressId !== undefined
        ? command.pickUpAddressId
        : wasteInquiry.pickUpAddressId,
      pickUpAddressName: command.pickUpAddressName !== undefined
        ? command.pickUpAddressName
        : wasteInquiry.pickUpAddressName,
      isUnknownPickUpAddress: command.isUnknownPickUpAddress !== undefined
        ? command.isUnknownPickUpAddress
        : wasteInquiry.isUnknownPickUpAddress
    }
  }
}
