import assert from 'assert'
import dayjs from 'dayjs'
import { WasteInquiry } from '../../entities/waste-inquiry.entity.js'
import { mapStableTemperatureTypeToSapValue } from '../../enums/stable-temperature-type.enum.js'
import { mapStateOfMatterToSapValue } from '../../enums/state-of-matter.enum.js'
import { mapSvhcExtraOptionToSapValue } from '../../enums/svhc-extra-option.enum.js'
import { mapWasteDischargeFrequencyToSapValue } from '../../enums/waste-discharge-frequency.enum.js'
import { mapWasteFlashpointOptionToSapValue } from '../../enums/waste-flashpoint-option.enum.js'
import { mapWasteMeasurementUnitToSapValue } from '../../enums/waste-measurement-unit.enum.js'
import { mapWastePackagingTypeToSapValue } from '../../enums/waste-packaging-type.enum.js'
import { mapWastePhOptionToSapValue } from '../../enums/waste-ph-option.enum.js'
import { mapRegulatedTransportOptionToSapValue } from '../../enums/regulated-transport-option.enum.js'
import { mapWasteTransportTypeToSapValue } from '../../enums/waste-transport-type.enum.js'
import { mapContainerLoadingTypeToSapValue } from '../../enums/container-loading-type.enum.js'
import { mapWasteLoadingTypeToSapValue } from '../../enums/waste-loading-type.enum.js'
import { mapWasteStoredInOptionToSapValue } from '../../enums/waste-stored-in-option.enum.js'
import { mapCollectionRequirementOptionToSapValue } from '../../enums/collection-requirement-option.enum.js'
import { mapWasteLegislationOptionToSapValue, WasteLegislationOption } from '../../enums/waste-legislation-option.enum.js'
import { mapWastePropertyOptionToSapValue, WastePropertyOption } from '../../enums/waste-property-option.enum.js'
import { mapWastePackagingOptionToSapValue } from '../../enums/waste-packaging-option.enum.js'
import { mapWasteTransportInToSapValue } from '../../enums/waste-transport-in-option.enum.js'
import { mapWasteLoadingMethodToSapValue } from '../../enums/waste-loading-method.enum.js'
import { mapPackingGroupToSapValue } from '../../enums/packaging-group.enum.js'
import { SapCreateWasteInquiryCommand, SapLoadingType, SapTransportType } from '../../../sap/use-cases/create-waste-inquiry/create-waste-inquiry-sap.command.js'

export class CreateWasteInquirySapMapper {
  static mapSubmittedWasteInquiryToSapCommand (
    wasteInquiry: WasteInquiry,
    customerDefaultSalesOrg: string
  ): SapCreateWasteInquiryCommand {
    assert(wasteInquiry.createdByUser !== undefined)

    return {
      Customer: wasteInquiry.customerId ?? undefined,
      SalesOrganization: customerDefaultSalesOrg,
      WasteProducer: wasteInquiry.wasteProducerId ?? wasteInquiry.customerId ?? undefined,
      PickUpAddress: wasteInquiry.pickUpAddressId ?? wasteInquiry.customerId ?? undefined,
      WasteName: wasteInquiry.wasteStreamName ?? undefined,
      WasteMaterialDescription: wasteInquiry.wasteStreamDescription ?? undefined,
      EwcCustomerLevel1: wasteInquiry.ewcLevel1 ?? undefined,
      EwcCustomerLevel2: wasteInquiry.ewcLevel2 ?? undefined,
      EwcCustomerLevel3: wasteInquiry.ewcLevel3 ?? undefined,
      StateOfMatter: wasteInquiry.stateOfMatter !== null
        ? mapStateOfMatterToSapValue(wasteInquiry.stateOfMatter)
        : undefined,
      WasteType: wasteInquiry.packagingType !== null
        ? mapWastePackagingTypeToSapValue(wasteInquiry.packagingType)
        : undefined,
      Flashpoint: wasteInquiry.flashpoint !== null
        ? mapWasteFlashpointOptionToSapValue(wasteInquiry.flashpoint)
        : undefined,
      Ph: wasteInquiry.ph !== null
        ? mapWastePhOptionToSapValue(wasteInquiry.ph)
        : undefined,
      Gravity: wasteInquiry.specificGravity ?? undefined,
      Temperature: wasteInquiry.stableTemperatureType !== null
        ? mapStableTemperatureTypeToSapValue(wasteInquiry.stableTemperatureType)
        : undefined,
      MinTemperature: wasteInquiry.minStableTemperature
        ?? wasteInquiry.averageStableTemperature
        ?? undefined,
      MaxTemperature: wasteInquiry.maxStableTemperature
        ?? wasteInquiry.averageStableTemperature
        ?? undefined,
      Sds: wasteInquiry.noSds === false,
      AnalysisReport: wasteInquiry.noAnalysisReport === false,
      _component: wasteInquiry.composition.map(composition => ({
        Component: composition.name ?? undefined,
        MinWeight: composition.minWeight ?? undefined,
        MaxWeight: composition.maxWeight ?? undefined
      })),
      Sample: wasteInquiry.isSampleAvailable ?? undefined,
      _wasteSubjectLegislation: Object.values(WasteLegislationOption).map(option => ({
        WasteSubjectCode: mapWasteLegislationOptionToSapValue(option),
        WasteSubjectCodeValid: wasteInquiry.selectedLegislationOptions.includes(option)
      })),
      TypeOfSvhc: wasteInquiry.svhcExtra !== null
        ? mapSvhcExtraOptionToSapValue(wasteInquiry.svhcExtra)[0]
        : undefined,
      PersistentSubstance: wasteInquiry.svhcExtra !== null
        ? (mapSvhcExtraOptionToSapValue(wasteInquiry.svhcExtra)[1] ?? '1')
        : undefined,
      CommentSubjectLegislation: wasteInquiry.legislationRemarks ?? undefined,
      _wasteSubjectHazard: Object.values(WastePropertyOption).map(option => ({
        WasteSubjectCode: mapWastePropertyOptionToSapValue(option),
        WasteSubjectCodeValid: wasteInquiry.selectedPropertyOptions.includes(option)
      })),
      CommentSubjectHazard: wasteInquiry.propertyRemarks ?? undefined,
      QuantityYear: wasteInquiry.expectedYearlyVolumeAmount ?? undefined,
      QuantityYearUom: wasteInquiry.expectedYearlyVolumeUnit !== null
        ? mapWasteMeasurementUnitToSapValue(wasteInquiry.expectedYearlyVolumeUnit)
        : undefined,
      CollectionQuantity: wasteInquiry.expectedPerCollectionQuantity ?? undefined,
      CollectionUom: wasteInquiry.expectedPerCollectionUnit !== null
        ? mapWasteMeasurementUnitToSapValue(wasteInquiry.expectedPerCollectionUnit)
        : undefined,
      FrequencyDischarge: wasteInquiry.dischargeFrequency !== null
        ? mapWasteDischargeFrequencyToSapValue(wasteInquiry.dischargeFrequency)
        : undefined,
      CollectionDate: wasteInquiry.firstCollectionDate !== null
        ? dayjs(wasteInquiry.firstCollectionDate).format('YYYY-MM-DD')
        : undefined,
      ExpectedEnd: wasteInquiry.expectedEndDate !== null
        ? dayjs(wasteInquiry.expectedEndDate).format('YYYY-MM-DD')
        : undefined,
      InformationDelivery: wasteInquiry.collectionRemarks ?? undefined,
      TransportArranged: wasteInquiry.isTransportByIndaver ?? undefined,
      LoadingByIndaver: wasteInquiry.isLoadingByIndaver ?? undefined,
      TransportRegulated: wasteInquiry.isRegulatedTransport !== null
        ? mapRegulatedTransportOptionToSapValue(wasteInquiry.isRegulatedTransport)
        : undefined,
      _unNumber: wasteInquiry.unNumbers.map(unNumber => ({
        UnNumber: unNumber.unNumber ?? undefined,
        UnNumberPackGroup: unNumber.packingGroup !== null
          ? mapPackingGroupToSapValue(unNumber.packingGroup)
          : undefined
      })),
      _packaging: wasteInquiry.packaging.map(packaging => ({
        TypePackaging: packaging.type !== null
          ? mapWastePackagingOptionToSapValue(packaging.type)
          : undefined,
        SizePackaging: packaging.size ?? undefined,
        WeightPiece: packaging.weightPerPieceValue ?? undefined,
        WeightPieceUom: 'KG',
        InnerPackaging: packaging.hasInnerPackaging ?? undefined,
        CommentPackaging: packaging.remarks ?? undefined
      })),
      TransportType: this.getTransportType(wasteInquiry),
      LoadingType: this.getLoadingType(wasteInquiry),
      LoadingMethod: wasteInquiry.loadingMethod !== null
        ? mapWasteLoadingMethodToSapValue(wasteInquiry.loadingMethod)
        : undefined,
      StoredIn: wasteInquiry.storedIn !== null
        ? mapWasteStoredInOptionToSapValue(wasteInquiry.storedIn)
        : undefined,
      Volume: wasteInquiry.transportVolumeAmount ?? undefined,
      VolumeUom: wasteInquiry.transportVolumeUnit !== null
        ? mapWasteMeasurementUnitToSapValue(wasteInquiry.transportVolumeUnit)
        : undefined,
      TankOwnedCustomer: wasteInquiry.isTankOwnedByCustomer ?? undefined,
      RequiredForCollection: wasteInquiry.collectionRequirements !== null
        ? mapCollectionRequirementOptionToSapValue(wasteInquiry.collectionRequirements)
        : undefined,
      RequestDescription: wasteInquiry.remarks ?? undefined,
      _contact: wasteInquiry.sendCopyToContacts.map(contact => ({
        FirstName: contact.firstName ?? undefined,
        LastName: contact.lastName ?? undefined,
        Email: contact.email ?? undefined
      })),
      FirstNameCustomerZone: wasteInquiry.createdByUser.firstName ?? undefined,
      LastNameCustomerZone: wasteInquiry.createdByUser?.lastName ?? undefined,
      EmailCustomerZone: wasteInquiry.createdByUser.email,
      NewRequestStatus: '2',
      RequestCategory: '4'
    }
  }

  static getTransportType (wasteInquiry: WasteInquiry): SapTransportType | undefined {
    if (wasteInquiry.transportType !== null) {
      return mapWasteTransportTypeToSapValue(wasteInquiry.transportType)
    }
    if (wasteInquiry.transportIn !== null) {
      return mapWasteTransportInToSapValue(wasteInquiry.transportIn)
    }

    return undefined
  }

  static getLoadingType (wasteInquiry: WasteInquiry): SapLoadingType | undefined {
    if (wasteInquiry.containerLoadingType !== null) {
      return mapContainerLoadingTypeToSapValue(wasteInquiry.containerLoadingType)
    }
    if (wasteInquiry.loadingType !== null) {
      return mapWasteLoadingTypeToSapValue(wasteInquiry.loadingType)
    }

    return undefined
  }
}
