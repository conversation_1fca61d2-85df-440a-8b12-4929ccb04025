import { afterEach, before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import Sinon, { SinonStubbedInstance, createStubInstance } from 'sinon'
import { Repository } from 'typeorm'
import { randNumber } from '@ngneat/falso'
import { expect } from 'expect'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { WasteInquiry } from '../../../../waste-inquiry/entities/waste-inquiry.entity.js'
import { CreateWasteInquirySapUseCase } from '../create-waste-inquiry-sap.use-case.js'
import { User } from '../../../../../app/users/entities/user.entity.js'
import { UserEntityBuilder } from '../../../../../app/users/tests/user-entity.builder.js'
import { ValidWasteInquiryEntityBuilder } from '../../../tests/valid-waste-inquiry-entity.builder.js'
import { SapCreateWasteInquiryUseCase } from '../../../../sap/use-cases/create-waste-inquiry/create-waste-inquiry.use-case.js'
import { SapCreateWasteInquiryResponseBuilder } from '../../../../sap/use-cases/create-waste-inquiry/tests/create-waste-inquiry.response.builder.js'
import { GetCustomerDefaultSalesOrganisationIdUseCase } from '../../../../customer/use-cases/get-customer-default-sales-organisation-id/get-customer-default-sales-organisation-id.use-case.js'
import { FileLinkService } from '../../../../files/services/file-link.service.js'
import { SapUploadAndCreateDocumentUseCase } from '../../../../sap/use-cases/upload-and-create-document/upload-and-create-document.use-case.js'
import { SapFile } from '../../../../sap-files/entities/sap-file.entity.js'
import { SapFileEntityBuilder } from '../../../../sap-files/tests/sap-files-entity.builder.js'

describe('Create waste inquiry SAP use-case unit test', () => {
  let useCase: CreateWasteInquirySapUseCase

  let user: User
  let wasteInquiry: WasteInquiry

  let sapFileRepository: SinonStubbedInstance<Repository<SapFile>>
  let wasteInquiryRepository: SinonStubbedInstance<Repository<WasteInquiry>>
  let salesOrganisationUseCase: SinonStubbedInstance<GetCustomerDefaultSalesOrganisationIdUseCase>
  let sapCreateWasteInquiryUseCase: SinonStubbedInstance<SapCreateWasteInquiryUseCase>
  let fileLinkService: SinonStubbedInstance<FileLinkService>
  let sapUploadAndCreateDocumentUseCase: SinonStubbedInstance<SapUploadAndCreateDocumentUseCase>

  before(() => {
    TestBench.setupUnitTest()

    user = new UserEntityBuilder().build()
    wasteInquiry = new ValidWasteInquiryEntityBuilder()
      .createdBy(user.uuid)
      .build()
    wasteInquiry.createdByUser = user

    sapFileRepository = createStubInstance<Repository<SapFile>>(
      Repository<SapFile>
    )
    wasteInquiryRepository = createStubInstance<Repository<WasteInquiry>>(
      Repository<WasteInquiry>
    )
    salesOrganisationUseCase = createStubInstance(GetCustomerDefaultSalesOrganisationIdUseCase)
    fileLinkService = createStubInstance(FileLinkService)
    sapCreateWasteInquiryUseCase = createStubInstance(SapCreateWasteInquiryUseCase)
    sapUploadAndCreateDocumentUseCase = createStubInstance(SapUploadAndCreateDocumentUseCase)

    useCase = new CreateWasteInquirySapUseCase(
      sapFileRepository,
      wasteInquiryRepository,
      salesOrganisationUseCase,
      fileLinkService,
      sapCreateWasteInquiryUseCase,
      sapUploadAndCreateDocumentUseCase
    )

    mockMethods()
  })

  afterEach(() => {
    Sinon.resetHistory()
    mockMethods()
  })

  function mockMethods () {
    sapFileRepository.create.resolves(
      new SapFileEntityBuilder().build()
    )
    sapFileRepository.save.resolves()
    wasteInquiryRepository.findOneOrFail.resolves(wasteInquiry)
    salesOrganisationUseCase.getOrganisationIdOrFail.resolves(randomUUID())
    fileLinkService.loadFileLinksGroupedByEntityParts.resolves([])
    sapCreateWasteInquiryUseCase.execute.resolves(
      new SapCreateWasteInquiryResponseBuilder().build()
    )
    sapUploadAndCreateDocumentUseCase.execute.resolves()
  }

  it('Returns inquiry number', async () => {
    const inquiryNumber = randNumber({ min: 10000000, max: 99999999 }).toString()

    sapCreateWasteInquiryUseCase.execute.resolves(
      new SapCreateWasteInquiryResponseBuilder()
        .withInquiryNumber(inquiryNumber)
        .build()
    )

    const response = await useCase.execute(wasteInquiry.uuid)

    expect(response).toEqual(inquiryNumber)
  })
})
