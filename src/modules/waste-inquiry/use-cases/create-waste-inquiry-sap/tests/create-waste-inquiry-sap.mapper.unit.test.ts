import { afterEach, before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import Sinon from 'sinon'
import { expect } from 'expect'
import dayjs from 'dayjs'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { WasteInquiry } from '../../../../waste-inquiry/entities/waste-inquiry.entity.js'
import { CreateWasteInquirySapMapper } from '../create-waste-inquiry-sap.mapper.js'
import { ValidWasteInquiryEntityBuilder } from '../../../tests/valid-waste-inquiry-entity.builder.js'
import { UserEntityBuilder } from '../../../../../app/users/tests/user-entity.builder.js'
import { User } from '../../../../../app/users/entities/user.entity.js'
import { mapStateOfMatterToSapValue } from '../../../enums/state-of-matter.enum.js'
import { mapWastePackagingTypeToSapValue } from '../../../enums/waste-packaging-type.enum.js'
import { mapWasteFlashpointOptionToSapValue } from '../../../enums/waste-flashpoint-option.enum.js'
import { mapWastePhOptionToSapValue } from '../../../enums/waste-ph-option.enum.js'
import { mapStableTemperatureTypeToSapValue } from '../../../enums/stable-temperature-type.enum.js'
import { mapWasteLegislationOptionToSapValue, WasteLegislationOption } from '../../../enums/waste-legislation-option.enum.js'
import { mapWastePropertyOptionToSapValue, WastePropertyOption } from '../../../enums/waste-property-option.enum.js'
import { mapWasteMeasurementUnitToSapValue } from '../../../enums/waste-measurement-unit.enum.js'
import { mapWasteDischargeFrequencyToSapValue } from '../../../enums/waste-discharge-frequency.enum.js'
import { mapRegulatedTransportOptionToSapValue } from '../../../enums/regulated-transport-option.enum.js'
import { mapWastePackagingOptionToSapValue } from '../../../enums/waste-packaging-option.enum.js'
import { mapContainerLoadingTypeToSapValue } from '../../../enums/container-loading-type.enum.js'
import { mapWasteTransportTypeToSapValue } from '../../../enums/waste-transport-type.enum.js'
import { SapLanguage } from '../../../../sap/enums/sap-language.enum.js'

describe('Create waste inquiry SAP mapper unit test', () => {
  let user: User
  let wasteInquiry: WasteInquiry
  let customerDefaultSalesOrg: string

  before(() => {
    TestBench.setupUnitTest()

    user = new UserEntityBuilder().build()
    wasteInquiry = new ValidWasteInquiryEntityBuilder()
      .createdBy(user.uuid)
      .build()
    wasteInquiry.createdByUser = user
    customerDefaultSalesOrg = randomUUID()
  })

  afterEach(() => {
    Sinon.resetHistory()
  })

  it('maps a valid waste inquiry', () => {
    const result = CreateWasteInquirySapMapper.mapSubmittedWasteInquiryToSapCommand(
      wasteInquiry,
      customerDefaultSalesOrg
    )

    expect(result).toStrictEqual({
      Customer: wasteInquiry.customerId,
      SalesOrganization: customerDefaultSalesOrg,
      WasteProducer: wasteInquiry.wasteProducerId,
      PickUpAddress: wasteInquiry.pickUpAddressId,
      WasteName: wasteInquiry.wasteStreamName,
      WasteMaterialDescription: wasteInquiry.wasteStreamDescription,
      EwcCustomerLevel1: wasteInquiry.ewcLevel1,
      EwcCustomerLevel2: wasteInquiry.ewcLevel2,
      EwcCustomerLevel3: wasteInquiry.ewcLevel3,
      StateOfMatter: mapStateOfMatterToSapValue(wasteInquiry.stateOfMatter!),
      WasteType: mapWastePackagingTypeToSapValue(wasteInquiry.packagingType!),
      Flashpoint: mapWasteFlashpointOptionToSapValue(wasteInquiry.flashpoint!),
      Ph: mapWastePhOptionToSapValue(wasteInquiry.ph!),
      Gravity: undefined,
      Temperature: mapStableTemperatureTypeToSapValue(wasteInquiry.stableTemperatureType!),
      MinTemperature: undefined,
      MaxTemperature: undefined,
      Sds: false,
      AnalysisReport: false,
      _component: wasteInquiry.composition.map(composition => ({
        Component: composition.name,
        MinWeight: composition.minWeight,
        MaxWeight: composition.maxWeight
      })),
      Sample: wasteInquiry.isSampleAvailable,
      _wasteSubjectLegislation: Object.values(WasteLegislationOption).map(option => ({
        WasteSubjectCode: mapWasteLegislationOptionToSapValue(option),
        WasteSubjectCodeValid: wasteInquiry.selectedLegislationOptions.includes(option)
      })),
      TypeOfSvhc: undefined,
      PersistentSubstance: undefined,
      CommentSubjectLegislation: undefined,
      _wasteSubjectHazard: Object.values(WastePropertyOption).map(option => ({
        WasteSubjectCode: mapWastePropertyOptionToSapValue(option),
        WasteSubjectCodeValid: wasteInquiry.selectedPropertyOptions.includes(option)
      })),
      CommentSubjectHazard: undefined,
      QuantityYear: wasteInquiry.expectedYearlyVolumeAmount,
      QuantityYearUom: mapWasteMeasurementUnitToSapValue(wasteInquiry.expectedYearlyVolumeUnit!),
      CollectionQuantity: wasteInquiry.expectedPerCollectionQuantity,
      CollectionUom: mapWasteMeasurementUnitToSapValue(wasteInquiry.expectedPerCollectionUnit!),
      FrequencyDischarge: mapWasteDischargeFrequencyToSapValue(wasteInquiry.dischargeFrequency!),
      CollectionDate: wasteInquiry.firstCollectionDate,
      InformationDelivery: undefined,
      TransportArranged: wasteInquiry.isTransportByIndaver,
      LoadingByIndaver: undefined,
      TransportRegulated: mapRegulatedTransportOptionToSapValue(wasteInquiry.isRegulatedTransport!),
      _unNumber: wasteInquiry.unNumbers.map(unNumber => ({
        UnNumber: unNumber.unNumber,
        UnNumberPackGroup: unNumber.packingGroup
      })),
      _packaging: wasteInquiry.packaging.map(packaging => ({
        TypePackaging: mapWastePackagingOptionToSapValue(packaging.type!),
        SizePackaging: packaging.size,
        WeightPiece: packaging.weightPerPieceValue,
        WeightPieceUom: 'KG',
        InnerPackaging: packaging.hasInnerPackaging,
        CommentPackaging: packaging.remarks
      })),
      TransportType: mapWasteTransportTypeToSapValue(wasteInquiry.transportType!),
      LoadingType: mapContainerLoadingTypeToSapValue(wasteInquiry.containerLoadingType!),
      LoadingMethod: undefined,
      StoredIn: undefined,
      Volume: wasteInquiry.transportVolumeAmount,
      VolumeUom: mapWasteMeasurementUnitToSapValue(wasteInquiry.transportVolumeUnit!),
      TankOwnedCustomer: undefined,
      RequiredForCollection: undefined,
      RequestDescription: undefined,
      _contact: wasteInquiry.sendCopyToContacts.map(contact => ({
        FirstName: contact.firstName,
        LastName: contact.lastName,
        Email: contact.email
      })),
      FirstNameCustomerZone: wasteInquiry.createdByUser!.firstName,
      LastNameCustomerZone: wasteInquiry.createdByUser!.lastName,
      EmailCustomerZone: wasteInquiry.createdByUser!.email,
      NewRequestStatus: '2',
      RequestCategory: '4',
      GeneralRemarks: wasteInquiry.remarks !== null
        ? wasteInquiry.remarks
        : undefined,
      ProjectStart: wasteInquiry.firstCollectionDate !== null
        ? dayjs(wasteInquiry.firstCollectionDate).format('YYYY-MM-DD')
        : undefined,
      ProjectEnd: wasteInquiry.expectedEndDate !== null
        ? dayjs(wasteInquiry.expectedEndDate).format('YYYY-MM-DD')
        : undefined,
      LanguageCustomerZone: SapLanguage.EN,
      HazardInducer1: wasteInquiry.hazardInducer1,
      HazardInducer2: wasteInquiry.hazardInducer2,
      HazardInducer3: wasteInquiry.hazardInducer3
    })
  })

  it('uses the customerId when no waste producer is set', () => {
    const wasteInquiryWithNoWasteProducer = { ...wasteInquiry, wasteProducerId: null }

    const result = CreateWasteInquirySapMapper.mapSubmittedWasteInquiryToSapCommand(
      wasteInquiryWithNoWasteProducer,
      customerDefaultSalesOrg
    )

    expect(result.WasteProducer).toBe(wasteInquiry.customerId)
  })

  it('uses the customerId when no pick-up address is set', () => {
    const wasteInquiryWithNoWasteProducer = { ...wasteInquiry, pickUpAddressId: null }

    const result = CreateWasteInquirySapMapper.mapSubmittedWasteInquiryToSapCommand(
      wasteInquiryWithNoWasteProducer,
      customerDefaultSalesOrg
    )

    expect(result.PickUpAddress).toBe(wasteInquiry.customerId)
  })
})
