TZ=UTC
NODE_ENV=test

FRONTEND_URL=https://template-project.development.appwi.se
BACKEND_URL=

DATABASE_SSL=false
DATABASE_URI=postgresql://postgres:password@localhost:5432/test_db
DATABASE_REPLICA_URIS=

REDIS_URL=redis://localhost:6379

TYPESENSE_HOST=127.0.0.1
TYPESENSE_KEY=api_key

NATS_HOST=127.0.0.1
NATS_PORT=4222

AZURE_BLOB_ENDPOINT=http://localhost:10000
AZURE_BLOB_ACCOUNT_NAME=devstoreaccount1
AZURE_BLOB_ACCOUNT_KEY=secret
AZURE_BLOB_CONTAINER_NAME=local

MAIL_FROM_NAME=
SCW_PROJECT_ID=
SCW_REGION=
SCW_API_KEY=

SENTRY_DSN=
SENTRY_TRACE_SAMPLE_RATE=
SENTRY_ERROR_SAMPLE_RATE=

SIGNOZ_LOG_ENDPOINT=
SIGNOZ_TRACE_ENDPOINT=
SIGNOZ_INGESTION_KEY=
SIGNOZ_AUTH=
SIGNOZ_KEY=

AUTH_JWKS_ENDPOINT=https://zitadel.internal.appwi.se/oauth/v2/keys
AUTH_ISSUER=https://zitadel.internal.appwi.se
AUTH_PROJECT_ID=284257737964130471

OPEN_API_SCOPES=urn:zitadel:iam:org:id:305531015504349194 users
OPEN_API_SERVERS=https://vue.project-template.development.appwi.se,https://vue.project-template.test.appwi.se,https://vue.project-template.staging.appwi.se,https://vue.project-template.production.appwi.se
OPEN_API_OPENID_CONFIGURATION_URL=https://zitadel.internal.appwi.se/.well-known/openid-configuration

TWILIO_ACCOUNT_SID=
TWILIO_AUTH_TOKEN=
TWILIO_PHONE_NUMBER=

ONESIGNAL_API_KEY=
ONESIGNAL_APP_ID=
ONESIGNAL_PRIVATE_KEY=
ONESIGNAL_PASSPHRASE=
ONESIGNAL_PUBLIC_KEY=

SAP_BASE_URL=https://services.odata.org/OData
SAP_USERNAME=username
SAP_PASSWORD=password
SAP_CLIENT=140

FLEXMAIL_BASE_URL=https://api.flexmail.eu
FLEXMAIL_ACCOUNT_ID=5748
FLEXMAIL_ACCESS_TOKEN=secret
FLEXMAIL_FORM_ID=0

ZITADEL_BASE_URL=
ZITADEL_ORGANISATION_ID=
ZITADEL_API_TOKEN=

MICROSOFT_GRAPH_TENANT_ID=tenant-id
MICROSOFT_GRAPH_CLIENT_ID=client-id
MICROSOFT_GRAPH_CLIENT_SECRET=secret
MICROSOFT_ENTRA_SERVICE_PRINCIPAL_ID=service-principal-id


SHAREPOINT_DOCUMENT_BASE_URL=https://ind-cz-sharepointfileconnect-uat.azurewebsites.net
SHAREPOINT_DOCUMENT_OAUTH_URL=
SHAREPOINT_DOCUMENT_CLIENT_ID=
SHAREPOINT_DOCUMENT_CLIENT_SECRET=
SHAREPOINT_DOCUMENT_SCOPE=api://ind-cz-sharepointfileconnect/.default
SHAREPOINT_DOCUMENT_CERTIFICATE=
SHAREPOINT_DOCUMENT_PRIVATE_KEY=

SHAREPOINT_BASE_URL=
SHAREPOINT_SUBSCRIPTION_KEY=
