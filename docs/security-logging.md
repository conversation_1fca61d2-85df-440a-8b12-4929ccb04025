# Security Logging Implementation Guide

## Overview

This document describes the security logging implementation that has been added to the application to enhance security monitoring and audit capabilities.

## Components

### 1. SecurityLoggerService

The `SecurityLoggerService` provides structured logging for security events throughout the application.

**Location:** `src/utils/opentelemetry/modules/security-logger.service.ts`

### 2. Security Event Types

The following security events are automatically logged:

- `AUTHENTICATION_SUCCESS` - Successful user authentication
- `AUTHENTICATION_FAILED` - Failed authentication attempts
- `AUTHORIZATION_DENIED` - Permission denied events
- `IMPERSONATION_STARTED` - User impersonation initiated
- `IMPERSONATION_FAILED` - Failed impersonation attempts
- `FILE_ACCESS` - File access events
- `FILE_UPLOAD` - File upload events
- `FILE_DOWNLOAD` - File download events
- `SENSITIVE_DATA_ACCESS` - Access to sensitive data
- `INVALID_TOKEN` - Invalid JWT token attempts
- `WEBSOCKET_AUTH_FAILED` - WebSocket authentication failures

## Automatic Logging

### Authentication Events

Authentication events are automatically logged in:
- `src/modules/auth/middleware/auth.middleware.ts`

```typescript
// Successful authentication
this.securityLogger.logAuthenticationSuccess(
  content.uuid,
  content.azureEntraUpn,
  req.ip,
  req.headers['user-agent']
)

// Failed authentication
this.securityLogger.logAuthenticationFailure(
  error.message,
  req.ip,
  req.headers['user-agent']
)
```

### Authorization Events

Authorization failures are automatically logged in:
- `src/modules/permission/guards/permission.guard.ts`

### Impersonation Events

Impersonation events are automatically logged in:
- `src/modules/auth/middleware/impersonation.middleware.ts`

### File Access Events

File access events are logged in file controllers:
- `src/modules/files/use-cases/create-file/create-file.controller.ts`

### WebSocket Events

WebSocket authentication failures are logged in:
- `src/modules/websocket/websocket-adapter.ts`

## Manual Logging

### Using the SensitiveData Decorator

For endpoints that access sensitive data, use the `@SensitiveData` decorator:

```typescript
import { SensitiveData } from '../auth/interceptors/security-logging.interceptor.js'

@Controller('customers')
export class CustomerController {
  @Get(':uuid')
  @SensitiveData({ resourceType: 'customer', action: 'read' })
  async getCustomer(@Param('uuid') uuid: string) {
    // This will automatically log sensitive data access
    return await this.customerService.findOne(uuid)
  }
}
```

### Direct Logging

You can also log security events directly:

```typescript
constructor(
  private readonly securityLogger: SecurityLoggerService
) {}

async someMethod() {
  // Log sensitive data access
  this.securityLogger.logSensitiveDataAccess(
    userUuid,
    'customer_data',
    customerId,
    'read',
    ipAddress
  )
}
```

## Log Structure

Security logs follow this structure:

```json
{
  "context": "security",
  "body": {
    "event": "authentication_success",
    "success": true,
    "userUuid": "user-uuid",
    "azureEntraUpn": "<EMAIL>",
    "endpoint": "CustomerController.getCustomer",
    "resourceType": "customer",
    "resourceId": "customer-uuid",
    "action": "read",
    "timestamp": "2024-01-01T12:00:00.000Z"
  },
  "attributes": {
    "ipAddress": "***********",
    "userAgent": "Mozilla/5.0...",
    "env": "production",
    "host": "api-server"
  }
}
```

## Monitoring and Alerting

### Critical Events to Monitor

1. **Multiple Authentication Failures** - Potential brute force attacks
2. **Authorization Denials** - Potential privilege escalation attempts
3. **Failed Impersonation Attempts** - Potential unauthorized access
4. **Unusual File Access Patterns** - Potential data exfiltration
5. **WebSocket Authentication Failures** - Potential connection abuse

### Recommended Alerts

Set up alerts for:
- More than 5 authentication failures from the same IP in 5 minutes
- Authorization denials for admin-level permissions
- Any failed impersonation attempts
- Bulk file downloads by a single user
- WebSocket authentication failures exceeding normal patterns

## Integration with Existing Systems

The security logging integrates with:
- **OpenTelemetry** - For structured logging and trace correlation
- **Sentry** - For error tracking and alerting
- **SignOZ** - For log aggregation and analysis

## Best Practices

1. **Always log security events** - Use the provided methods for consistency
2. **Include context** - IP addresses, user agents, and trace IDs help with investigation
3. **Sanitize sensitive data** - Never log passwords, tokens, or personal data
4. **Monitor log volume** - Ensure logging doesn't impact performance
5. **Regular review** - Periodically review logs for security patterns

## Future Enhancements

Consider implementing:
- Anomaly detection based on user behavior patterns
- Integration with SIEM systems
- Real-time security dashboards
- Automated response to security events
- Compliance reporting features
